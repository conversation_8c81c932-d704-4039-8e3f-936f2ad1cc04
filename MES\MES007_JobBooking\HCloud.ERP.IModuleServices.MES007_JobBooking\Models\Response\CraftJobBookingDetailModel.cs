﻿using System;

namespace HCloud.ERP.IModuleServices.MES007_JobBooking.Models
{
    /// <summary>
    /// 工艺加工任务model
    /// </summary>
    [MessagePack.MessagePackObject]
    public class CraftJobBookingDetailModel:T_MESD_CRAFT_JOB_BOOKING
    {
        /// <summary>
        /// 工位编号
        /// </summary>
        public string FSTATION_CODE { get; set; }
        /// <summary>
        /// 工位名称
        /// </summary>
        public string FSTATION_NAME { get; set; }

        /// <summary>
        /// 车间名称
        /// </summary>
        public string FWORK_SHOP_NAME { get; set; }
        /// <summary>
        /// 车间编号
        /// </summary>
        public string FWORK_SHOP_CODE { get; set; }


        /// <summary>
        /// 工艺编号
        /// </summary>
        public string FCRAFT_CODE { get; set; }

        /// <summary>
        /// 工艺名称
        /// </summary>
        public string FCRAFT_NAME { get; set; }



        /// <summary>
        /// 工单编号
        /// </summary>
        public string FWORK_ORDER_NO { get; set; }

     

        /// <summary>
        /// 订单编号
        /// </summary>
        public string FSALE_ORDER_NO { get; set; }



        /// <summary>
        /// 人员编号
        /// </summary>
        public string FEMP_CODE { get; set; }

          /// <summary>
          /// 排程任务编号
          /// </summary>
        public string FCRAFT_SCHEDULE_NO { get; set; }


        /// <summary>
        /// 物料编号
        /// </summary>
        public string FMATERIAL_CODE { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string FMATERIAL_NAME { get; set; }
        /// <summary>
        /// 物料规格描述
        /// </summary>
        public string FSPEC_DESC { get; set; }

        /// <summary>
        /// 生产单位
        /// </summary>
        public string FPRO_UNIT_ID { get; set; }
        /// <summary>
        /// 生产单位
        /// </summary>
        public string FPRO_UNIT_NAME { get; set; }
        /// <summary>
        /// 重量单位
        /// </summary>
        public string FWEIGHT_UNIT_ID { get; set; }
        /// <summary>
        /// 重量单位
        /// </summary>
        public string FWEIGHT_UNIT_NAME { get; set; }
        #region 附加属性
        /// <summary>
        /// 不良原因名称
        /// </summary>
        public string FBAD_REASON_NAME
        {
            get;
            set;
        }
        /// <summary>
        /// 检验人员工名称
        /// </summary>
        public string FCHECK_PERSON_NAME
        {
            get;
            set;
        }
        #endregion
        /// <summary>
        /// 产品型号
        /// </summary>
        public string FGOODS_MODEL
        {
            get;
            set;
        }
        /// <summary>
        /// 是否转委外 转委外申请单后自动改为1，委外工单转完后自动下发
        /// </summary>
        public int FIS_OUT
        {
            get; set;
        }




        ///// <summary>
        ///// 同步状态
        ///// </summary>
        //public int FSYNC_STATUS { get; set; }


        ///// <summary>
        ///// 同步信息
        ///// </summary>
        //public string FSYNC_MSG { get; set; }


        ///// <summary>
        ///// 第几卷
        ///// </summary>
        //public int FDI_JI_JUAN { get; set; }

        ///// <summary>
        ///// 当前第几卷状态
        ///// </summary>
        //public int FJUAN_STATUS { get; set; }




        /// <summary>
        /// iu 订单日期
        /// </summary>
        public DateTime? FIUIU_ORDER_DATE { get; set; }
        /// <summary>
        /// iu 客户
        /// </summary>
        public string FIUIU_CUST { get; set; }
        /// <summary>
        /// iu订单号
        /// </summary>
        public string FIUIU_ORDER_NO { get; set; }
        /// <summary>
        /// iu 业务名称
        /// </summary>
        public string FIUIU_SALE_NAME { get; set; }
        ///// <summary>
        ///// 批量完工-计算得出完工时间
        ///// </summary>
        //public DateTime? FACT_BATCH_ED_DATE
        //{
        //    get; set;
        //}
        ///<summary>
        ///排版数
        ///<summary>
        public int? FLAYOUT_NUM
        {
            get;
            set;
        }
        ///<summary>
        ///张数
        ///<summary>
        public int? FSHEET_NUM
        {
            get;
            set;
        }
        ///<summary>
        ///合格张数
        ///<summary>
        public int? FPASS_SHEET_NUM
        {
            get;
            set;
        }
        ///<summary>
        ///不合格张数
        ///<summary>
        public int? FNG_SHEET_NUM
        {
            get;
            set;
        }
        /// <summary>
        /// 父级物料ID
        /// </summary>
        public string FPARENT_MATERIAL_ID
        {
            get; set;
        }
        /// <summary>
        /// 父级物料编码
        /// </summary>
        public string FPARENT_MATERIAL_CODE
        {
            get; set;
        }
        /// <summary>
        /// 父级物料名称
        /// </summary>
        public string FPARENT_MATERIAL_NAME
        {
            get; set;
        }
        /// <summary>
        /// 项目编号
        /// </summary>
        public string FPROJECT_CODE
        {
            get; set;
        }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string FPROJECT_NAME
        {
            get; set;
        }

        /// <summary>
        /// 批号
        /// </summary>
        public string FLOT_NO
        {
            get;
            set;
        }

        /// <summary>
        /// 批号颜色
        /// </summary>
        public string FLOT_NO_COLOR { get; set; }


    }
}
