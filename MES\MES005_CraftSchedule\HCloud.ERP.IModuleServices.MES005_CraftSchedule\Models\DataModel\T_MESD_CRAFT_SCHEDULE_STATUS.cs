﻿using HCloud.Core.HCPlatform.Sugar;
using HCloud.Core.ProxyGenerator.Models;
using System;

namespace HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models
{
    /// <summary>
    /// 排程任务状态表
    /// </summary>
    [SugarTable("T_MESD_CRAFT_SCHEDULE_STATUS")]
    public class T_MESD_CRAFT_SCHEDULE_STATUS : DtlDBEntityBase
    {
        public T_MESD_CRAFT_SCHEDULE_STATUS()
        {
        }

        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string FCRAFT_SCHEDULE_STATUS_ID
        {
            get; set;
        }
        /// <summary>
        /// 任务Id
        /// </summary>
        public string FCRAFT_SCHEDULE_ID
        {
            get; set;
        }
        /// <summary>
        /// 下发状态 0-未下发, 1-已下发
        /// </summary>
        public bool FRELEASE_STATUS
        {
            get; set;
        }
        /// <summary>
        /// 已接收数
        /// </summary>
        public decimal FRECV_QTY
        {
            get; set;
        }
        /// <summary>
        /// 已完工数
        /// </summary>
        public decimal FFINISH_QTY
        {
            get; set;
        }


        /// <summary>
        /// 接收时间
        /// </summary>
        public DateTime? FRECV_DATE
        {
            get; set;
        }
        /// <summary>
        /// 实际开工时间
        /// </summary>
        public DateTime? FACT_ST_DATE
        {
            get; set;
        }
        /// <summary>
        /// 实际完工时间
        /// </summary>
        public DateTime? FACT_ED_DATE
        {
            get; set;
        }

        /// <summary>
        /// 结案状态
        /// </summary>
        public int FIF_CLOSE
        {
            get; set;
        }

        /// <summary>
        /// 结案人ID
        /// </summary>
        public string FCLOSER_ID
        {
            get; set;
        }
        /// <summary>
        /// 结案人
        /// </summary>
        public string FCLOSER
        {
            get; set;
        }
        /// <summary>
        /// 结案日期
        /// </summary>
        public DateTime? FCLOSEDATE
        {
            get; set;
        }
        /// <summary>
        /// 下发人ID
        /// </summary>
        public string FRELEASER_ID
        {
            get; set;
        }
        /// <summary>
        /// 下发人
        /// </summary>
        public string FRELEASER
        {
            get; set;
        }
        /// <summary>
        /// 下发日期
        /// </summary>
        public DateTime? FRELEASE_DATE
        {
            get; set;
        }

        /// <summary>
        /// 实际工时(小时)
        /// </summary>
        public decimal FACT_USE_HOUR { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public decimal FPASS_QTY
        {
            get; set;
        }
        /// <summary>
        /// 不良数量
        /// </summary>
        public decimal FNG_QTY
        {
            get; set;
        }

        /// <summary>
        /// 不良重量 根据报工完工的不良重量汇总
        /// </summary>
        public decimal FNG_WEIGHT
        {
            get; set;
        }
        /// <summary>
        /// 合格重量 根据报工完工的合格重量汇总
        /// </summary>
        public decimal FPASS_WEIGHT
        {
            get; set;
        }
        /// <summary>
        /// 已完工重量 根据报工完工的不良+合格重量汇总
        /// </summary>
        public decimal FFINISH_WEIGHT
        {
            get; set;
        }

        /// <summary>
        /// 是否转委外 转委外申请单后自动改为1，委外工单转完后自动下发
        /// </summary>
        public int FIS_OUT
        {
            get; set;
        }

        /// <summary>
        /// 标签是否生成
        /// </summary>
        public bool? FIS_TAG_GENERATE
        {
            get; set;
        } = false;

    }
}
