﻿using HCloud.Core.HCPlatform.Sugar;
using HCloud.Core.ProxyGenerator.Models;
using System;

namespace HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models
{
    /// <summary>
    /// 排程任务表
    /// </summary>
    [SugarTable("T_MESD_CRAFT_SCHEDULE")]
    public class T_MESD_CRAFT_SCHEDULE : MstDBEntityBase
    {
        public T_MESD_CRAFT_SCHEDULE()
        {
        }

        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string FCRAFT_SCHEDULE_ID
        {
            get; set;
        }
        /// <summary>
        /// 任务编号
        /// </summary>
        public string FCRAFT_SCHEDULE_NO
        {
            get; set;
        }
       
        /// <summary>
        /// 二维码
        /// </summary>
        public string FQRCODE
        {
            get; set;
        }
        /// <summary>
        /// 计划产出数量
        /// </summary>
        public decimal FPLAN_QTY
        {
            get; set;
        }
        /// <summary>
        /// 原下发数量
        /// </summary>
        public decimal FORI_PLAN_QTY
        {
            get; set;
        }
        /// <summary>
        /// 订单Id
        /// </summary>
        public string FSALE_ORDER_ID
        {
            get; set;
        }
        /// <summary>
        /// 工单Id
        /// </summary>
        public string FWORK_ORDER_ID
        {
            get; set;
        }
        /// <summary>
        /// 工单产品Id
        /// </summary>
        public string FMATERIAL_ID
        {
            get; set;
        }
        /// <summary>
        /// 工单工艺记录Id
        /// </summary>
        public string FWORK_ORDER_CRAFT_ID
        {
            get; set;
        }
        /// <summary>
        /// 批号
        /// </summary>
        public string FLOT_NO
        {
            get; set;
        }
        /// <summary>
        /// 批号颜色
        /// </summary>
        public string FLOT_NO_COLOR { get; set; }
        /// <summary>
        /// 工艺Id
        /// </summary>
        public string FCRAFT_ID
        {
            get; set;
        }
        /// <summary>
        /// 工位Id
        /// </summary>
        public string FSTATION_ID
        {
            get; set;
        }
        /// <summary>
        /// 计划开工时间
        /// </summary>
        public DateTime FPLAN_ST_DATE
        {
            get; set;
        }
        /// <summary>
        /// 计划工时(小时)
        /// </summary>
        public decimal FPLAN_USE_HOUR
        {
            get; set;
        }
        /// <summary>
        /// 计划完工时间
        /// </summary>
        public DateTime FPLAN_ED_DATE
        {
            get; set;
        }
        /// <summary>
        /// 计划员员工Id
        /// </summary>
        public string FPLAN_EMP_ID
        {
            get; set;
        }
        /// <summary>
        /// 备注
        /// </summary>
        public string FREMARK
        {
            get; set;
        }

        /// <summary>
        /// 排程批次Id
        /// </summary>
        public string FSCHEDULE_BATCH_ID { get; set; }

        /// <summary>
        /// 单位重量 从生产工单带入冗余
        /// </summary>
        public decimal FUNIT_WEIGHT
        {
            get; set;
        }
        /// <summary>
        /// 计划产出重量 界面栏位只读 计划产出数量*单重
        /// </summary>
        public decimal FPLAN_WEIGHT
        {
            get; set;
        }
        /// <summary>
        /// 重量单位ID 从生产工单带入冗余
        /// </summary>
        public string FWEIGHT_UNIT_ID
        {
            get; set;
        }
        /// <summary>
        /// 排程排序
        /// </summary>
        public int SCHEDULE_FSHOW_SEQNO { get; set; }

        ///<summary>
        ///作业人数
        ///<summary>
        public int? FWORK_NUM
        {
            get;
            set;
        }


        /// <summary>
        /// 父排程ID
        /// </summary>
        public string FPARENT_CRAFT_SCHEDULE_ID
        {
            get; set;
        }

        /// <summary>
        /// 加工任务ID
        /// </summary>
        public string FSOURCE_JOB_BOOKING_ID 
        {
            get;
            set;
        }

        /// <summary>
        /// 文本字段1 
        /// 佳乐-绑定工艺报工标签数据FCRAFT_JOB_BOOKING_TAG_ID
        /// </summary>
        public string FSCHEDULE_TEXT1 { get; set; }
    }
}
