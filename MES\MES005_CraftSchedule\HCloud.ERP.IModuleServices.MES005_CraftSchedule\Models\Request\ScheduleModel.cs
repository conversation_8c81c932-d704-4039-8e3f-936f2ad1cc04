﻿using System.Collections.Generic;

namespace HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models
{
    /// <summary>
    /// 待排程模型
    /// </summary>
    [MessagePack.MessagePackObject]
    public class ScheduleModel
    {
        /// <summary>
        /// 工单主表
        /// </summary>
        public List<WOScheduleModel> WOModels { get; set; }

        /// <summary>
        /// 工单工艺
        /// </summary>
        public List<WOCraftScheduleModel> WOCraftModels { get; set; }

        /// <summary>
        /// 是否同时下发任务
        /// </summary>
        public bool IsReleaseTask { get; set; }

        /// <summary>
        /// 修改类型
        /// </summary>
        public ScheduleEditType EditType { get; set; }

        /// <summary>
        /// 修改的对象
        /// </summary>
        public WOCraftScheduleModel EditModel { get; set; }

        /// <summary>
        /// 工艺排程任务拆分明细
        /// </summary>
        public List<WOCraftScheduleModel> TaskSplitList { get; set; }


        /// <summary>
        /// 是否自动为未排满的工单工艺数量创建新的“待下发”排程任务。
        /// true: 自动创建 (当前默认行为)。
        /// false: 不创建，只生成明确传入WOCraftModels中的任务。
        /// </summary>
        public bool IsAutoCreateRemainder { get; set; } = true;

    }
}
