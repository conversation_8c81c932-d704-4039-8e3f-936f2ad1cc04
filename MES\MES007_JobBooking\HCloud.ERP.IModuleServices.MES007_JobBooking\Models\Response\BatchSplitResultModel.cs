using System;
using System.Collections.Generic;

namespace HCloud.ERP.IModuleServices.MES007_JobBooking.Models
{
    /// <summary>
    /// 批次拆分结果模型
    /// </summary>
    [MessagePack.MessagePackObject]
    public class BatchSplitResultModel
    {
        /// <summary>
        /// 原始加工任务ID
        /// </summary>
        public string FORIGINAL_JOB_BOOKING_ID { get; set; }

        /// <summary>
        /// 原始排程任务ID
        /// </summary>
        public string FORIGINAL_SCHEDULE_ID { get; set; }

        /// <summary>
        /// 原始批号
        /// </summary>
        public string FORIGINAL_LOT_NO { get; set; }

        /// <summary>
        /// 拆分出的已完工排程ID
        /// </summary>
        public string FSPLIT_FINISHED_SCHEDULE_ID { get; set; }

        /// <summary>
        /// 拆分出的已完工排程编号
        /// </summary>
        public string FSPLIT_FINISHED_SCHEDULE_NO { get; set; }

        /// <summary>
        /// 拆分出的已完工批号
        /// </summary>
        public string FSPLIT_FINISHED_LOT_NO { get; set; }

        /// <summary>
        /// 拆分完工数量
        /// </summary>
        public decimal FSPLIT_FINISH_QTY { get; set; }

        /// <summary>
        /// 下游工序任务列表
        /// </summary>
        public List<DownstreamTaskInfo> DownstreamTasks { get; set; } = new List<DownstreamTaskInfo>();

        /// <summary>
        /// 拆分是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 拆分结果消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 下游任务信息
    /// </summary>
    [MessagePack.MessagePackObject]
    public class DownstreamTaskInfo
    {
        /// <summary>
        /// 下游排程任务ID
        /// </summary>
        public string FCRAFT_SCHEDULE_ID { get; set; }

        /// <summary>
        /// 下游排程任务编号
        /// </summary>
        public string FCRAFT_SCHEDULE_NO { get; set; }

        /// <summary>
        /// 下游工艺ID
        /// </summary>
        public string FCRAFT_ID { get; set; }

        /// <summary>
        /// 下游工艺名称
        /// </summary>
        public string FCRAFT_NAME { get; set; }

        /// <summary>
        /// 下游工位ID
        /// </summary>
        public string FSTATION_ID { get; set; }

        /// <summary>
        /// 下游工位名称
        /// </summary>
        public string FSTATION_NAME { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        public decimal FPLAN_QTY { get; set; }

        /// <summary>
        /// 批号
        /// </summary>
        public string FLOT_NO { get; set; }

        /// <summary>
        /// 是否已下发
        /// </summary>
        public bool FRELEASE_STATUS { get; set; }
    }
} 