﻿using HCloud.Core.HCPlatform.Sugar;
using HCloud.Core.HCPlatform.Utilities;
using HCloud.Core.ProxyGenerator.Models;

namespace HCloud.ERP.IModuleServices.MES007_JobBooking.Models
{
    /// <summary>
    /// 工艺报工标签数据
    /// </summary>
    [SugarTable("T_MESD_CRAFT_JOB_BOOKING_TAG")]
    public class T_MESD_CRAFT_JOB_BOOKING_TAG : MstDBEntityBase
    {
        /// <summary>
        /// id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string FCRAFT_JOB_BOOKING_TAG_ID
        {
            get; set;
        }
        /// <summary>
        /// 报工记录id
        /// </summary>
        public string FCRAFT_JOB_BOOKING_ID
        {
            get; set;
        }
        /// <summary>
        /// 任务Id
        /// </summary>
        public string FCRAFT_SCHEDULE_ID
        {
            get; set;
        }
      
        /// <summary>
        ///数量
        /// </summary>
        public decimal? FPRODUCT_NUM { get; set; }
        /// <summary>
        /// 重量
        /// </summary>
        public decimal? FPRODUCT_WEIGHT { get; set; }
        /// <summary>
        /// 毛重
        /// </summary>
        public decimal? FGROSS_WEIGHT { get; set; }
        /// <summary>
        /// 材料批号
        /// </summary>
        public string FSTUFFLOT_NO
        {
            get; set;
        }
        /// <summary>
        /// 条码号
        /// </summary>
        public string FBARCODE_NO { get; set; }
        public string FTEXT1 { get; set; }
        public string FTEXT2 { get; set; }
        public string FTEXT3 { get; set; }
        public string FTEXT4 { get; set; }
        public string FTEXT5 { get; set; }
        public string FSHOW_SEQNO { get; set; }
        /// <summary>
        /// 打印标签类型CraftTagType子表，内外包箱,产品(ITEM,PACK,PRODCUT)
        /// </summary>
        public string FTAG_TYPE { get; set; }
        /// <summary>
        /// 包装类型（ITEM,PACK），内包箱,外包箱
        /// </summary>
        public string FPACK_TYPE { get; set; }
        /// <summary>
        /// 上级包箱号
        /// </summary>
        public string FPACK_PARENTS { get; set; }
        /// <summary>
        /// 是否尾箱（true=尾箱，false=整箱）
        /// </summary>
        public bool? FIS_TAIL_BOX { get; set; }

    }
}
