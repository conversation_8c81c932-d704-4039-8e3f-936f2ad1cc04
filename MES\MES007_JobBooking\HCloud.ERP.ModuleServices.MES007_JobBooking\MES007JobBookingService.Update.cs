using Castle.Core.Internal;
using HCloud.Core.Common;
using HCloud.Core.HCPlatform.Auth.Internal;
using HCloud.Core.HCPlatform.Auth.Models;
using HCloud.Core.HCPlatform.CommonData;
using HCloud.Core.HCPlatform.Serialization;
using HCloud.Core.HCPlatform.Utilities;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.ProxyGenerator.Models;
using HCloud.Core.Sugar;
using HCloud.ERP.IModuleServices.ADM024_Employee;
using HCloud.ERP.IModuleServices.EMS001.Models;
using HCloud.ERP.IModuleServices.IU010_UniversalApi;
using HCloud.ERP.IModuleServices.MES001WorkArea.Models;
using HCloud.ERP.IModuleServices.MES002_Craft;
using HCloud.ERP.IModuleServices.MES002_Craft.Models;
using HCloud.ERP.IModuleServices.MES003_WorkOrder.Models;
using HCloud.ERP.IModuleServices.MES004_WorkFetch;
using HCloud.ERP.IModuleServices.MES004_WorkFetch.Models;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models.Request;
using HCloud.ERP.IModuleServices.MES006_WorkIn;
using HCloud.ERP.IModuleServices.MES006_WorkIn.Models;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models.Response;
using HCloud.ERP.IModuleServices.MES017_TAG;
using HCloud.ERP.IModuleServices.MES017_TAG.Models.Response;
using HCloud.ERP.IModuleServices.MSD002_Material;
using HCloud.ERP.IModuleServices.MSD002_Material.Models;
using HCloud.ERP.IModuleServices.MSD004_MaterialBOM.Models;
using HCloud.ERP.IModuleServices.STK003_OtherIOBill;
using HCloud.ERP.IModuleServices.STK003_OtherIOBill.Models;
using HCloud.ERP.IModuleServices.STK005_Stock;
using HCloud.ERP.IModuleServices.STK005_Stock.Models.Request;
using HCloud.ERP.IModuleServices.ZhaokeApi_UniApi.Models;
using HCloud.ERP.IModuleServices.ZhaokeApiUniApi;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using QRCoder;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HCloud.ERP.ModuleServices.MES007_JobBooking
{
    public partial class MES007JobBookingService
    {
        ////
        #region 系统参数

        /// <summary>
        /// 用于批号生成的线程安全锁
        /// </summary>
        private static readonly object _lotNumberLock = new object();

        /// <summary>
        /// 存储上一次生成批号时所使用的时间块 (格式: YYTTT)
        /// </summary>
        private static string _lastTimeBlock = "";

        /// <summary>
        /// 在同一时间块内的计数器
        /// </summary>
        private static int _sequenceCounter = 0;

        /// <summary>
        /// 工位是否允许并行开工
        /// </summary>
        private const string _MES_STATION_ALLOW_COMPLEX = "MES_STATION_ALLOW_COMPLEX";
        /// <summary>
        /// 报工完工总数量允许超出排程数量
        /// </summary>
        private const string _MES_FinishQtyAllowGreatSch = "MES_FinishQtyAllowGreateSch";
        /// <summary>
        /// 后工艺报工允许超过前工艺的完工总数量
        /// </summary>
        private const string _MES_FinishQtyAllowGreatePre = "MES_FinishQtyAllowGreatePre";
        /// <summary>
        /// 工艺开工检查条件 1-检查前一工艺有完工/部份完工记录，0-检查前一工艺有开工记录
        /// </summary>
        private const string _MES_StartJobCheckModel = "MES_StartJobCheckModel";
        /// <summary>
        /// 排程超出数量时自动结案
        /// </summary>
        private const string _MES_AutoCloseOnExceedQty = "MES_AutoCloseOnExceedQty";
        /// <summary>
        /// 公司标志
        /// </summary>
        private const string _Company = "Company";

        ///<summary>
        ///半成品入库
        /// </summary>
        private const string _MES_FinishJobPlatingStore = "MES_FinishJobPlatingStore";

        /// <summary>
        /// 是否启用批次流转, 完工后自动拆分后续工艺, 进行批次流转
        /// </summary>
        private const string _MES_EnableAutoSplitOnCompletion = "MES_EnableAutoSplitOnCompletion";

        /// <summary>
        /// 自动拆分生成的下游子排程是否立即下发
        /// </summary>
        private const string _MES_AutoSplitReleaseImmediately = "MES_AutoSplitReleaseImmediately";

        /// <summary>
        /// 不良品是否返工
        /// </summary>
        private const string _MES_EnableAutoReworkOnNg = "MES_EnableAutoReworkOnNg";



        #endregion

        #region 恢复加工
        /// <summary>
        /// 恢复加工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<CraftJobBookingModel>> ResumeJobAsync(JobBookingOperateModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            //恢复开工验证
            if (!await ValidateResumeJobAsync(model))
            {
                ERROR(null, 105001, _multiLang["暂停开工验证失败."]);
            }

            //string empId = user.UserPsnId;
            //string empNm = user.UserPsnName;

            var curDate = _iauth.GetCurDateTime();

            var db = _isugar.DB;

            T_MESD_CRAFT_JOB_BOOKING daoJob = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                        .Where(p => p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                        //.Select(p => new T_MESD_CRAFT_JOB_BOOKING
                        //{
                        //    FACT_ST_DATE = p.FACT_ST_DATE,
                        //    FACT_USE_HOUR = p.FACT_USE_HOUR,

                        //    FLAST_RESUME_DATE = p.FLAST_RESUME_DATE,
                        //    FLAST_PAUSE_DATE = p.FLAST_PAUSE_DATE,

                        //    FCRAFT_ID = p.FCRAFT_ID,

                        //    FSALE_ORDER_ID = p.FSALE_ORDER_ID,
                        //    FSTATION_ID = p.FSTATION_ID,

                        //    FCRAFT_JOB_BOOKING_ID = p.FCRAFT_JOB_BOOKING_ID,
                        //    FCRAFT_JOB_BOOKING_NO = p.FCRAFT_JOB_BOOKING_NO,

                        //    FEMP_ID = p.FEMP_ID,
                        //    FEMP_NAME = p.FEMP_NAME,

                        //    FWORK_ORDER_CRAFT_ID = p.FWORK_ORDER_CRAFT_ID,

                        //    FWORK_ORDER_ID = p.FWORK_ORDER_ID,
                        //    FWORK_STATUS = p.FWORK_STATUS,

                        //    FCRAFT_SCHEDULE_ID = p.FCRAFT_SCHEDULE_ID,
                        //    FMATERIAL_ID = p.FMATERIAL_ID,

                        //    FECODE = p.FECODE,
                        //})
                        .FirstAsync();


            daoJob.FLAST_RESUME_DATE = curDate;

            daoJob.FWORK_STATUS = WorkStatus.working.ToString();

            db = _isugar.DB;
            try
            {
                db.BeginTran();

                //更新加工任务
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(daoJob)
                    .UpdateColumns(p => new { p.FWORK_STATUS, p.FLAST_RESUME_DATE })
                    .ExecuteCommandAsync();

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }

            //保存日志
            Dictionary<string, string> logData = new Dictionary<string, string>();
            logData.Add("FCRAFT_JOB_BOOKING_ID", daoJob.FCRAFT_JOB_BOOKING_ID);
            logData.Add("FCRAFT_JOB_BOOKING_NO", daoJob.FCRAFT_JOB_BOOKING_NO);

            logData.Add("FCRAFT_ID", daoJob.FCRAFT_ID);
            logData.Add("FSTATION_ID", daoJob.FSTATION_ID);

            logData.Add("FEMP_ID", daoJob.FEMP_ID);
            logData.Add("FEMP_NAME", daoJob.FEMP_NAME);

            logData.Add("FACT_ST_DATE", daoJob.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            logData.Add("FLAST_PAUSE_DATE", daoJob.FLAST_PAUSE_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            logData.Add("FLAST_RESUME_DATE", daoJob.FLAST_RESUME_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));

            logData.Add("FACT_USE_HOUR", daoJob.FACT_USE_HOUR.ToString());

            logData.Add("FMATERIAL_ID", daoJob.FMATERIAL_ID);

            logData.Add("FWORK_ORDER_ID", daoJob.FWORK_ORDER_ID);
            logData.Add("FCRAFT_SCHEDULE_ID", daoJob.FCRAFT_SCHEDULE_ID);

            logData.Add("FWORK_STATUS", daoJob.FWORK_STATUS);

            _ = SaveOperateLogAsync(daoJob, OperateType.resume, _serialize.Serialize(logData));

            //返回加工任务
            var jobBooking = await GetJobBookingByIdAsync(daoJob.FCRAFT_JOB_BOOKING_ID);
            DataResult<CraftJobBookingModel> result = new DataResult<CraftJobBookingModel>()
            {
                Entity = jobBooking,
                StatusCode = 200,
            };


            //  SendMsg();//发送消息通知
            return await OK(result);
        }

        /// <summary>
        ///  验证恢复开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<bool> ValidateResumeJobAsync(JobBookingOperateModel model)
        {

            if (model == null)
            {
                ERROR(null, 105001, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model"));
            }

            if (string.IsNullOrWhiteSpace(model.FCRAFT_JOB_BOOKING_ID))
            {
                ERROR(null, 105003, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model.FCRAFT_JOB_BOOKING_ID"));
            }

            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                ERROR(null, 105004, _multiLang["执行失败, 当前用户员工信息为空."]);
            }

            //加工任务
            var jobData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE>
                ((job, sch) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID))
                .Where((job, sch) => job.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                .Select((job, sch) => new
                {
                    job.FCRAFT_JOB_BOOKING_ID,
                    job.FCRAFT_JOB_BOOKING_NO,
                    job.FWORK_STATUS,
                    job.FEMP_ID,
                    job.FEMP_NAME,
                    sch.FCRAFT_SCHEDULE_NO
                })
                .FirstAsync();
            if (jobData == null)
            {
                ERROR(null, 105005, _multiLang["加工任务数据不存在, 请查正."]);
            }


            //暂时放开，后面做参数控制
            /*
            //只能由本人恢复加工           
            if (jobData.FEMP_ID != user.UserPsnId)
            {
                ERROR(null, 105006, string.Format(_multiLang["执行失败, 加工任务 {0}, 是由{1}开工的, 请通知本人操作."],
                 jobData.FCRAFT_JOB_BOOKING_NO, jobData.FEMP_NAME));
            }
            */

            //检查状态
            if (jobData.FWORK_STATUS == WorkStatus.cancel.ToString())
            {
                ERROR(null, 105007, string.Format(_multiLang["执行失败, 加工任务 {0}, 已取消加工."], jobData.FCRAFT_JOB_BOOKING_NO));
            }

            if (jobData.FWORK_STATUS == WorkStatus.finished.ToString())
            {
                ERROR(null, 105008, string.Format(_multiLang["执行失败, 加工任务 {0}, 已完工."], jobData.FCRAFT_JOB_BOOKING_NO));
            }

            if (jobData.FWORK_STATUS == WorkStatus.working.ToString())
            {
                ERROR(null, 105009, string.Format(_multiLang["执行失败, 加工任务 {0}, 已在加工中."], jobData.FCRAFT_JOB_BOOKING_NO));
            }

            return await Task.FromResult(true);

        }
        #endregion

        #region 暂停加工
        /// <summary>
        /// 暂停加工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<CraftJobBookingModel>> PauseJobAsync(JobBookingOperateModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            //取消开工验证
            if (!await ValidatePauseJobAsync(model))
            {
                ERROR(null, 104001, _multiLang["暂停开工验证失败."]);
            }

            //string empId = user.UserPsnId;
            //string empNm = user.UserPsnName;

            var curDate = _iauth.GetCurDateTime();

            var db = _isugar.DB;

            T_MESD_CRAFT_JOB_BOOKING daoJob = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                        .Where(p => p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                        //.Select(p => new T_MESD_CRAFT_JOB_BOOKING
                        //{
                        //    FACT_ST_DATE = p.FACT_ST_DATE,
                        //    FACT_USE_HOUR = p.FACT_USE_HOUR,

                        //    FLAST_RESUME_DATE = p.FLAST_RESUME_DATE,

                        //    FCRAFT_ID = p.FCRAFT_ID,

                        //    FSALE_ORDER_ID = p.FSALE_ORDER_ID,
                        //    FSTATION_ID = p.FSTATION_ID,

                        //    FCRAFT_JOB_BOOKING_ID = p.FCRAFT_JOB_BOOKING_ID,
                        //    FCRAFT_JOB_BOOKING_NO = p.FCRAFT_JOB_BOOKING_NO,

                        //    FEMP_ID = p.FEMP_ID,
                        //    FEMP_NAME = p.FEMP_NAME,

                        //    FWORK_ORDER_CRAFT_ID = p.FWORK_ORDER_CRAFT_ID,

                        //    FWORK_ORDER_ID = p.FWORK_ORDER_ID,
                        //    FWORK_STATUS = p.FWORK_STATUS,

                        //    FCRAFT_SCHEDULE_ID = p.FCRAFT_SCHEDULE_ID,
                        //    FMATERIAL_ID = p.FMATERIAL_ID,

                        //    FECODE = p.FECODE,
                        //})
                        .FirstAsync();


            daoJob.FLAST_PAUSE_DATE = curDate;

            //计算实际工时(与下句顺序不能调)
            daoJob.FACT_USE_HOUR = CalcActUseHour(daoJob.FACT_USE_HOUR, daoJob.FLAST_RESUME_DATE, daoJob.FACT_ST_DATE, curDate, daoJob.FWORK_STATUS);

            daoJob.FWORK_STATUS = WorkStatus.paused.ToString();

            db = _isugar.DB;
            try
            {
                db.BeginTran();

                //更新加工任务
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(daoJob)
                    .UpdateColumns(p => new { p.FWORK_STATUS, p.FLAST_PAUSE_DATE, p.FACT_USE_HOUR })
                    .ExecuteCommandAsync();

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }

            //保存日志
            Dictionary<string, string> logData = new Dictionary<string, string>();
            logData.Add("FCRAFT_JOB_BOOKING_ID", daoJob.FCRAFT_JOB_BOOKING_ID);
            logData.Add("FCRAFT_JOB_BOOKING_NO", daoJob.FCRAFT_JOB_BOOKING_NO);

            logData.Add("FCRAFT_ID", daoJob.FCRAFT_ID);
            logData.Add("FSTATION_ID", daoJob.FSTATION_ID);

            logData.Add("FEMP_ID", daoJob.FEMP_ID);
            logData.Add("FEMP_NAME", daoJob.FEMP_NAME);

            logData.Add("FACT_ST_DATE", daoJob.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            logData.Add("FLAST_PAUSE_DATE", daoJob.FLAST_PAUSE_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));

            logData.Add("FACT_USE_HOUR", daoJob.FACT_USE_HOUR.ToString());

            logData.Add("FMATERIAL_ID", daoJob.FMATERIAL_ID);

            logData.Add("FWORK_ORDER_ID", daoJob.FWORK_ORDER_ID);
            logData.Add("FCRAFT_SCHEDULE_ID", daoJob.FCRAFT_SCHEDULE_ID);

            logData.Add("FWORK_STATUS", daoJob.FWORK_STATUS);

            _ = SaveOperateLogAsync(daoJob, OperateType.pause, _serialize.Serialize(logData));

            //返回加工任务
            var jobBooking = await GetJobBookingByIdAsync(daoJob.FCRAFT_JOB_BOOKING_ID);
            DataResult<CraftJobBookingModel> result = new DataResult<CraftJobBookingModel>()
            {
                Entity = jobBooking,
                StatusCode = 200,
            };



            // SendMsg();//发送消息通知
            return await OK(result);
        }

        /// <summary>
        ///  验证暂停开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<bool> ValidatePauseJobAsync(JobBookingOperateModel model)
        {

            if (model == null)
            {
                ERROR(null, 104001, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model"));
            }

            if (string.IsNullOrWhiteSpace(model.FCRAFT_JOB_BOOKING_ID))
            {
                ERROR(null, 104003, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model.FCRAFT_JOB_BOOKING_ID"));
            }

            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                ERROR(null, 104004, _multiLang["执行失败, 当前用户员工信息为空."]);
            }

            //加工任务
            var jobData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE>
                ((job, sch) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID))
                .Where((job, sch) => job.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                .Select((job, sch) => new
                {
                    job.FCRAFT_JOB_BOOKING_ID,
                    job.FCRAFT_JOB_BOOKING_NO,
                    job.FWORK_STATUS,
                    job.FEMP_ID,
                    job.FEMP_NAME,
                    sch.FCRAFT_SCHEDULE_NO
                })
                .FirstAsync();
            if (jobData == null)
            {
                ERROR(null, 104005, _multiLang["加工任务数据不存在, 请查正."]);
            }


            //暂时放开，后面做参数控制
            /*
            //只能由本人暂停开工            
            if (jobData.FEMP_ID != user.UserPsnId)
            {
                ERROR(null, 104006, string.Format(_multiLang["执行失败, 加工任务 {0}, 是由{1}开工的, 请通知本人操作."],
                 jobData.FCRAFT_JOB_BOOKING_NO, jobData.FEMP_NAME));
            }
            */

            //检查状态
            if (jobData.FWORK_STATUS == WorkStatus.cancel.ToString())
            {
                ERROR(null, 104007, string.Format(_multiLang["执行失败, 加工任务 {0}, 已取消加工."], jobData.FCRAFT_JOB_BOOKING_NO));
            }

            if (jobData.FWORK_STATUS == WorkStatus.finished.ToString())
            {
                ERROR(null, 104008, string.Format(_multiLang["执行失败, 加工任务 {0}, 已完工."], jobData.FCRAFT_JOB_BOOKING_NO));
            }

            if (jobData.FWORK_STATUS == WorkStatus.paused.ToString())
            {
                ERROR(null, 104009, string.Format(_multiLang["执行失败, 加工任务 {0}, 已暂停."], jobData.FCRAFT_JOB_BOOKING_NO));
            }

            return await Task.FromResult(true);

        }
        #endregion

        #region 开工
        /// <summary>
        /// 开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<CraftJobBookingModel>> StartJobAsync(JobBookingOperateModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            //开工验证
            var validate = await ValidateStartJobAsync(model);
            if (validate.StatusCode != 200)
            {
                ERROR(null, 102001, _multiLang["开工验证失败.错误信息:" + validate.Message]);
            }

            //产生任务单号
            var billCodes = await GetBillCodesAsync();
            if (billCodes.Count < 1)
            {
                ERROR(null, 100102, string.Format(_multiLang["执行失败,产生加工任务编号为空."], 1, billCodes.Count));
            }

            string empId = user.UserPsnId;
            string empNm = user.UserPsnName;

            if (!string.IsNullOrEmpty(model.FEMP_ID))
            {
                empId = model.FEMP_ID;
                empNm = model.FEMP_NAME;
            }

            var stDate = _iauth.GetCurDateTime();

            var db = _isugar.DB;

            var schData = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
              .Where((sch) => sch.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
              //.Select((sch) => new
              //{
              //    sch.FCRAFT_SCHEDULE_ID,
              //    sch.FCRAFT_SCHEDULE_NO,

              //    sch.FWORK_ORDER_ID,
              //    sch.FWORK_ORDER_CRAFT_ID,

              //    sch.FSALE_ORDER_ID,
              //    sch.FMATERIAL_ID,

              //    sch.FSTATION_ID,
              //    sch.FCRAFT_ID,
              //})
              .FirstAsync();

            //加工表dao
            T_MESD_CRAFT_JOB_BOOKING daoJob = new T_MESD_CRAFT_JOB_BOOKING
            {
                FCRAFT_JOB_BOOKING_ID = GuidHelper.NewGuid(),
                FCRAFT_JOB_BOOKING_NO = billCodes[0],

                FACT_ST_DATE = stDate,
                FCRAFT_ID = schData.FCRAFT_ID,

                FSALE_ORDER_ID = schData.FSALE_ORDER_ID,
                FSTATION_ID = model.FSTATION_ID,


                FEMP_ID = empId,

                FEMP_NAME = empNm,
                FWORK_ORDER_CRAFT_ID = schData.FWORK_ORDER_CRAFT_ID,

                FWORK_ORDER_ID = schData.FWORK_ORDER_ID,
                FWORK_STATUS = WorkStatus.working.ToString(),

                FCRAFT_SCHEDULE_ID = schData.FCRAFT_SCHEDULE_ID,
                FMATERIAL_ID = schData.FMATERIAL_ID,
            };

            db = _isugar.DB;
            try
            {
                db.BeginTran();

                //更新加工任务
                await db.Insertable<T_MESD_CRAFT_JOB_BOOKING>(daoJob)
                    .IgnoreColumns(p => new { p.FMODIDATE, p.FMODIFIER, p.FMODIFIER_ID })
                    .ExecuteCommandAsync();

                //更新排程实际开工时间
                await UpdateScheduleStartDateAsync(daoJob, db);

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }

            //保存日志
            Dictionary<string, string> logData = new Dictionary<string, string>();

            logData.Add("FCRAFT_JOB_BOOKING_ID", daoJob.FCRAFT_JOB_BOOKING_ID);
            logData.Add("FCRAFT_JOB_BOOKING_NO", daoJob.FCRAFT_JOB_BOOKING_NO);

            logData.Add("FCRAFT_ID", daoJob.FCRAFT_ID);
            logData.Add("FSTATION_ID", daoJob.FSTATION_ID);

            logData.Add("FEMP_ID", daoJob.FEMP_ID);
            logData.Add("FEMP_NAME", daoJob.FEMP_NAME);

            logData.Add("FACT_ST_DATE", daoJob.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            logData.Add("FMATERIAL_ID", daoJob.FMATERIAL_ID);

            logData.Add("FWORK_ORDER_ID", daoJob.FWORK_ORDER_ID);
            logData.Add("FCRAFT_SCHEDULE_ID", daoJob.FCRAFT_SCHEDULE_ID);

            logData.Add("FWORK_STATUS", daoJob.FWORK_STATUS);

            _ = SaveOperateLogAsync(daoJob, OperateType.start, _serialize.Serialize(logData));

            //返回加工任务
            var jobBooking = await GetJobBookingByIdAsync(daoJob.FCRAFT_JOB_BOOKING_ID);
            DataResult<CraftJobBookingModel> result = new DataResult<CraftJobBookingModel>()
            {
                Entity = jobBooking,
                StatusCode = 200,
            };


            //SendMsg(daoJob.FCRAFT_JOB_BOOKING_ID);//博顺发送
            //SendMsg();//悠悠发送消息通知
            return await OK(result);
        }

        /// <summary>
        /// 更新排程实际开工时间
        /// </summary>
        /// <param name="daoJob"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        private async Task UpdateScheduleStartDateAsync(T_MESD_CRAFT_JOB_BOOKING daoJob, ISqlSugarClient db)
        {
            //取出最小加工时间
            var daoJobs = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => p.FCRAFT_SCHEDULE_ID == daoJob.FCRAFT_SCHEDULE_ID)
                .Where(p => p.FCRAFT_SCHEDULE_ID == daoJob.FCRAFT_SCHEDULE_ID &&
                            (p.FWORK_STATUS == WorkStatus.working.ToString() || p.FWORK_STATUS == WorkStatus.paused.ToString()))
                .Select(p => new { p.FACT_ST_DATE })
                .ToListAsync();

            DateTime? startDate = null;
            if (daoJobs.Count > 0)
            {
                startDate = daoJobs.Min(p => p.FACT_ST_DATE);
            }

            //取出排程状态
            var daoSchStatus = await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS>().Where(p => p.FCRAFT_SCHEDULE_ID == daoJob.FCRAFT_SCHEDULE_ID)
                .Select(p => new T_MESD_CRAFT_SCHEDULE_STATUS
                {
                    FACT_ST_DATE = p.FACT_ST_DATE,
                    FCRAFT_SCHEDULE_STATUS_ID = p.FCRAFT_SCHEDULE_STATUS_ID,
                    FECODE = p.FECODE
                })
                .FirstAsync();

            //验证是否需更新
            bool needUpdate = false;

            if (daoSchStatus.FACT_ST_DATE.HasValue && startDate.HasValue)
            {
                needUpdate = (daoSchStatus.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss") != startDate.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            else
            {
                needUpdate = (daoSchStatus.FACT_ST_DATE != startDate);
            }

            //更新排程状态的实际开工时间
            if (needUpdate)
            {
                daoSchStatus.FACT_ST_DATE = startDate;
                await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daoSchStatus).UpdateColumns(p => new { p.FACT_ST_DATE }).ExecuteCommandAsync();
            }
        }

        /// <summary>
        /// 产生任务编号
        /// </summary>
        /// <returns></returns>
        private async Task<List<string>> GetBillCodesAsync(int count = 1, bool isshc = false)
        {
            GenBillCodeModel model = new GenBillCodeModel();
            if (isshc)
            {
                model = new GenBillCodeModel()
                {
                    FBILL_COUNT = count,
                    FBILL_RULE_CODE = "BR-CPF-0029",
                    FENTITY_DESC = "工艺排程任务",
                    FENTITY_NAME = "T_MESD_CRAFT_SCHEDULE",
                    FFIELD_NAME = "FCRAFT_SCHEDULE_NO",
                    FFIELD_DESC = "任务编号",
                    Parms = new List<GenBillCodeParmModel> { }
                };
            }
            else
            {

                model = new GenBillCodeModel()
                {
                    FBILL_COUNT = count,
                    FBILL_RULE_CODE = "BR-CPF-0031",
                    FENTITY_DESC = "生产加工任务",
                    FENTITY_NAME = "T_MESD_CRAFT_JOB_BOOKING",
                    FFIELD_NAME = "FCRAFT_JOB_BOOKING_NO",
                    FFIELD_DESC = "加工任务编号",
                    Parms = new List<GenBillCodeParmModel> { }
                };
            }

            model.Parms.Add(new GenBillCodeParmModel
            {
                FPARM_CODE = "Date",
                FPARM_VALUE = _iauth.GetCurDateTime().ToString("yyyyMMdd"),
            });
            return await _commonDataProvider.GenEntBillCodeAsync(model);
        }

        /// <summary>
        /// 获取系统参数值
        /// </summary>
        /// <returns></returns>
        private async Task<string> GetSysParamValue(string parmCode)
        {

            EntParmModel parmModel = new EntParmModel
            {
                ProgName = ContextUtils.PageId,
                ServiceName = ContextUtils.ServiceKey,
                SysParmCode = parmCode,
            };
            return await _commonDataProvider.GetEntParmValueAsync(parmModel);


        }



        /// <summary>
        ///  验证开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<JobBookingOperateModel>> ValidateStartJobAsync(JobBookingOperateModel model)
        {
            var result = new DataResult<JobBookingOperateModel>();
            result.StatusCode = 200;
            if (model == null)
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model");
                result.StatusCode = 1010;
                return await OK(result);
            }

            if (string.IsNullOrWhiteSpace(model.FCRAFT_SCHEDULE_ID))
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model.FCRAFT_SCHEDULE_ID");
                result.StatusCode = 1010;
                return await OK(result);
            }

            if (string.IsNullOrWhiteSpace(model.FSTATION_ID))
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model.FSTATION_ID");
                result.StatusCode = 1010;
                return await OK(result);
            }


            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                result.Message = _multiLang["执行失败, 当前用户员工信息为空."];
                result.StatusCode = 1010;
                return await OK(result);
            }


            //排程任务
            var db = _isugar.DB;
            var schData = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER>
                ((sch, schStatus, wo) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID))
                .Where((sch, schStatus, wo) => sch.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                .Select((sch, schStatus, wo) => new
                {
                    sch.FCRAFT_SCHEDULE_ID,
                    sch.FCRAFT_SCHEDULE_NO,
                    schStatus.FIF_CLOSE,
                    schStatus.FRECV_QTY,
                    sch.FPLAN_QTY,
                    schStatus.FRELEASE_STATUS,
                    schStatus.FFINISH_QTY,
                    wo.FBOOKING_TYPE,
                    wo.FWORK_ORDER_NO
                })
                .FirstAsync();
            if (schData == null)
            {
                result.Message = _multiLang["排程任务数据不存在, 请查正."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            //任务是否已结案
            if (!model.FEXTERNAL_QUOTE && (schData.FIF_CLOSE == 1 || schData.FIF_CLOSE == 3))
            {
                result.Message = string.Format(_multiLang["执行失败, 排程任务 {0} 已结案."], schData.FCRAFT_SCHEDULE_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            //任务是否未下发
            if (!schData.FRELEASE_STATUS && !model.FEXTERNAL_QUOTE)
            {
                result.Message = string.Format(_multiLang["执行失败, 排程任务 {0} 未下发."], schData.FCRAFT_SCHEDULE_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            //任务是否已完工
            if (schData.FFINISH_QTY >= schData.FPLAN_QTY && model.FPASS_QTY > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 排程任务 {0} 已全部完工."], schData.FCRAFT_SCHEDULE_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            //检查是否为工单工艺报工
            if (schData.FBOOKING_TYPE != _wocraft)
            {
                result.Message = string.Format(_multiLang["执行失败, 工单 {0} 的报工方式不是按工单工艺报工."], schData.FWORK_ORDER_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            //工单
            var woData = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>
                ((sch, wo, woStatus) => new JoinQueryInfos(JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                        JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID))
                .Where((sch, wo, woStatus) => sch.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                .Select((sch, wo, woStatus) => new
                {
                    wo.FWORK_ORDER_ID,
                    wo.FWORK_ORDER_NO,
                    woStatus.FIF_CLOSE,
                    woStatus.FIF_CANCEL,
                    sch.FWORK_ORDER_CRAFT_ID,
                    sch.FCRAFT_SCHEDULE_NO
                })
                .FirstAsync();
            if (woData == null)
            {
                result.Message = _multiLang["排程任务对应的工单数据不存在, 请查正."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            //工单是否已结案
            if (woData.FIF_CLOSE == 1 || woData.FIF_CLOSE == 3)
            {
                result.Message = string.Format(_multiLang["执行失败, 排程任务 {0} , 工单 {1} 已结案."], schData.FCRAFT_SCHEDULE_NO, woData.FWORK_ORDER_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            //工单是否已作废
            if (woData.FIF_CANCEL)
            {
                result.Message = string.Format(_multiLang["执行失败, 排程任务 {0} , 工单 {1} 已作废."], schData.FCRAFT_SCHEDULE_NO, woData.FWORK_ORDER_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            //调整为支持一个排程任务有多个加工中任务，只是工位不同
            /*
            //排程任务是否有加工中的任务
            var jobData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE>
               ((job, sch) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID))
               .Where((job, sch) => job.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID &&
                       (job.FWORK_STATUS == WorkStatus.working.ToString() ||
                       job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                       job.FWORK_STATUS == WorkStatus.paused.ToString()))
               .Select((job, sch) => new
               {
                   job.FWORK_STATUS,
                   sch.FCRAFT_SCHEDULE_NO,
                   sch.FCRAFT_SCHEDULE_ID,
                   job.FCRAFT_JOB_BOOKING_NO
               })
               .FirstAsync();
            if (jobData != null)
            {
                ERROR(null, 101008, string.Format(_multiLang["执行失败, 排程任务 {0} 已有正在加工中的任务 {1}."],
                    jobData.FCRAFT_SCHEDULE_NO, jobData.FCRAFT_JOB_BOOKING_NO));
            }
            */

            //根据参数配置，允许工位同时开工任务
            if (await GetSysParamValue(_MES_STATION_ALLOW_COMPLEX) != "1")
            {

                //工位是否有其他任务正在加工中
                var stationData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER>
                    ((job, sch, wo) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                                        JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID))
                    .Where((job, sch, wo) => job.FSTATION_ID == model.FSTATION_ID &&
                            (job.FWORK_STATUS == WorkStatus.working.ToString() ||
                            job.FWORK_STATUS == WorkStatus.paused.ToString() ||
                            job.FWORK_STATUS == WorkStatus.partfinished.ToString()))
                    .Select((job, sch, wo) => new { job.FWORK_STATUS, sch.FCRAFT_SCHEDULE_NO, sch.FCRAFT_SCHEDULE_ID, job.FCRAFT_JOB_BOOKING_NO, wo.FWORK_ORDER_NO })
                    .FirstAsync();
                if (stationData != null)
                {
                    //取出工位
                    string station = string.Empty;
                    var stationResult = await GetStationByIdsAsync(new List<string> { model.FSTATION_ID });
                    if (stationResult.StatusCode == 200 && stationResult.Entity != null && stationResult.Entity.Count > 0)
                    {
                        station = string.Concat(stationResult.Entity[0].FSTATION_CODE, "/", stationResult.Entity[0].FSTATION_NAME);
                    }

                    if (stationData.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                    {
                        result.Message = string.Format(_multiLang["不需重复执行: 工位 {0}, 排程任务 {1}, 正在加工任务 {2}, 工单 {3}"],
                            station, stationData.FCRAFT_SCHEDULE_NO, stationData.FCRAFT_JOB_BOOKING_NO, stationData.FWORK_ORDER_NO);
                        result.StatusCode = 1010;
                        return await OK(result);
                    }
                    result.Message = string.Format(_multiLang["不能同时开工: 工位 {0}, 正在加工任务 {1}, 排程任务 {2}, 工单 {3}"],
                        station, stationData.FCRAFT_JOB_BOOKING_NO, stationData.FCRAFT_SCHEDULE_NO, stationData.FWORK_ORDER_NO);
                    result.StatusCode = 1010;
                    return await OK(result);
                }
            }

            //
            //工艺开工检查条件 1-检查前一工艺有完工/部份完工记录，0-检查前一工艺有开工记录
            var checkPre = await GetSysParamValue(_MES_StartJobCheckModel);
            if (string.IsNullOrWhiteSpace(checkPre))
            {
                checkPre = "1";
            }


            //检查前一工艺是否有完工记录
            var woCrafts = (await db.Queryable<T_MESD_WORK_ORDER_CRAFT>()
                .Where(p => p.FWORK_ORDER_ID == woData.FWORK_ORDER_ID)
                .Select(p => new { p.FWORK_ORDER_ID, p.FWORK_ORDER_CRAFT_ID, p.FCRAFT_ID, p.FSHOW_SEQNO })
                .ToListAsync())
                .OrderBy(p => p.FSHOW_SEQNO).ToList();

            var schCraft = woCrafts.FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == woData.FWORK_ORDER_CRAFT_ID);

            if (schCraft == null)
            {
                result.Message = _multiLang["执行失败, 排程任务 {0}, 对应工艺信息不存在于工单工艺列表."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            if (woCrafts.IndexOf(schCraft) > 0)
            {
                var preWoCraft = woCrafts[woCrafts.IndexOf(schCraft) - 1];
                var schJobs = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_JOB_BOOKING>
                    ((sch, job) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == job.FCRAFT_SCHEDULE_ID))
                    .Where((sch, job) => sch.FWORK_ORDER_CRAFT_ID == preWoCraft.FWORK_ORDER_CRAFT_ID)
                    .Select((sch, job) => new
                    {
                        sch.FCRAFT_SCHEDULE_NO,
                        job.FCRAFT_JOB_BOOKING_NO,
                        job.FWORK_STATUS,
                        job.FPASS_QTY,
                        job.FNG_QTY,
                    })
                    .ToListAsync();

                //1-检查前一工艺有完工/部份完工记录
                if (checkPre == "1")
                {
                    if (!(schJobs.Any(p => p.FWORK_STATUS == WorkStatus.finished.ToString() || p.FNG_QTY + p.FPASS_QTY > 0)))
                    {
                        string craft = string.Empty;
                        var craftResult = await GetCraftByIdsAsync(new List<string> { preWoCraft.FCRAFT_ID });
                        if (craftResult.StatusCode == 200 && craftResult.Entity.Count > 0)
                        {
                            var entity = craftResult.Entity[0];
                            craft = entity.FCRAFT_NAME;
                        }
                        var schNos = string.Join(",", schJobs.Select(p => p.FCRAFT_SCHEDULE_NO).Distinct().ToList());
                        result.Message = string.Format(_multiLang["执行失败, 上一工艺 {0} 未完工, 对应排程任务 {1}"], craft, schNos);
                        result.StatusCode = 1010;
                        return await OK(result);
                    }
                }
                else if (checkPre == "0")  //0-检查前一工艺有开工记录
                {

                    if (!(schJobs.Any(p => p.FWORK_STATUS == WorkStatus.working.ToString() || p.FWORK_STATUS == WorkStatus.finished.ToString()
                        || p.FWORK_STATUS == WorkStatus.partfinished.ToString() || p.FWORK_STATUS == WorkStatus.paused.ToString())))
                    {
                        string craft = string.Empty;
                        var craftResult = await GetCraftByIdsAsync(new List<string> { preWoCraft.FCRAFT_ID });
                        if (craftResult.StatusCode == 200 && craftResult.Entity.Count > 0)
                        {
                            var entity = craftResult.Entity[0];
                            craft = entity.FCRAFT_NAME;
                        }
                        var schNos = string.Join(",", schJobs.Select(p => p.FCRAFT_SCHEDULE_NO).Distinct().ToList());
                        result.Message = string.Format(_multiLang["执行失败, 上一工艺 {0} 未开工, 对应排程任务 {1}"], craft, schNos);
                        result.StatusCode = 1010;
                        return await OK(result);
                    }
                }

            }

            return await OK(result);

        }
        #endregion

        #region 取消开工
        /// <summary>
        /// 取消开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<CraftJobBookingModel>> CancelJobAsync(JobBookingOperateModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            //取消开工验证
            var validate = await ValidateCancelJobAsync(model);
            if (validate.StatusCode != 200)
            {
                ERROR(null, 103001, _multiLang["取消开工验证失败.错误信息:" + validate.Message]);
            }

            //string empId = user.UserPsnId;
            //string empNm = user.UserPsnName;

            var db = _isugar.DB;

            T_MESD_CRAFT_JOB_BOOKING daoJob = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                        .Where(p => p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                        //.Select(p => new T_MESD_CRAFT_JOB_BOOKING
                        //{
                        //    FACT_ST_DATE = p.FACT_ST_DATE,
                        //    FCRAFT_ID = p.FCRAFT_ID,

                        //    FSALE_ORDER_ID = p.FSALE_ORDER_ID,
                        //    FSTATION_ID = p.FSTATION_ID,

                        //    FCRAFT_JOB_BOOKING_ID = p.FCRAFT_JOB_BOOKING_ID,
                        //    FCRAFT_JOB_BOOKING_NO = p.FCRAFT_JOB_BOOKING_NO,

                        //    FEMP_ID = p.FEMP_ID,
                        //    FEMP_NAME = p.FEMP_NAME,

                        //    FWORK_ORDER_CRAFT_ID = p.FWORK_ORDER_CRAFT_ID,

                        //    FWORK_ORDER_ID = p.FWORK_ORDER_ID,
                        //    FWORK_STATUS = p.FWORK_STATUS,

                        //    FCRAFT_SCHEDULE_ID = p.FCRAFT_SCHEDULE_ID,
                        //    FMATERIAL_ID = p.FMATERIAL_ID,

                        //    FECODE = p.FECODE,
                        //})
                        .FirstAsync();

            daoJob.FWORK_STATUS = WorkStatus.cancel.ToString();

            db = _isugar.DB;
            try
            {
                db.BeginTran();

                //更新加工任务
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(daoJob)
                    .UpdateColumns(p => new { p.FWORK_STATUS })
                    .ExecuteCommandAsync();

                //更新排程实际开工时间
                await UpdateScheduleStartDateAsync(daoJob, db);

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }

            //保存日志
            Dictionary<string, string> logData = new Dictionary<string, string>();
            logData.Add("FCRAFT_JOB_BOOKING_ID", daoJob.FCRAFT_JOB_BOOKING_ID);
            logData.Add("FCRAFT_JOB_BOOKING_NO", daoJob.FCRAFT_JOB_BOOKING_NO);

            logData.Add("FCRAFT_ID", daoJob.FCRAFT_ID);
            logData.Add("FSTATION_ID", daoJob.FSTATION_ID);

            logData.Add("FEMP_ID", daoJob.FEMP_ID);
            logData.Add("FEMP_NAME", daoJob.FEMP_NAME);

            logData.Add("FACT_ST_DATE", daoJob.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            logData.Add("FMATERIAL_ID", daoJob.FMATERIAL_ID);

            logData.Add("FWORK_ORDER_ID", daoJob.FWORK_ORDER_ID);
            logData.Add("FCRAFT_SCHEDULE_ID", daoJob.FCRAFT_SCHEDULE_ID);

            logData.Add("FWORK_STATUS", daoJob.FWORK_STATUS);

            _ = SaveOperateLogAsync(daoJob, OperateType.cancel, _serialize.Serialize(logData));

            //返回加工任务
            var jobBooking = await GetJobBookingByIdAsync(daoJob.FCRAFT_JOB_BOOKING_ID);
            DataResult<CraftJobBookingModel> result = new DataResult<CraftJobBookingModel>()
            {
                Entity = jobBooking,
                StatusCode = 200,
            };
            // SendMsg();//发送消息通知
            return await OK(result);
        }

        /// <summary>
        ///  验证取消开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<JobBookingOperateModel>> ValidateCancelJobAsync(JobBookingOperateModel model)
        {
            var result = new DataResult<JobBookingOperateModel>();
            result.StatusCode = 200;
            if (model == null)
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model");
                result.StatusCode = 1010;
                return await OK(result);
            }

            if (string.IsNullOrWhiteSpace(model.FCRAFT_JOB_BOOKING_ID))
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model.FCRAFT_JOB_BOOKING_ID");
                result.StatusCode = 1010;
                return await OK(result);
            }

            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                result.Message = _multiLang["执行失败, 当前用户员工信息为空."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            //加工任务
            var jobData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE>
                ((job, sch) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID))
                .Where((job, sch) => job.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                .Select((job, sch) => new
                {
                    job.FCRAFT_JOB_BOOKING_ID,
                    job.FCRAFT_JOB_BOOKING_NO,
                    job.FWORK_STATUS,
                    job.FEMP_ID,
                    job.FEMP_NAME,
                    sch.FCRAFT_SCHEDULE_NO,
                    job.FPASS_QTY,
                    job.FNG_QTY,
                })
                .FirstAsync();
            if (jobData == null)
            {
                result.Message = _multiLang["加工任务数据不存在, 请查正."];
                result.StatusCode = 1010;
                return await OK(result);
            }
            //暂时放开，后面做参数控制
            /*
            //只能由本人取消开工
            if (jobData.FEMP_ID != user.UserPsnId)
            {
                ERROR(null, 102006, string.Format(_multiLang["执行失败, 加工任务 {0}, 是由{1}开工的, 请通知本人操作."],
                 jobData.FCRAFT_JOB_BOOKING_NO, jobData.FEMP_NAME));
            }
            */

            //检查状态
            if (jobData.FWORK_STATUS == WorkStatus.cancel.ToString())
            {
                result.Message = string.Format(_multiLang["执行失败, 加工任务 {0}, 已取消加工."], jobData.FCRAFT_JOB_BOOKING_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            if (jobData.FWORK_STATUS == WorkStatus.finished.ToString())
            {
                result.Message = string.Format(_multiLang["执行失败, 加工任务 {0}, 已完工."], jobData.FCRAFT_JOB_BOOKING_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            if (jobData.FNG_QTY + jobData.FPASS_QTY > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 加工任务 {0}, 已部份完工."], jobData.FCRAFT_JOB_BOOKING_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            return await OK(result);
        }

        #endregion



        /// <summary>
        /// 完工申报---设备完工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<bool>> EquFinishJobAsync(string id)
        {
            var db = _isugar.DB;

            var equdata = await db.Queryable<F_EQU_DATA>().Where(p => p.F_EQUID == id).FirstAsync();

            var jobstation = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => p.FSTATION_ID == equdata.FSTATION_ID && p.FWORK_STATUS == "working").FirstAsync();

            if (jobstation != null)
            {
                var jobBookingOperateModel = new JobBookingOperateModel();
                jobBookingOperateModel.FCRAFT_JOB_BOOKING_ID = jobstation.FCRAFT_JOB_BOOKING_ID;
                jobBookingOperateModel.FCRAFT_SCHEDULE_ID = jobstation.FCRAFT_SCHEDULE_ID;
                jobBookingOperateModel.OperateType = OperateType.partfinish;
                jobBookingOperateModel.FPASS_QTY = equdata.F_WEIGHT.Value;
                jobBookingOperateModel.FSTATION_ID = equdata.FSTATION_ID;
                jobBookingOperateModel.FEMP_NAME = jobstation.FEMP_NAME;
                jobBookingOperateModel.FEMP_ID = jobstation.FEMP_ID;

                var dataresult = await FinishJobAsync(jobBookingOperateModel);

                //更新
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>()
                .SetColumns(p => new T_MESD_CRAFT_JOB_BOOKING()
                {
                    F_EQUID = id,
                }
                )
               .Where(p => p.FCRAFT_JOB_BOOKING_ID == jobBookingOperateModel.FCRAFT_JOB_BOOKING_ID)
               .ExecuteCommandAsync();

                //没有未作废的 就增加卷次数
                await db.Updateable<F_EQU_DATA>()
                .SetColumns(p => new F_EQU_DATA()
                {
                    FCRAFT_JOB_BOOKING_ID = jobBookingOperateModel.FCRAFT_JOB_BOOKING_ID,
                }
                )
               .Where(p => p.F_EQUID == id)
               .ExecuteCommandAsync();
            }
            return new DataResult<bool>() { StatusCode = 200, Entity = true };
        }



        /// <summary>
        /// 处理卷次数
        /// </summary>
        /// <param name="FCRAFT_JOB_BOOKING_ID"></param>
        /// <returns></returns>
        public async Task<DataResult<bool>> DealJuanShu(string FCRAFT_JOB_BOOKING_ID)
        {
            var db = _isugar.DB;

            var job = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => p.FCRAFT_JOB_BOOKING_ID == FCRAFT_JOB_BOOKING_ID).FirstAsync();

            //处理属于第几卷
            var finishJobs = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => p.FCRAFT_SCHEDULE_ID == job.FCRAFT_SCHEDULE_ID && p.FWORK_STATUS == "finished").ToListAsync();

            //检查是否有作废得卷次数
            var juanzuofeijob = finishJobs.Where(p => p.FJUAN_STATUS == 1).OrderBy(p => p.FDI_JI_JUAN).FirstOrDefault();

            //检查是否有作废得卷次数
            //var yichulijuan = finishJobs.Where(p => p.FJUAN_STATUS== 0).OrderBy(p=>p.FDI_JI_JUAN).LastOrDefault();
            //var dijijuan = yichulijuan.FDI_JI_JUAN+1;


            var yichulijuan = finishJobs.Where(p => p.FJUAN_STATUS != 0).Count();
            var dijijuan = finishJobs.Count - yichulijuan;//需要减去作废的卷数

            try
            {
                db.BeginTran();

                if (juanzuofeijob != null)
                {
                    //有作废得第几卷次数 就重新用
                    dijijuan = juanzuofeijob.FDI_JI_JUAN;
                    //没有未作废的 就增加卷次数 把作废的卷改为已使用
                    await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>().SetColumns(p => p.FJUAN_STATUS == 2).Where(p => p.FCRAFT_JOB_BOOKING_ID == juanzuofeijob.FCRAFT_JOB_BOOKING_ID).ExecuteCommandAsync();
                }
                //没有未作废的 就增加卷次数
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>()
                .SetColumns(p => new T_MESD_CRAFT_JOB_BOOKING()
                {
                    FDI_JI_JUAN = dijijuan,
                    F_STATUS = 1,
                }
                )
               .Where(p => p.FCRAFT_JOB_BOOKING_ID == FCRAFT_JOB_BOOKING_ID)
               .ExecuteCommandAsync();

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();

                throw;
            }
            return new DataResult<bool>() { StatusCode = 200, Entity = true };
        }



        #region 完工申报
        /// <summary>
        /// 完工申报 - 不包含拆分
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<CraftJobBookingModel>> FinishJobAsync(JobBookingOperateModel model)
        {
            var user = await _iauth.GetUserAccountAsync();


            //验证完工
            var validate = await ValidateFinishJobAsync(model);
            if (validate.StatusCode != 200)
            {
                ERROR(null, 103001, _multiLang["验证完工申报失败.错误信息:" + validate.Message]);
            }

            // 若启用了参数数：品质管理，且不良数量大于0，则必须选择：不良原因、检验员工。
            await CheckQcAsync(model);

            var db = _isugar.DB;


            //查询原数据行
            var daoJob = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(p => p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                .Select<T_MESD_CRAFT_JOB_BOOKING>()
                .FirstAsync();

            ////如果部份完工, 本次完工数量+本次不良数量大于等于未完工数量, 则当成完工            
            if (model.OperateType == OperateType.partfinish)
            {
                ///判断是否设置完工超数
                var mesFinishQtyAllowGreatSch = await GetSysParamValue(_MES_FinishQtyAllowGreatSch);
                var schData = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                    ((sch, schsts) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schsts.FCRAFT_SCHEDULE_ID))
                    .Where((sch, schsts) => sch.FCRAFT_SCHEDULE_ID == daoJob.FCRAFT_SCHEDULE_ID)
                    .Select((sch, schsts) => new { sch.FPLAN_QTY, schsts.FFINISH_QTY })
                    .FirstAsync();

                if (model.FPASS_QTY + model.FNG_QTY >= schData.FPLAN_QTY - schData.FFINISH_QTY)
                {
                    model.OperateType = OperateType.finish;
                }

            }



            //类型为全检类型（根据内定数据字典）
            model.FCHK_TYPE = "full";
            model.FCREATOR = user.UserPsnName;
            model.FCREATOR_ID = user.UserId;
            model.FCDATE = _iauth.GetCurDateTime();
            //不良主表
            var daoMt = new T_MESD_CRAFT_JOB_BOOKING_QC_MT();
            //不良明细
            var daoBad = new List<T_MESD_CRAFT_JOB_BOOKING_BAD>();
            //图片附件
            var daoAtt = new List<T_MESD_CRAFT_JOB_BOOKING_QC_ATTACH>();

            //取出不良明细
            if (model.BadList.Count > 0)
            {


                if (string.IsNullOrWhiteSpace(model.FCRAFT_JOB_BOOKING_QC_MT_ID))
                {

                    model.FCRAFT_JOB_BOOKING_QC_MT_ID = GuidHelper.NewGuid();

                    //自动产生单号
                    if (string.IsNullOrWhiteSpace(model.FCRAFT_JOB_BOOKING_QC_MT_NO))
                    {
                        if (!await GenQcNoAsync(model))
                        {
                            ERROR(null, 100051, _multiLang["生成报工质检单号错误."]);
                        }
                    }

                    //新增
                    //检查单号是否已存在
                    var isExists = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING_QC_MT>().Where(p => p.FCRAFT_JOB_BOOKING_QC_MT_NO == model.FCRAFT_JOB_BOOKING_QC_MT_NO).AnyAsync();
                    if (isExists)
                    {
                        ERROR(null, 100062, string.Format(_multiLang["不良报工单号 {0} 已存在, 不能重复."], model.FCRAFT_JOB_BOOKING_QC_MT_NO));
                    }
                }
                //主表设定dao实体属性值
                daoMt = model.MapToDestObj<JobBookingOperateModel, T_MESD_CRAFT_JOB_BOOKING_QC_MT>();
                daoMt.FNG_PCS = '0';
                daoMt.FCREATOR_ID = user.UserId;
                daoMt.FCREATOR = user.UserPsnName;
                daoMt.FCDATE = _iauth.GetCurDateTime();
                daoMt.FECODE = "1";
                daoMt.FCHECK_QC_DATE = _iauth.GetCurDateTime();


                model.BadList.ForEach(item =>
                {
                    //不良表
                    item.FCRAFT_JOB_BOOKING_ID = model.FCRAFT_JOB_BOOKING_ID;
                    item.FCRAFT_SCHEDULE_ID = model.FCRAFT_SCHEDULE_ID;
                    item.FCRAFT_JOB_BOOKING_QC_MT_ID = model.FCRAFT_JOB_BOOKING_QC_MT_ID;

                    //图片附件
                    if (item.AttachList != null && item.AttachList.Count > 0)
                    {
                        item.AttachList.ForEach(att =>
                        {
                            // 细表附件
                            att.FCRAFT_JOB_BOOKING_QC_MT_ID = model.FCRAFT_JOB_BOOKING_QC_MT_ID;
                            att.FCREATOR_ID = user.UserId;
                            att.FCREATOR = user.UserPsnName;
                            att.FCDATE = _iauth.GetCurDateTime();
                            var addAtt = att.MapToDestObj<CraftJobBookingQcAttachModel, T_MESD_CRAFT_JOB_BOOKING_QC_ATTACH>();
                            daoAtt.Add(addAtt);
                        });
                    }
                });

                //不良明细
                daoBad = model.BadList.MapToDestObj<CraftJobBookingQcBadModel, T_MESD_CRAFT_JOB_BOOKING_BAD>();
            }







            //设定dao实体属性值 
            DateTime curDate = _iauth.GetCurDateTime();

            daoJob.FACT_ED_DATE = curDate;
            daoJob.FEMP_ID = user.UserPsnId;
            daoJob.FEMP_NAME = user.UserPsnName;
            //如果传上来的存在 就使用传上来的
            if (!string.IsNullOrEmpty(model.FEMP_ID))
            {
                daoJob.FEMP_ID = model.FEMP_ID;
                daoJob.FEMP_NAME = model.FEMP_NAME;
            }

            //File.AppendAllLines("aaaaaa.txt", new List<string>() { $"{model.FCRAFT_JOB_BOOKING_ID}==={model.FEMP_ID}——{model.FEMP_NAME}--job-{daoJob.FEMP_ID}--{daoJob.FEMP_NAME}" });

            daoJob.FNG_QTY += model.FNG_QTY;
            daoJob.FPASS_QTY += model.FPASS_QTY;

            daoJob.FNG_WEIGHT += model.FNG_WEIGHT;
            daoJob.FPASS_WEIGHT += model.FPASS_WEIGHT;

            //顺序不能与下句调换
            daoJob.FACT_USE_HOUR = CalcActUseHour(daoJob.FACT_USE_HOUR, daoJob.FLAST_RESUME_DATE, daoJob.FACT_ST_DATE, curDate, daoJob.FWORK_STATUS);

            //部份完工当成完工,并生成新的加工任务
            daoJob.FWORK_STATUS = WorkStatus.finished.ToString();

            // 品质字段
            daoJob.FBAD_REASON_ID = model.FBAD_REASON_ID;
            daoJob.FCHECK_PERSON_ID = model.FCHECK_PERSON_ID;

            db = _isugar.DB;

            var craftInfo = await db.Queryable<T_MESM_CRAFT>().Where(p => p.FCRAFT_ID == daoJob.FCRAFT_ID).FirstAsync();

            T_MESD_CRAFT_JOB_BOOKING_QC daoQCjob = null;
            if (craftInfo.FIF_QC)
            {
                if (daoJob.FNG_QTY > 0)
                {
                    daoQCjob = daoJob.MapToDestObj<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_JOB_BOOKING_QC>();

                    daoQCjob.FPASS_QTY = 0;
                    daoQCjob.FPASS_WEIGHT = 0;

                    daoJob.FNG_QTY = 0;
                    daoJob.FNG_WEIGHT = 0;
                }
            }


            try
            {
                db.BeginTran();

                if (daoQCjob != null)
                {
                    await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_QC>(daoQCjob).ExecuteCommandAsync();
                }


                //不良主表
                if (daoMt != null && !string.IsNullOrWhiteSpace(daoMt.FCRAFT_JOB_BOOKING_QC_MT_ID))
                {
                    await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_QC_MT>(daoMt).IgnoreColumns(p => new { p.FMODIFIER, p.FMODIFIER_ID, p.FMODIDATE })
                        .ExecuteCommandAsync();
                }
                //不良表
                if (daoBad != null && daoBad.Count > 0)
                {
                    await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_BAD>(daoBad).IgnoreColumns(p => new { p.FMODIFIER, p.FMODIFIER_ID, p.FMODIDATE })
                        .ExecuteCommandAsync();
                }
                //附件表
                if (daoAtt != null && daoAtt.Count > 0)
                {
                    await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_QC_ATTACH>(daoAtt).IgnoreColumns(p => new { p.FMODIFIER, p.FMODIFIER_ID, p.FMODIDATE })
                        .ExecuteCommandAsync();
                }


                //更新加工任务字段
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(daoJob)
                    .UpdateColumns(p => new
                    {
                        p.FACT_ED_DATE,
                        p.FEMP_ID,
                        p.FEMP_NAME,
                        p.FNG_QTY,
                        p.FPASS_QTY,
                        p.FNG_WEIGHT,
                        p.FPASS_WEIGHT,
                        p.FACT_USE_HOUR,
                        p.FWORK_STATUS,
                        // 品质字段
                        p.FBAD_REASON_ID,
                        p.FCHECK_PERSON_ID,
                    })
                    .ExecuteCommandAsync();

                //更新排程完工时间,完工数量,排程结案
                await UpdateSchedulesFinishAsync(new List<string> { daoJob.FCRAFT_SCHEDULE_ID }, db, user, curDate);

                //更新工单工艺完工数
                await UpdateWorkOrderCraftsFinishAsync(new List<string> { daoJob.FWORK_ORDER_CRAFT_ID }, db);

                db.CommitTran();
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }

            //移到事务外, 保存,根据参数审核过账
            //检查是否最后一道排程,调用生成入库单
            await GenrateWorkOrderInAsync(new List<T_MESD_CRAFT_JOB_BOOKING> { daoJob });

            //处理卷数量
            await DealJuanShu(daoJob.FCRAFT_JOB_BOOKING_ID);

            //保存日志
            Dictionary<string, string> logData = new Dictionary<string, string>();
            logData.Add("FCRAFT_JOB_BOOKING_ID", daoJob.FCRAFT_JOB_BOOKING_ID);
            logData.Add("FCRAFT_JOB_BOOKING_NO", daoJob.FCRAFT_JOB_BOOKING_NO);
            logData.Add("FCRAFT_ID", daoJob.FCRAFT_ID);
            logData.Add("FSTATION_ID", daoJob.FSTATION_ID);
            logData.Add("FEMP_ID", daoJob.FEMP_ID);
            logData.Add("FEMP_NAME", daoJob.FEMP_NAME);
            logData.Add("FACT_ST_DATE", daoJob.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            if (daoJob.FACT_ED_DATE != null)
            {
                logData.Add("FACT_ED_DATE", daoJob.FACT_ED_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            logData.Add("FMATERIAL_ID", daoJob.FMATERIAL_ID);
            logData.Add("FACT_USE_HOUR", daoJob.FACT_USE_HOUR.ToString());
            logData.Add("FNG_QTY", daoJob.FNG_QTY.ToString());
            logData.Add("FPASS_QTY", daoJob.FPASS_QTY.ToString());
            logData.Add("FWORK_ORDER_ID", daoJob.FWORK_ORDER_ID);
            logData.Add("FCRAFT_SCHEDULE_ID", daoJob.FCRAFT_SCHEDULE_ID);
            logData.Add("FWORK_STATUS", daoJob.FWORK_STATUS);
            _ = SaveOperateLogAsync(daoJob, model.OperateType, _serialize.Serialize(logData));

            DataResult<CraftJobBookingModel> result = new DataResult<CraftJobBookingModel>()
            {
                Entity = null,
                StatusCode = 200,
            };
            // 操作类型 等于 部分完工 并且 当前状态不是完工
            if (model.OperateType == OperateType.partfinish)
            {
                //部份完工，重新开工
                JobBookingOperateModel model2 = _serialize.Deserialize<string, JobBookingOperateModel>(_serialize.Serialize(model));

                model2.OperateType = OperateType.start;
                model2.FCRAFT_JOB_BOOKING_ID = string.Empty;
                model2.FCRAFT_SCHEDULE_ID = daoJob.FCRAFT_SCHEDULE_ID;
                model2.FSTATION_ID = daoJob.FSTATION_ID;

                model2.FNG_QTY = 0;
                model2.FNG_WEIGHT = 0;
                model2.FPASS_QTY = 0;
                model2.FPASS_WEIGHT = 0;

                result = await StartJobAsync(model2);
            }
            else
            {
                // 完工，返回加工任务
                var jobBooking = await GetJobBookingByIdAsync(daoJob.FCRAFT_JOB_BOOKING_ID);
                result.Entity = jobBooking;

                await DeleteWorkJobBooKingAsync(daoJob.FCRAFT_SCHEDULE_ID);
                // SendMsg();//发送消息通知
            }

            return await OK(result);
        }



        /// <summary>
        /// 生成单号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<bool> GenQcNoAsync(JobBookingOperateModel model)
        {
            GenBillCodeModel billModel = new GenBillCodeModel()
            {
                FBILL_COUNT = 1,
                FBILL_RULE_CODE = "BR-CPF-0103",
                FENTITY_DESC = "不良报工",
                FENTITY_NAME = "CraftJobBookingQcMtModel",
                FFIELD_DESC = "工艺加工质检任务主表单号",
                FFIELD_NAME = "FCRAFT_JOB_BOOKING_QC_MT_NO",
                Parms = new List<GenBillCodeParmModel> { }
            };
            billModel.Parms.Add(new GenBillCodeParmModel
            {
                FPARM_CODE = "Date",
                FPARM_VALUE = _iauth.GetCurDateTime().ToString("yyyyMMdd"),
            });

            var billResult = await _commonDataProvider.GenEntBillCodeAsync(billModel);
            if (billResult == null || billResult.Count == 0 || string.IsNullOrWhiteSpace(billResult[0]))
            {
                ERROR(null, 100600, _multiLang["编码规则生成样品订单编号为空, 需检查系统编码配置."]);
            }
            model.FCRAFT_JOB_BOOKING_QC_MT_NO = billResult[0];

            return await OK(true);
        }


        /// <summary>
        /// 发送通知调用--悠悠发送
        /// </summary>
        /// <returns></returns>
        public async Task SendMsg()
        {
            var wss = await _ThirdPartySetting.GetSettingParmByCodeAsync<JObject>("wss");

            if (wss == null)
            {
                return;
            }

            if (wss.ContainsKey("open") && wss.ContainsKey("ipport"))
            {
                if (wss["open"].ToString() == "true")
                {
                    _httpClient.CreateClient("a").GetAsync($"http://{wss["ipport"].ToString()}/send?msg=1");
                }
            }

        }


        /// <summary>
        /// 发送通知调用---博顺
        /// </summary>
        /// <returns></returns>
        public async Task SendMsg(string FCRAFT_JOB_BOOKING_ID)
        {
            var wss = await _ThirdPartySetting.GetSettingParmByCodeAsync<JObject>("wss");

            if (wss == null)
            {
                return;
            }

            if (wss.ContainsKey("open") && wss.ContainsKey("bsipport"))
            {
                if (wss["open"].ToString() == "true")
                {
                    _httpClient.CreateClient("a").GetAsync($"http://{wss["bsipport"].ToString()}/send?msg={FCRAFT_JOB_BOOKING_ID}");
                }

            }


        }


        /// <summary>
        /// 根据排程任务ID生成内外箱标签
        /// </summary>
        /// <returns></returns>
        public async Task<bool> CreatePackBarCodeAsync(string FCRAFT_SCHEDULE_ID)
        {
            var db = _isugar.DB;
            var _mainDB = _isugar.GetDB("MAIN");
            var user = await _iauth.GetUserAccountAsync();
            var curDate = _iauth.GetCurDateTime();
            try
            {
                // 步骤1：查询指定工艺进度单的基本信息
                var craftScheduleInfo = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER, T_MESM_CRAFT>(
                        (sch, schStatus, wo, craft) => new JoinQueryInfos(
                            JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                            JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                            JoinType.Left, sch.FCRAFT_ID == craft.FCRAFT_ID
                        ))
                    .Where((sch, schStatus, wo, craft) => sch.FCRAFT_SCHEDULE_ID == FCRAFT_SCHEDULE_ID)
                    .Select((sch, schStatus, wo, craft) => new
                    {
                        sch.FCRAFT_SCHEDULE_NO,
                        sch.FCRAFT_SCHEDULE_ID,
                        schStatus.FFINISH_QTY,
                        wo.FMATERIAL_ID,
                        craft.FIF_TAG,
                        craft.FTAG_TYPE,
                        schStatus.FIS_TAG_GENERATE
                    })
                    .FirstAsync();

                // 步骤2：查询该工艺进度单下已完成工作的合格和不合格数量
                var jobBookings = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                    .Where(x => x.FCRAFT_SCHEDULE_ID == FCRAFT_SCHEDULE_ID && x.FWORK_STATUS == "finished")
                    .ToListAsync();

                // 步骤3：在内存中计算总数量
                decimal totalPassQty = jobBookings.Sum(x => x.FPASS_QTY);
                decimal totalNgQty = jobBookings.Sum(x => x.FNG_QTY);
                decimal totalJobQty = totalPassQty + totalNgQty;

                // 步骤4：组合最终结果
                var result = new
                {
                    craftScheduleInfo.FCRAFT_SCHEDULE_NO,
                    craftScheduleInfo.FCRAFT_SCHEDULE_ID,
                    craftScheduleInfo.FFINISH_QTY,
                    craftScheduleInfo.FMATERIAL_ID,
                    craftScheduleInfo.FIF_TAG,
                    craftScheduleInfo.FTAG_TYPE,
                    craftScheduleInfo.FIS_TAG_GENERATE,
                    TOTAL_JOB_QTY = totalJobQty,
                    IS_FINISHED = totalJobQty >= craftScheduleInfo.FFINISH_QTY ? 1 : 0
                };
                if (result == null)
                {

                    ERROR(null, 100100, "该排程未完工无法打印内外箱标签！");
                }

                if (result.FIF_TAG != true)
                {
                    ERROR(null, 100101, "该工艺未启用标签打印！");

                }
                if (result.FTAG_TYPE.ToUpper() != "PACK")
                {
                    ERROR(null, 100102, "该工艺未设置对应的包箱标签打印类型！");

                }
                if (result.FIS_TAG_GENERATE == true)
                {
                    ERROR(null, 100002, "该排程已生成包箱条码，无法重复生成！");
                }
                //根据物料ID查询物料信息 
                var materialPack = await _mainDB.Queryable<T_MSDM_MATERIAL, T_MSDM_MATERIAL_PAKSPEC>((a, b) => new JoinQueryInfos(JoinType.Left, a.FMATERIAL_ID == b.FMATERIAL_ID))
                    .Where((a, b) => a.FMATERIAL_ID == result.FMATERIAL_ID && b.FIS_DEFAULT_PACKAGE == true)
                    .Select((a, b) => new { b.FINNER_QTY, b.FOUTER_QTY, a.FUNIT_WEIGHT, a.FMATERIAL_CODE })
                    .FirstAsync();
                if (materialPack == null)
                {

                    ERROR(null, 100103, "物料包装方式未设置，或者未设置未默认包装方式！");
                }

                if (materialPack.FINNER_QTY <= 0 || materialPack.FOUTER_QTY <= 0)
                {

                    ERROR(null, 100104, "物料包装未配置对应的内包装数和外包装数！");
                }
                //生成对应的内箱标签(result.FFINISH_QTY/materialPack.FINNER_QTY)
                //生成对应的内箱标签(result.FFINISH_QTY/(materialPack.FINNER_QTY*materialPack.FOUTER_QTY))
                decimal totalQty = result.FFINISH_QTY;
                int innerQty = materialPack.FINNER_QTY;
                int outerQty = innerQty * materialPack.FOUTER_QTY;

                int innerBoxCount = (int)Math.Ceiling(totalQty / innerQty);
                int outerBoxCount = (int)Math.Ceiling(totalQty / outerQty);

                decimal innerTailQty = totalQty % innerQty;
                decimal outerTailQty = totalQty % outerQty;

                var NewTagList = new List<T_MESD_CRAFT_JOB_BOOKING_TAG>();
                int innerSeq = 1;
                int outerSeq = 1;
                //DateTime curDate = DateTime.Now;

                // 内箱标签生成
                for (int i = 0; i < innerBoxCount; i++)
                {
                    bool isTail = (i == innerBoxCount - 1) && innerTailQty > 0;
                    decimal qty = isTail ? innerTailQty : innerQty;

                    NewTagList.Add(new T_MESD_CRAFT_JOB_BOOKING_TAG
                    {
                        FCRAFT_JOB_BOOKING_TAG_ID = GuidHelper.NewGuid(),
                        //FCRAFT_JOB_BOOKING_ID = model.FCRAFT_JOB_BOOKING_ID,
                        FCRAFT_SCHEDULE_ID = result.FCRAFT_SCHEDULE_ID,
                        // 数量类
                        FPRODUCT_NUM = qty,
                        FPRODUCT_WEIGHT = 0,             // 如果你有重量信息可以替换
                        FGROSS_WEIGHT = qty * materialPack.FUNIT_WEIGHT,
                        // 条码、批次、文本
                        FSTUFFLOT_NO = "",               // 如有可传入
                        FBARCODE_NO = null,              // 先留空，生成后再填
                        FTEXT1 = "",                     // 可扩展
                        FTEXT2 = "",
                        FTEXT3 = "",
                        FTEXT4 = "",
                        FTEXT5 = "",
                        FSHOW_SEQNO = (innerSeq++).ToString("D3"),
                        FTAG_TYPE = "PACK",              // 内箱标签类型
                        FPACK_TYPE = "ITEM",              // 内箱标签类型
                        FIS_TAIL_BOX = isTail,        // 是否为尾箱
                        // 通用字段
                        FCREATOR_ID = user.UserId,
                        FCREATOR = user.UserPsnName,
                        FCDATE = DateTime.Now,
                        FECODE = user.CompanyId
                    });
                }
                // 外箱标签生成
                for (int i = 0; i < outerBoxCount; i++)
                {
                    bool isTail = (i == outerBoxCount - 1) && outerTailQty > 0;
                    decimal qty = isTail ? outerTailQty : outerQty;
                    NewTagList.Add(new T_MESD_CRAFT_JOB_BOOKING_TAG
                    {
                        FCRAFT_JOB_BOOKING_TAG_ID = GuidHelper.NewGuid(),
                        //FCRAFT_JOB_BOOKING_ID = model.FCRAFT_JOB_BOOKING_ID,
                        FCRAFT_SCHEDULE_ID = result.FCRAFT_SCHEDULE_ID,
                        FPRODUCT_NUM = qty,
                        FPRODUCT_WEIGHT = qty * materialPack.FUNIT_WEIGHT,
                        FGROSS_WEIGHT = 0,
                        FSTUFFLOT_NO = "",               // 可传
                        FBARCODE_NO = null,              // 待生成
                        FTEXT1 = "",
                        FTEXT2 = "",
                        FTEXT3 = "",
                        FTEXT4 = "",
                        FTEXT5 = "",
                        FSHOW_SEQNO = (outerSeq++).ToString("D3"),
                        FTAG_TYPE = "PACK",              // 外箱标签类型
                        FPACK_TYPE = "PACK",
                        FIS_TAIL_BOX = isTail,
                        FCREATOR_ID = user.UserId,
                        FCREATOR = user.UserPsnName,
                        FCDATE = DateTime.Now,
                        FECODE = user.CompanyId

                    });
                }
                var _genlist = new List<TagGenModel>();
                foreach (var item in NewTagList)
                {

                    _genlist.Add(new TagGenModel { GEN_NO = materialPack.FMATERIAL_CODE, FBILL_SOURCE_ITEM_ID = item.FCRAFT_JOB_BOOKING_TAG_ID, FBILL_SOURCE_NO = result.FCRAFT_SCHEDULE_NO, FSTK_QTY = item.FPRODUCT_NUM, FBILL_TYPE = $"FCRAFT_SCHEDULE_{item.FPACK_TYPE}".ToUpper() });
                }
                var rpcServer = this.GetService<IMES017TagService>("MES017Tag");
                var TagList = await rpcServer.GenerateOrderCodeList(_genlist);
                if (TagList.StatusCode != 200)
                {
                    ERROR(null, TagList.StatusCode, TagList.Message);
                }
                db.BeginTran();
                NewTagList.ForEach(x => x.FBARCODE_NO = TagList.Entity.FirstOrDefault(n => n.FBILL_SOURCE_ITEM_ID == x.FCRAFT_JOB_BOOKING_TAG_ID)?.FBARCODE_NO);
                await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_TAG>(NewTagList).ExecuteCommandAsync();
                await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>().SetColumns(p => new T_MESD_CRAFT_SCHEDULE_STATUS() { FIS_TAG_GENERATE = true })
                .Where(p => p.FCRAFT_SCHEDULE_ID == FCRAFT_SCHEDULE_ID)
                .ExecuteCommandAsync();
                db.CommitTran();
                return true;
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }

        }


        /// <summary>
        /// 根据排程ID获取内外箱标签
        /// </summary>
        /// <param name="FCRAFT_SCHEDULE_ID"></param>
        /// <returns></returns>
        public async Task<PackInfoModel> GetPackInfoAsync(string FCRAFT_SCHEDULE_ID)
        {

            var db = _isugar.DB;
            var _mainDB = _isugar.GetDB("MAIN");
            var user = await _iauth.GetUserAccountAsync();
            var curDate = _iauth.GetCurDateTime();
            try
            {
                var craftScheduleInfo = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER, T_MESM_CRAFT>(
                       (sch, schStatus, wo, craft) => new JoinQueryInfos(
                           JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                           JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                           JoinType.Left, sch.FCRAFT_ID == craft.FCRAFT_ID
                       ))
                   .Where((sch, schStatus, wo, craft) => sch.FCRAFT_SCHEDULE_ID == FCRAFT_SCHEDULE_ID)
                   .Select((sch, schStatus, wo, craft) => new
                   {
                       sch.FCRAFT_SCHEDULE_NO,
                       sch.FCRAFT_SCHEDULE_ID,
                       schStatus.FFINISH_QTY,
                       wo.FMATERIAL_ID,
                       craft.FIF_TAG,
                       craft.FTAG_TYPE,
                       schStatus.FIS_TAG_GENERATE
                   })
                   .FirstAsync();
                // 步骤4：组合最终结果
                var result = new
                {
                    craftScheduleInfo.FCRAFT_SCHEDULE_NO,
                    craftScheduleInfo.FCRAFT_SCHEDULE_ID,
                    craftScheduleInfo.FFINISH_QTY,
                    craftScheduleInfo.FMATERIAL_ID,
                    craftScheduleInfo.FIF_TAG,
                    craftScheduleInfo.FTAG_TYPE,
                    craftScheduleInfo.FIS_TAG_GENERATE,


                };
                if (craftScheduleInfo == null)
                {

                    ERROR(null, 100100, "该排程ID不存在！");
                }

                if (craftScheduleInfo.FIS_TAG_GENERATE != true)
                {

                    ERROR(null, 100101, "该排程未生成包箱条码！");
                }
                //if (result.FIF_TAG != true)
                //{
                //    ERROR(null, 100102, "该工艺未启用标签打印！");
                //}
                //if (result.FTAG_TYPE.ToUpper() != "PACK")
                //{
                //    ERROR(null, 100103, "该工艺未设置对应的包箱标签打印类型！");
                //}

                var TagList = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING_TAG>().Where(n => n.FCRAFT_SCHEDULE_ID == FCRAFT_SCHEDULE_ID && n.FTAG_TYPE.ToUpper() == "PACK").Select<JobBookingTagModel>().ToListAsync();
                //获取产品物料信息
                var productResult = await _businessService.GetMaterialByIdsAsync(new List<string> { result.FMATERIAL_ID });
                if (productResult.StatusCode != 200)
                {
                    ERROR(productResult, productResult.StatusCode, productResult.Message);
                }
                var matinfo = productResult.Entity.FirstOrDefault();
                TagList.ForEach(x => { x.FMATERIAL_CODE = matinfo.FMATERIAL_CODE; x.FMATERIAL_NAME = matinfo.FMATERIAL_NAME; x.MATERIAL_ID = matinfo.FMATERIAL_ID; x.FBARCODE_URL = GenCode(x.FBARCODE_NO); });
                return new PackInfoModel
                {
                    PACK_LIST = TagList.Where(n => n.FPACK_TYPE.ToUpper() == "PACK").ToList(),
                    ITEM_LIST = TagList.Where(n => n.FPACK_TYPE.ToUpper() == "ITEM").ToList()
                };

            }
            catch (Exception)
            {

                throw;
            }

        }

        private string GenCode(string no)
        {
            QRCodeGenerator qRCodeGenerator = new QRCodeGenerator();
            QRCodeData qRCodeData = qRCodeGenerator.CreateQrCode(no, QRCodeGenerator.ECCLevel.Q);
            QRCode qRCode = new QRCode(qRCodeData);
            Bitmap qrCodeImage = qRCode.GetGraphic(20); // 20是二维码的大小，可以根据需要调整
            MemoryStream ms = new MemoryStream();
            qrCodeImage.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
            byte[] arr = new byte[ms.Length];
            ms.Position = 0;
            ms.Read(arr, 0, (int)ms.Length);
            ms.Close();
            String strbaser64 = Convert.ToBase64String(arr);
            return strbaser64;

        }

        #region 合并到UpdateSchedulesFinishAsync
        /*
        /// <summary>
        /// 更新排程任务完工数,实际完工时间
        /// </summary>
        /// <param name="scheduleId"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        private async Task UpdateScheduleFinishAsync(string scheduleId, ISqlSugarClient db, UserAccountModel user, DateTime curDate)
        {

            //已完工的加工任务
            var jobs = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(p => p.FCRAFT_SCHEDULE_ID == scheduleId)
                .Select(p => new { p.FPASS_QTY, p.FNG_QTY, p.FPASS_WEIGHT, p.FNG_WEIGHT, p.FACT_ED_DATE, p.FACT_USE_HOUR })
                .ToListAsync();

            //排程任务
            var schStatus = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                                ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((sch, schStatus) => sch.FCRAFT_SCHEDULE_ID == scheduleId)
                .Select((sch, schStatus) => new
                {
                    FCRAFT_SCHEDULE_ID = schStatus.FCRAFT_SCHEDULE_ID,
                    FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,
                    FPLAN_QTY = sch.FPLAN_QTY,

                    FIF_CLOSE = schStatus.FIF_CLOSE,
                    FCLOSEDATE = schStatus.FCLOSEDATE,
                    FCLOSER = schStatus.FCLOSER,
                    FCLOSER_ID = schStatus.FCLOSER_ID,
                })
                .FirstAsync();

            //统计
            //数量
            decimal FPASS_QTY = jobs.Sum(p => p.FPASS_QTY);
            decimal FNG_QTY = jobs.Sum(p => p.FNG_QTY);

            //重量
            decimal FPASS_WEIGHT = jobs.Sum(p => p.FPASS_WEIGHT);
            decimal FNG_WEIGHT = jobs.Sum(p => p.FNG_WEIGHT);

            DateTime? FACT_ED_DATE = jobs.Max(p => p.FACT_ED_DATE);
            decimal FACT_USE_HOUR = jobs.Sum(p => p.FACT_USE_HOUR);

            T_MESD_CRAFT_SCHEDULE_STATUS daoSchStatus = new T_MESD_CRAFT_SCHEDULE_STATUS()
            {
                FCRAFT_SCHEDULE_ID = schStatus.FCRAFT_SCHEDULE_ID,
                FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,

                FIF_CLOSE = schStatus.FIF_CLOSE,
                FCLOSEDATE = schStatus.FCLOSEDATE,
                FCLOSER = schStatus.FCLOSER,
                FCLOSER_ID = schStatus.FCLOSER_ID,
            };

            //合格数
            daoSchStatus.FPASS_QTY = FPASS_QTY;
            //不良数
            daoSchStatus.FNG_QTY = FNG_QTY;
            //完工数
            daoSchStatus.FFINISH_QTY = FPASS_QTY + FNG_QTY;

            //合格重量
            daoSchStatus.FPASS_WEIGHT = FPASS_WEIGHT;
            //不良重量
            daoSchStatus.FNG_WEIGHT = FNG_WEIGHT;
            //完工重量
            daoSchStatus.FFINISH_WEIGHT = FPASS_WEIGHT + FNG_WEIGHT;

            //实际完工
            daoSchStatus.FACT_ED_DATE = FACT_ED_DATE;
            //实际工时
            daoSchStatus.FACT_USE_HOUR = FACT_USE_HOUR;

            //若满足则自动结案
            if (daoSchStatus.FFINISH_QTY >= schStatus.FPLAN_QTY)
            {
                daoSchStatus.FIF_CLOSE = 1;
                daoSchStatus.FCLOSEDATE = curDate;
                daoSchStatus.FCLOSER = user.UserPsnName;
                daoSchStatus.FCLOSER_ID = user.UserPsnId;
            }

            //更新回排程状态表
            await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daoSchStatus)
                .UpdateColumns(p => new
                {
                    p.FPASS_QTY,
                    p.FNG_QTY,
                    p.FFINISH_QTY,

                    p.FPASS_WEIGHT,
                    p.FNG_WEIGHT,
                    p.FFINISH_WEIGHT,

                    p.FACT_ED_DATE,
                    p.FACT_USE_HOUR,

                    p.FIF_CLOSE,
                    p.FCLOSEDATE,

                    p.FCLOSER,
                    p.FCLOSER_ID
                })
                .ExecuteCommandAsync();
        }
        */
        #endregion

        /// <summary>
        /// 更新排程任务完工数,实际完工时间
        /// </summary>
        /// <param name="scheduleId"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        private async Task UpdateSchedulesFinishAsync(List<string> scheduleIds, ISqlSugarClient db, UserAccountModel user, DateTime curDate)
        {
            ///判断是否设置完工超数
            var mesFinishQtyAllowGreatSch = await GetSysParamValue(_MES_FinishQtyAllowGreatSch);

            //已完工的加工任务
            var jobs = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
            .Where(p => scheduleIds.Contains(p.FCRAFT_SCHEDULE_ID))
            .Select(p => new { p.FCRAFT_SCHEDULE_ID, p.FPASS_QTY, p.FNG_QTY, p.FPASS_WEIGHT, p.FNG_WEIGHT, p.FACT_ED_DATE, p.FACT_USE_HOUR, p.FACT_ST_DATE })
            .ToListAsync();

            //排程任务
            var schStatuses = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                                ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((sch, schStatus) => scheduleIds.Contains(sch.FCRAFT_SCHEDULE_ID))
                .Select((sch, schStatus) => new
                {
                    FCRAFT_SCHEDULE_ID = schStatus.FCRAFT_SCHEDULE_ID,
                    FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,
                    FPLAN_QTY = sch.FPLAN_QTY,

                    FIF_CLOSE = schStatus.FIF_CLOSE,
                    FCLOSEDATE = schStatus.FCLOSEDATE,
                    FCLOSER = schStatus.FCLOSER,
                    FCLOSER_ID = schStatus.FCLOSER_ID,
                })
                .ToListAsync();

            // 按调度ID分组处理数据，构建更新对象
            List<T_MESD_CRAFT_SCHEDULE_STATUS> daoSchStatuses = jobs
                .GroupBy(p => p.FCRAFT_SCHEDULE_ID)
                .Select(g =>
                {
                    // 查找对应的调度状态记录
                    var schStatus = schStatuses.FirstOrDefault(s => s.FCRAFT_SCHEDULE_ID == g.Key);
                    if (schStatus == null)
                        return null;

                    // 计算合计数据
                    decimal totalPassQty = g.Sum(j => j.FPASS_QTY);
                    decimal totalNgQty = g.Sum(j => j.FNG_QTY);
                    decimal totalPassWeight = g.Sum(j => j.FPASS_WEIGHT);
                    decimal totalNgWeight = g.Sum(j => j.FNG_WEIGHT);
                    decimal totalFinishQty = totalPassQty + totalNgQty;
                    decimal totalFinishWeight = totalPassWeight + totalNgWeight;

                    // 获取最早和最晚日期
                    DateTime? earliestStartDate = g.Min(j => j.FACT_ST_DATE);
                    DateTime? latestEndDate = g.Max(j => j.FACT_ED_DATE);
                    decimal totalUseHour = g.Sum(j => j.FACT_USE_HOUR);

                    // 判断是否需要关闭工单
                    bool shouldClose = false;

                    // 检查完成数量是否达到或超过计划数量
                    if (!string.IsNullOrEmpty(mesFinishQtyAllowGreatSch) && mesFinishQtyAllowGreatSch == "1")
                    {
                        // 允许超出计划数量
                        shouldClose = totalFinishQty >= schStatus.FPLAN_QTY;
                    }
                    else
                    {
                        // 不允许超出计划数量，或者配置值为其他值
                        shouldClose = totalFinishQty == schStatus.FPLAN_QTY;
                    }

                    // 创建更新对象
                    T_MESD_CRAFT_SCHEDULE_STATUS updateStatus = new T_MESD_CRAFT_SCHEDULE_STATUS
                    {
                        FCRAFT_SCHEDULE_ID = g.Key,
                        FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,
                        FPASS_QTY = totalPassQty,
                        FNG_QTY = totalNgQty,
                        FFINISH_QTY = totalFinishQty,
                        FPASS_WEIGHT = totalPassWeight,
                        FNG_WEIGHT = totalNgWeight,
                        FFINISH_WEIGHT = totalFinishWeight,
                        FACT_ST_DATE = earliestStartDate,
                        FACT_ED_DATE = latestEndDate,
                        FACT_USE_HOUR = totalUseHour,
                        FIF_CLOSE = shouldClose ? 1 : 0
                    };

                    // 如果需要关闭工单，设置关闭信息
                    if (shouldClose)
                    {
                        updateStatus.FCLOSEDATE = curDate;
                        updateStatus.FCLOSER = user.UserPsnName;
                        updateStatus.FCLOSER_ID = user.UserId;
                    }

                    return updateStatus;
                })
                .Where(s => s != null)
                .ToList();
            if (daoSchStatuses.Any())
            {
                //更新回排程状态表
                await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daoSchStatuses)
                .UpdateColumns(p => new
                {
                    p.FPASS_QTY,
                    p.FNG_QTY,
                    p.FFINISH_QTY,

                    p.FPASS_WEIGHT,
                    p.FNG_WEIGHT,
                    p.FFINISH_WEIGHT,

                    p.FACT_ST_DATE,
                    p.FACT_ED_DATE,

                    p.FACT_USE_HOUR,

                    p.FIF_CLOSE,
                    p.FCLOSEDATE,

                    p.FCLOSER,
                    p.FCLOSER_ID
                })
                .ExecuteCommandAsync();
            }
        }

        #region 合并到UpdateWorkOrderCraftsFinishAsync
        /*
        /// <summary>
        /// 更新工单工艺完工信息
        /// </summary>
        /// <param name="workOrderCraftId"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        private async Task UpdateWorkOrderCraftFinishAsync(string workOrderCraftId, ISqlSugarClient db)
        {
            //取出工艺排程完工数
            var finishQtys = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((sch, schStatus) => sch.FWORK_ORDER_CRAFT_ID == workOrderCraftId &&
                                        schStatus.FRELEASE_STATUS == true)
                .Select((sch, schStatus) => new { schStatus.FFINISH_QTY })
                .ToListAsync();

            //汇总
            decimal FFINISH_QTY = finishQtys.Sum(p => p.FFINISH_QTY);

            //构造数据
            var daoWoCraftStatus = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>()
                .Where(p => p.FWORK_ORDER_CRAFT_ID == workOrderCraftId)
                .Select(p => new T_MESD_WORK_ORDER_CRAFT_STATUS
                {
                    FWORK_ORDER_CRAFT_ID = p.FWORK_ORDER_CRAFT_ID,
                    FWORK_ORDER_CRAFT_STATUS_ID = p.FWORK_ORDER_CRAFT_STATUS_ID,

                    FECODE = p.FECODE
                })
                .FirstAsync();

            daoWoCraftStatus.FFINISH_QTY = FFINISH_QTY;

            //更新工单工艺状态表
            await db.Updateable<T_MESD_WORK_ORDER_CRAFT_STATUS>(daoWoCraftStatus)
                .UpdateColumns(p => new { p.FFINISH_QTY })
                .ExecuteCommandAsync();

        }
        */
        #endregion

        /// <summary>
        /// 更新工单工艺完工信息
        /// </summary>
        /// <param name="workOrderCraftId"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        private async Task UpdateWorkOrderCraftsFinishAsync(List<string> workOrderCraftIds, ISqlSugarClient db)
        {
            //取出工艺排程完工数
            var finishQtys = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((sch, schStatus) => workOrderCraftIds.Contains(sch.FWORK_ORDER_CRAFT_ID) &&
                                        schStatus.FRELEASE_STATUS == true)
                .Select((sch, schStatus) => new { sch.FCRAFT_SCHEDULE_ID, sch.FWORK_ORDER_CRAFT_ID, schStatus.FFINISH_QTY })
                .ToListAsync();

            //构造数据
            var daoWoCraftStatuses = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>()
                .Where(p => workOrderCraftIds.Contains(p.FWORK_ORDER_CRAFT_ID))
                .Select(p => new T_MESD_WORK_ORDER_CRAFT_STATUS
                {
                    FWORK_ORDER_CRAFT_ID = p.FWORK_ORDER_CRAFT_ID,
                    FWORK_ORDER_CRAFT_STATUS_ID = p.FWORK_ORDER_CRAFT_STATUS_ID,

                    FECODE = p.FECODE
                })
                .ToListAsync();

            var daoStatuses = finishQtys
                .GroupBy(p => p.FWORK_ORDER_CRAFT_ID)
                .Select(g =>
                {
                    var daoWoCraftStatus = daoWoCraftStatuses.FirstOrDefault(a => a.FWORK_ORDER_CRAFT_ID == g.Key);
                    // 如果找不到对应的状态记录，则返回 null，防止空引用异常
                    if (daoWoCraftStatus == null)
                    {
                        return null;
                    }
                    return new T_MESD_WORK_ORDER_CRAFT_STATUS
                    {
                        FWORK_ORDER_CRAFT_ID = g.Key,
                        FWORK_ORDER_CRAFT_STATUS_ID = daoWoCraftStatus.FWORK_ORDER_CRAFT_STATUS_ID,
                        FFINISH_QTY = g.Sum(a => a.FFINISH_QTY)
                    };
                })
                .Where(s => s != null) // 过滤掉所有为 null 的结果
                .ToList();

            if (daoStatuses.Any())
            {
                await db.Updateable<T_MESD_WORK_ORDER_CRAFT_STATUS>(daoStatuses)
                    .UpdateColumns(p => new { p.FFINISH_QTY })
                    .ExecuteCommandAsync();
            }

        }

        /// <summary>
        /// 在最后一道工艺产生完工入库单
        /// </summary>
        /// <returns></returns>
        private async Task GenrateWorkOrderInAsync(List<T_MESD_CRAFT_JOB_BOOKING> jobs)
        {

            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();
            var curDate = _iauth.GetCurDateTime();

            var woIds = jobs.Select(p => p.FWORK_ORDER_ID).ToList();

            //工单工艺
            var woCrafts = (await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_CRAFT>
                ((wo, woCraft) => new JoinQueryInfos(JoinType.Left, wo.FWORK_ORDER_ID == woCraft.FWORK_ORDER_ID))
                .Where((wo, woCraft) => woIds.Contains(wo.FWORK_ORDER_ID))
                .Select((wo, woCraft) => new
                {
                    wo.FWORK_ORDER_ID,
                    woCraft.FWORK_ORDER_CRAFT_ID,
                    woCraft.FSHOW_SEQNO,
                    wo.FPRO_UNIT_ID,
                    wo.FWORK_ORDER_NO,
                    wo.FUNIT_ID,
                    wo.FUNIT_PRO_CONVERT_SCALE
                })
                .ToListAsync())
                .OrderBy(p => p.FSHOW_SEQNO)
                .ToList();

            //
            List<T_MESD_CRAFT_JOB_BOOKING> inJobs = new List<T_MESD_CRAFT_JOB_BOOKING>();

            foreach (var job in jobs)
            {
                var curWOCrafts = woCrafts.Where(p => p.FWORK_ORDER_ID == job.FWORK_ORDER_ID).OrderBy(p => p.FSHOW_SEQNO).ToList();

                var curCraft = woCrafts.FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == job.FWORK_ORDER_CRAFT_ID);

                //最后一道工艺（执行生产入库)
                if (curWOCrafts.IndexOf(curCraft) == curWOCrafts.Count - 1)
                {
                    inJobs.Add(job);
                }
            }


            if (inJobs.Count > 0)
            {

                #region 保存生产入库单
                WorkInModel workIn = new WorkInModel()
                {
                    FBILL_DATE = _iauth.GetCurDateTime(),
                    FBUS_TYPE = "WOIn",
                    FCREATE_TYPE = "MES",
                    FCFLAG = 0,
                    FIF_APPROVE = "UN",
                    FSYNC_STATUS = 0,
                    FEMP_ID = user.UserPsnId,
                    FEMP_NAME = user.UserPsnName,
                    FDEPT_ID = user.DeptId,
                    WorkInProducts = new List<WorkInProductModel>(),
                };
                //获取部门
                if (string.IsNullOrEmpty(workIn.FDEPT_ID))
                {
                    var depResult = await QueryDepUserInfo();
                    if (depResult.StatusCode != 200)
                    {
                        ERROR(depResult, depResult.StatusCode, depResult.Message);
                    }
                    var depInfo = depResult.Entity?.FirstOrDefault();
                    if (depInfo != null)
                    {
                        workIn.FDEPT_ID = depInfo.FMAKER_ID;
                    }
                }

                //获取产品物料信息
                var productResult = await _businessService.GetMaterialByIdsAsync(jobs.Select(p => p.FMATERIAL_ID).Distinct().ToList());
                if (productResult.StatusCode != 200)
                {
                    ERROR(productResult, productResult.StatusCode, productResult.Message);
                }

                foreach (var job in inJobs)
                {

                    T_MSDM_MATERIAL_BOM bommain = null;
                    //查询工单bom 清单

                    var workOrder = db.Ado.SqlQuery<T_MESD_WORK_ORDER>($"select  *  from [ZY_MES].[dbo].[T_MESD_WORK_ORDER] where FWORK_ORDER_ID='{job.FWORK_ORDER_ID}'").FirstOrDefault();
                    if (!string.IsNullOrEmpty(workOrder.FMATERIAL_BOM_ID))
                    {
                        bommain = db.Ado.SqlQuery<T_MSDM_MATERIAL_BOM>($"select  *  from   [ZY_MAIN].[dbo].[T_MSDM_MATERIAL_BOM] where FMATERIAL_BOM_ID='{workOrder.FMATERIAL_BOM_ID}'").FirstOrDefault();
                    }

                    var curCraft = woCrafts.FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == job.FWORK_ORDER_CRAFT_ID);

                    WorkInProductModel workInProduct = new WorkInProductModel()
                    {
                        FWORK_IN_PRODUCT_ID = job.FMATERIAL_ID,
                        FPRO_UNIT_ID = curCraft.FPRO_UNIT_ID,
                        FUNIT_PRO_CONVERT_SCALE = curCraft.FUNIT_PRO_CONVERT_SCALE,

                        FQTY = job.FPASS_QTY,//+ job.FNG_QTY  使用合格
                        FSHOW_SEQNO = 10,

                        FSOURCE_BILL_ID = curCraft.FWORK_ORDER_ID,
                        FSOURCE_NO = curCraft.FWORK_ORDER_NO,

                        FSOURCE_TYPE = "WO",
                        FSTATION_ID = job.FSTATION_ID,

                        FMATERIAL_ID = job.FMATERIAL_ID,
                        FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                        FCRAFT_JOB_BOOKING_NO = job.FCRAFT_JOB_BOOKING_NO,

                        FUNIT_ID = curCraft.FUNIT_ID,

                        FINNER_PUR_PRICE = workOrder.FINNER_PUR_PRICE,
                        FINNER_PUR_AMT = decimal.Parse((job.FPASS_QTY * workOrder.FINNER_PUR_PRICE).ToString("0.####")),

                    };

                    if (bommain != null)
                    {
                        workInProduct.FSTORE_ID = bommain.FIN_STORE_ID;
                    }
                    else
                    {
                        //默认仓库和货位
                        var product = productResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == job.FMATERIAL_ID);
                        if (product != null)
                        {
                            workInProduct.FSTORE_ID = product.FSTORE_ID;
                        }
                    }

                    //默认仓库和货位
                    var productp = productResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == job.FMATERIAL_ID);
                    if (productp != null)
                    {
                        workInProduct.FSTORE_PLACE_ID = productp.FSTORE_PLACE_ID;
                    }

                    workIn.WorkInProducts.Add(workInProduct);
                }

                List<WorkInModel> workIns = new List<WorkInModel>() { workIn };

                //保存生产入库单
                var rpcServer = this.GetService<IMES006WorkInService>("MES006WorkIn");
                var saveResult = await rpcServer.SaveAsync(workIns);

                if (saveResult.StatusCode != 200)
                {
                    ERROR(saveResult, saveResult.StatusCode, saveResult.Message);
                }
                #endregion

                #region 在最后一道工艺完工，生成入库单后，按工单的倒扣材料生成工单投料单。
                //取出需要倒扣的材料
                var woIds1 = inJobs.Select(p => p.FWORK_ORDER_ID).ToList();

                var WorkMats = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL, T_MESD_WORK_ORDER_MATERIAL_STATUS>((a, b) =>
                            new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_MATERIAL_ID == b.FWORK_ORDER_MATERIAL_ID))
                            .Where((a, b) => woIds1.Contains(a.FWORK_ORDER_ID) && a.FIF_REVERSE)
                            .Select<WorkOrderMaterialModel>().ToListAsync();

                var subMatIds = WorkMats.Select(p => p.FSUB_MATERIAL_ID).Distinct().ToList();
                var matMovePurs = await GetService<IMSD002MaterialService>("MSD002Material").GetMaterialMoveUpByIdsAsync(subMatIds);

                if (WorkMats.Count > 0)
                {
                    FetchStoreModel fetchStoreModel = new FetchStoreModel();
                    fetchStoreModel.FETCH_MATERIAL = new List<FetchMaterialStoreModel>();

                    //筛选工单id
                    woIds1 = WorkMats.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();

                    var Works = await db.Queryable<T_MESD_WORK_ORDER>()
                                .Where(p => woIds1.Contains(p.FWORK_ORDER_ID))
                                .Select(p => new { p.FWORK_ORDER_ID, p.FWORK_ORDER_NO })
                                .ToListAsync();

                    //查询 物料基本信息
                    var matIds = WorkMats.Select(p => p.FSUB_MATERIAL_ID).ToList();
                    var matInfos = await _businessService.GetMaterialByIdsAsync(matIds);
                    if (matInfos.StatusCode != 200)
                    {
                        ERROR(matInfos, matInfos.StatusCode, matInfos.Message);
                    }

                    //筛选需要倒扣的jos
                    var reverseJobs = inJobs.Where(p => woIds1.Any(x => x == p.FWORK_ORDER_ID)).ToList();

                    //获取库存 价格
                    ConcurrentBag<Store> Stores = new ConcurrentBag<Store>();
                    matInfos.Entity.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
                    {
                        Store store = new Store();
                        store.FSTORE_ID = item.FSTORE_ID;
                        store.FSTORE_PLACE_ID = item.FSTORE_PLACE_ID;
                        store.FMATERIAL_ID = item.FMATERIAL_ID;
                        Stores.Add(store);
                    });

                    //查询库存 单价 信息
                    var rpcStockServer = this.GetService<ISTK005StockService>("STK005Stock");
                    var StockResult = await rpcStockServer.SelectMatCurrentAsync(new ReqStoreMatCursModel() { Stores = Stores.ToList() });

                    if (StockResult.StatusCode != 200)
                    {
                        ERROR(StockResult, StockResult.StatusCode, StockResult.Message);
                    }

                    fetchStoreModel.FBILL_DATE = DateTime.Now;
                    fetchStoreModel.FFETCH_EMP_ID = user.UserPsnId;
                    fetchStoreModel.FDEPT_ID = user.DeptId;
                    fetchStoreModel.FBUS_TYPE = "WO01";
                    fetchStoreModel.FCREATE_TYPE = "FinishReverse";
                    fetchStoreModel.IsJudegUnApprove = true;//不排除存在未审核的单子

                    foreach (var job in reverseJobs)
                    {
                        var product = productResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == job.FMATERIAL_ID);

                        var bomsub = new List<T_MSDM_MATERIAL_BOM_SUB>();
                        //查询工单bom 清单

                        var workOrder = db.Ado.SqlQuery<T_MESD_WORK_ORDER>($"select  *  from [ZY_MES].[dbo].[T_MESD_WORK_ORDER] where FWORK_ORDER_ID='{job.FWORK_ORDER_ID}'").FirstOrDefault();
                        if (!string.IsNullOrEmpty(workOrder.FMATERIAL_BOM_ID))
                        {
                            bomsub = db.Ado.SqlQuery<T_MSDM_MATERIAL_BOM_SUB>($"select  *  from   [ZY_MAIN].[dbo].[T_MSDM_MATERIAL_BOM_SUB] where FMATERIAL_BOM_ID='{workOrder.FMATERIAL_BOM_ID}'").ToList();
                        }



                        foreach (var item in WorkMats.Where(p => p.FWORK_ORDER_ID == job.FWORK_ORDER_ID))
                        {
                            FetchMaterialStoreModel fetchMaterialStoreModel1 = new FetchMaterialStoreModel();

                            //数量上做个判断  超出可投料  如果为零 继续
                            fetchMaterialStoreModel1.FQTY = Math.Round((item.FUSE_QTY * (job.FPASS_QTY + job.FNG_QTY) * (1 + (item.FLOSS_RATE / 100))), 6);
                            var fetchQty = item.FUSE_QTY - item.FFETCH_QTY;
                            if (fetchMaterialStoreModel1.FQTY > fetchQty)
                            {
                                fetchMaterialStoreModel1.FQTY = Math.Round(fetchQty * (1 + (item.FLOSS_RATE / 100)), 6);
                            }
                            if (fetchMaterialStoreModel1.FQTY == 0)
                            {
                                continue;
                            }

                            fetchMaterialStoreModel1._CHANGE_STATUS = 1;//add
                            fetchMaterialStoreModel1.FSHOW_SEQNO = item.FSHOW_SEQNO.Value;
                            fetchMaterialStoreModel1.FSUB_MATERIAL_ID = item.FSUB_MATERIAL_ID;
                            fetchMaterialStoreModel1.FSUB_UNIT_ID = item.FSUB_UNIT_ID;

                            fetchMaterialStoreModel1.FSOURCE_BILL_ID = job.FWORK_ORDER_ID;
                            fetchMaterialStoreModel1.FSOURCE_BILL_DETAIL_ID = item.FWORK_ORDER_MATERIAL_ID;

                            fetchMaterialStoreModel1.FSOURCE_NO = Works.FirstOrDefault(x => x.FWORK_ORDER_ID == job.FWORK_ORDER_ID).FWORK_ORDER_NO;
                            fetchMaterialStoreModel1.FSOURCE_TYPE = "WO";
                            //仓库和货位
                            fetchMaterialStoreModel1.FSTORE_ID = "";
                            fetchMaterialStoreModel1.FSTORE_PLACE_ID = "";


                            var matInfodd = bomsub.FirstOrDefault(p => p.FSUB_MATERIAL_ID == item.FSUB_MATERIAL_ID);
                            if (matInfodd != null)
                            {
                                fetchMaterialStoreModel1.FSTORE_ID = matInfodd.FOUT_STORE_ID;
                                //  fetchMaterialStoreModel1.FSTORE_PLACE_ID = matStore.FSTORE_PLACE_ID;
                            }
                            else
                            {
                                if (product != null && !string.IsNullOrWhiteSpace(product.FSTORE_ID))
                                {
                                    //优先取产品的仓库作为倒扣料仓
                                    fetchMaterialStoreModel1.FSTORE_ID = product.FSTORE_ID;
                                    fetchMaterialStoreModel1.FSTORE_PLACE_ID = product.FSTORE_PLACE_ID;
                                }
                                else
                                {
                                    var matStore = matInfos.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FSUB_MATERIAL_ID);
                                    if (matStore != null)
                                    {
                                        fetchMaterialStoreModel1.FSTORE_ID = matStore.FSTORE_ID;
                                        fetchMaterialStoreModel1.FSTORE_PLACE_ID = matStore.FSTORE_PLACE_ID;
                                    }
                                }
                            }


                            if (product != null && !string.IsNullOrWhiteSpace(product.FSTORE_ID))
                            {
                                //优先取产品的仓库作为倒扣料仓

                                fetchMaterialStoreModel1.FSTORE_PLACE_ID = product.FSTORE_PLACE_ID;
                            }
                            else
                            {
                                var matStore = matInfos.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FSUB_MATERIAL_ID);
                                if (matStore != null)
                                {

                                    fetchMaterialStoreModel1.FSTORE_PLACE_ID = matStore.FSTORE_PLACE_ID;
                                }
                            }




                            fetchMaterialStoreModel1.FUNIT_STK_CONVERT_SCALE = item.FUNIT_STK_CONVERT_SCALE;
                            fetchMaterialStoreModel1.FSTK_UNIT_ID = item.FSUB_STK_UNIT_ID;

                            fetchMaterialStoreModel1.FSTK_UNIT_QTY = fetchMaterialStoreModel1.FUNIT_STK_CONVERT_SCALE == 0 ? 0 : fetchMaterialStoreModel1.FQTY / fetchMaterialStoreModel1.FUNIT_STK_CONVERT_SCALE;

                            fetchMaterialStoreModel1.FUP = 0;

                            var matMovePur = matMovePurs.Entity.FirstOrDefault(p => p.FMATERIAL_ID == fetchMaterialStoreModel1.FSUB_MATERIAL_ID);
                            if (matMovePur != null)
                            {
                                fetchMaterialStoreModel1.FUP = matMovePur.FMOVE_AVG_UP.HasValue ? (matMovePur.FMOVE_AVG_UP.Value * fetchMaterialStoreModel1.FUNIT_STK_CONVERT_SCALE) : (matMovePur.FSTD_PUR_UP.HasValue ? (matMovePur.FSTD_PUR_UP.Value * fetchMaterialStoreModel1.FUNIT_STK_CONVERT_SCALE) : 0);//物料的移动加权价
                            }
                            ////库存单价
                            //var stockFup = StockResult.Entity.FirstOrDefault(p => (p.FSTORE_ID + "|" + p.FSTORE_PLACE_ID + "|" + p.FMATERIAL_ID) == (fetchMaterialStoreModel1.FSTORE_ID + "|" + fetchMaterialStoreModel1.FSTORE_PLACE_ID + "|" + fetchMaterialStoreModel1.FSUB_MATERIAL_ID));
                            //if (stockFup != null)
                            //{
                            //    fetchMaterialStoreModel1.FUP = stockFup.FPRICE;//库存单位的 单价
                            //}

                            fetchMaterialStoreModel1.FAMT = fetchMaterialStoreModel1.FSTK_UNIT_QTY * fetchMaterialStoreModel1.FUP;

                            fetchStoreModel.FETCH_MATERIAL.Add(fetchMaterialStoreModel1);
                        }
                    }

                    if (fetchStoreModel.FETCH_MATERIAL.Count > 0)
                    {
                        //保存生产工单投料
                        var rpcFetchServer = this.GetService<IMES004WorkFetchService>("MES004WorkFetch");
                        var saveFetchResult = await rpcFetchServer.SaveAsync(fetchStoreModel);

                        if (saveFetchResult.StatusCode != 200)
                        {
                            ERROR(saveFetchResult, saveFetchResult.StatusCode, saveFetchResult.Message);
                        }
                    }
                }
            }
            #endregion

        }

        /// <summary>
        /// 判断下工序是否为电镀，如果是则生成半成品入库单
        /// </summary>
        /// <returns></returns>
        private async Task UpdateCheckFinishStore(List<T_MESD_CRAFT_JOB_BOOKING> jobs)
        {
            //电镀半成品入库
            var MES_FinishJobPlatingStore = await GetSysParamValue(_MES_FinishJobPlatingStore);
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();
            var curDate = _iauth.GetCurDateTime();

            var woIds = jobs.Select(p => p.FWORK_ORDER_ID).ToList();

            //工单工艺
            var woCrafts = (await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_CRAFT>
                ((wo, woCraft) => new JoinQueryInfos(JoinType.Left, wo.FWORK_ORDER_ID == woCraft.FWORK_ORDER_ID))
                .Where((wo, woCraft) => woIds.Contains(wo.FWORK_ORDER_ID))
                .Select((wo, woCraft) => new
                {
                    wo.FWORK_ORDER_ID,
                    woCraft.FWORK_ORDER_CRAFT_ID,
                    woCraft.FSHOW_SEQNO,
                    wo.FPRO_UNIT_ID,
                    wo.FWORK_ORDER_NO,
                    wo.FUNIT_ID,
                    wo.FUNIT_PRO_CONVERT_SCALE,
                    woCraft.FCRAFT_ID,
                })
                .ToListAsync())
                .OrderBy(p => p.FSHOW_SEQNO)
                .ToList();

            //
            List<T_MESD_CRAFT_JOB_BOOKING> inJobs = new List<T_MESD_CRAFT_JOB_BOOKING>();

            foreach (var job in jobs)
            {
                var curWOCrafts = woCrafts.Where(p => p.FWORK_ORDER_ID == job.FWORK_ORDER_ID).OrderBy(p => p.FSHOW_SEQNO).ToList();

                var curCraft = woCrafts.FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == job.FWORK_ORDER_CRAFT_ID);

                //判断是否为最后一道工序
                bool isLast = curCraft == curWOCrafts.LastOrDefault();
                if (!isLast)
                {
                    //查询下一道工艺是否为电镀

                    var NextCraft = curWOCrafts[curWOCrafts.IndexOf(curCraft) + 1];

                    //查询工艺
                    var CraftResult = await _businessService.GetCraftByIdsAsync(new List<string> { NextCraft.FCRAFT_ID });

                    var craftData = CraftResult.Entity.FirstOrDefault();
                    if (craftData != null && MES_FinishJobPlatingStore == craftData.FCRAFT_CODE)//下工序为电镀，则生产半成品入库单
                    {

                        #region 保存其他入库单

                        var workIn = new IoBillModel()
                        {
                            FBILL_DATE = _iauth.GetCurDateTime(),
                            FBUS_TYPE = "otherin",
                            FBUS_TYPE_DESC = "semiFinished",
                            FCREATE_TYPE = "API",

                            FEMP_ID = user.UserPsnId,
                            FDEPT_ID = user.DeptId,
                            FCREATOR = user.UserPsnName,
                            FECODE = user.CompanyId,
                            FSTORE_ID = "",
                            FREMARK = "",
                        };

                        //查询电镀工艺生产工单物料清单
                        var fwork_craftId = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>()
                            .Where(p => craftData.FCRAFT_ID.Contains(p.FCRAFT_ID) && curCraft.FWORK_ORDER_ID == p.FWORK_ORDER_ID)
                            .Select(p => p.FWORK_ORDER_CRAFT_ID).ToListAsync();

                        var materials = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => fwork_craftId.Contains(p.FWORK_ORDER_CRAFT_ID))
                            .Select(p => p).ToListAsync();

                        //仓库，佳乐要求取物料档案预入仓库
                        var materialIds = materials.Select(a => a.FSUB_MATERIAL_ID).Distinct().ToList();

                        //获取仓库，库位信息
                        var storeServer = this.GetService<IModuleServices.MSD002_Material.IMSD002MaterialService>("MSD002Material");
                        var storeResult = await storeServer.GetMaterialStoreAsync(materialIds);

                        if (storeResult.StatusCode != 200)
                        {
                            ERROR(storeResult, storeResult.StatusCode, storeResult.Message);
                        }

                        //入库子表物料表
                        var materialDtl = new List<IoBillMaterialModel>();
                        if (materials.Count > 0)
                        {
                            materialDtl = materials.MapToDestObj<T_MESD_WORK_ORDER_MATERIAL, IoBillMaterialModel>();

                            foreach (var item in materialDtl)
                            {


                                item.FMATERIAL_ID = materials[materialDtl.IndexOf(item)].FSUB_MATERIAL_ID;//子件物料id

                                //查询仓库货位
                                if (item.FMATERIAL_ID != null)
                                {
                                    var store = storeResult.Entity.Where(p => item.FMATERIAL_ID.Contains(p.FMATERIAL_ID)).Select(p => p).FirstOrDefault();

                                    if (string.IsNullOrEmpty(store.FSTORE_ID))// || string.IsNullOrEmpty(store.FSTORE_PLACE_ID)
                                    {
                                        ERROR(item, storeResult.StatusCode = 101000, storeResult.Message = "未找到物料" + item.FMATERIAL_CODE + "的仓库信息，请检查物料库存信息！");
                                    }
                                    item.FSTORE_ID = store.FSTORE_ID;//仓库
                                    item.FSTORE_PLACE_ID = store.FSTORE_PLACE_ID;//货位
                                }


                                item.FSTK_UNIT_QTY = job.FPASS_QTY;//入库数量(库存单位数量)
                                item.FUNIT_ID = materials[materialDtl.IndexOf(item)].FSUB_UNIT_ID;//计量单位id
                                item.FSTK_UNIT_ID = materials[materialDtl.IndexOf(item)].FSUB_STK_UNIT_ID;//库存单位id
                                item.FUNIT_QTY = item.FUNIT_STK_CONVERT_SCALE * item.FSTK_UNIT_QTY;//计量单位数量


                                item.FCREATOR_ID = user.UserPsnId;
                                item.FCREATOR = user.UserPsnName;
                                item.FCDATE = _iauth.GetCurDateTime();
                                item.FECODE = user.CompanyId;
                                item._CHANGE_STATUS = 1;
                            }
                            workIn.IoBillMaterials = materialDtl;
                        }


                        //保存半成品入库单
                        var rpcServer = this.GetService<IModuleServices.STK003_OtherIOBill.ISTK003OtherIOBillService>("STK003OtherIOBill");
                        var saveResult = await rpcServer.SaveAsync(workIn);

                        if (saveResult.StatusCode != 200)
                        {
                            ERROR(saveResult, saveResult.StatusCode, saveResult.Message);
                        }
                        #endregion
                    }

                }
            }


        }

        /// <summary>
        ///  验证完工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<JobBookingOperateModel>> ValidateFinishJobAsync(JobBookingOperateModel model)
        {
            var result = new DataResult<JobBookingOperateModel>();
            result.StatusCode = 200;
            if (model == null)
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model");
                result.StatusCode = 1010;
                return await OK(result);
            }

            if (string.IsNullOrWhiteSpace(model.FCRAFT_JOB_BOOKING_ID))
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model.FCRAFT_JOB_BOOKING_ID");
                result.StatusCode = 1010;
                return await OK(result);
            }

            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                result.Message = _multiLang["执行失败, 当前用户员工信息为空."];
                result.StatusCode = 1010;
                return await OK(result);
            }



            //加工任务
            var jobData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT_STATUS>
                ((job, sch, schStatus, wo, woStatus, woCraftStatus) => new JoinQueryInfos(
                    JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                    JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                    JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                    JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                    JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == woCraftStatus.FWORK_ORDER_CRAFT_ID))
                .Where((job, sch, schStatus, wo, woStatus, woCraftStatus) =>
                job.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                .Select((job, sch, schStatus, wo, woStatus, woCraftStatus) => new
                {
                    FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                    FCRAFT_JOB_BOOKING_NO = job.FCRAFT_JOB_BOOKING_NO,

                    FWORK_STATUS = job.FWORK_STATUS,

                    FEMP_ID = job.FEMP_ID,
                    FEMP_NAME = job.FEMP_NAME,

                    FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,

                    FIF_CLOSE_SCHEDULE = schStatus.FIF_CLOSE,
                    FIF_CLOSE_WORK_ORDER = woStatus.FIF_CLOSE,

                    FIF_CANCEL_WORK_ORDER = woStatus.FIF_CANCEL,
                    FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                    FPLAN_QTY = sch.FPLAN_QTY,
                    FFINISH_QTY = schStatus.FFINISH_QTY,

                    FBOOKING_TYPE = wo.FBOOKING_TYPE,

                    FWORK_ORDER_ID = wo.FWORK_ORDER_ID,
                    FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,

                    FFINISH_QTY_CRAFT = woCraftStatus.FFINISH_QTY,

                    FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                })
                .FirstAsync();
            if (jobData == null)
            {
                result.Message = _multiLang["加工任务数据不存在, 请查正."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            //判断工单入库是否已审核
            var workInDatas = await db.Queryable<T_MESD_WORK_IN_PRODUCT, T_MESD_WORK_IN, T_MESD_WORK_IN_STATUS>((a, b, c) =>
            new JoinQueryInfos(
                JoinType.Left, a.FWORK_IN_ID == b.FWORK_IN_ID,
                JoinType.Left, b.FWORK_IN_ID == c.FWORK_IN_ID))
                .Where((a, b, c) => a.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID && c.FCFLAG == 1)
                .Select((a, b, c) => new { a.FCRAFT_JOB_BOOKING_ID, a.FCRAFT_JOB_BOOKING_NO, b.FWORK_IN_NO, c.FCFLAG }).ToListAsync();

            if (workInDatas.Count > 0)
            {
                result.Message = $"加工任务单 {workInDatas[0].FCRAFT_JOB_BOOKING_NO} 工单入库单 {workInDatas[0].FWORK_IN_NO} 已审核";
                result.StatusCode = 1010;
                return await OK(result);
            }

            //取出工位
            string station = string.Empty;

            var stationResult = await GetStationByIdsAsync(new List<string> { model.FSTATION_ID });
            if (stationResult.StatusCode == 200 && stationResult.Entity != null && stationResult.Entity.Count > 0)
            {
                station = string.Concat(stationResult.Entity[0].FSTATION_CODE, "/", stationResult.Entity[0].FSTATION_NAME);
            }

            //暂时放开，后面做参数控制
            //只能由本人取消开工
            /*
            if (jobData.FEMP_ID != user.UserPsnId)
            {
                ERROR(null, 103006, string.Format(_multiLang["执行失败, 加工任务 {0}, 是由{1}开工的, 请通知本人操作."],
                 jobData.FCRAFT_JOB_BOOKING_NO, jobData.FEMP_NAME));
            }
            */

            //检查状态
            if (jobData.FWORK_STATUS == WorkStatus.cancel.ToString())
            {
                result.Message = string.Format(_multiLang["执行失败, 加工任务 {0}, 已取消加工."], jobData.FCRAFT_JOB_BOOKING_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            if (jobData.FWORK_STATUS == WorkStatus.finished.ToString() && !model.FEXTERNAL_QUOTE)
            {
                result.Message = string.Format(_multiLang["加工任务 {0}, 已完工, 不能重复加工."], jobData.FCRAFT_JOB_BOOKING_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            //检查排程任务是否已结案
            if ((jobData.FIF_CLOSE_SCHEDULE == 1 || jobData.FIF_CLOSE_SCHEDULE == 3) && !model.FEXTERNAL_QUOTE)
            {
                result.Message = string.Format(_multiLang["执行失败, 排程任务 {0}, 已结案."], jobData.FCRAFT_SCHEDULE_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            //检查工单是否已结案
            if (jobData.FIF_CLOSE_WORK_ORDER == 1 || jobData.FIF_CLOSE_WORK_ORDER == 3)
            {
                result.Message = string.Format(_multiLang["执行失败, 工单 {0}, 已结案."], jobData.FWORK_ORDER_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }

            //检查工单是否已作废
            if (jobData.FIF_CANCEL_WORK_ORDER)
            {
                result.Message = string.Format(_multiLang["执行失败, 工单 {0}, 已作废."], jobData.FWORK_ORDER_NO);
                result.StatusCode = 1010;
                return await OK(result);
                ERROR(null, 103011, string.Format(_multiLang["执行失败, 工单 {0}, 已作废."], jobData.FWORK_ORDER_NO));
            }

            //检查是否为工单工艺报工
            if (jobData.FBOOKING_TYPE != _wocraft)
            {
                result.Message = string.Format(_multiLang["执行失败, 工单 {0} 的报工方式不是按工单工艺报工."], jobData.FWORK_ORDER_NO);
                result.StatusCode = 1010;
                return await OK(result);
            }


            //
            if (model.FPASS_QTY <= 0 && model.FNG_QTY <= 0 && !model.FEXTERNAL_QUOTE)
            {
                result.Message = _multiLang["执行失败, 合格数量与不良数量不能同时为0."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            //根据参数检查，是否允许超出排程未完工数量
            if (await GetSysParamValue(_MES_FinishQtyAllowGreatSch) == "0")
            {
                //检查是否超出排程未完工数
                if (model.FPASS_QTY + model.FNG_QTY > jobData.FPLAN_QTY - jobData.FFINISH_QTY)
                {
                    result.Message = string.Format(_multiLang["本次合格数 {0}, 本次不良数 {1}, 加总已超出排程任务 {2} 未完工数{3}."],
                        model.FPASS_QTY.ToString("#,##0.######"),
                        model.FNG_QTY.ToString("#,##0.######"),
                        jobData.FCRAFT_SCHEDULE_NO,
                        (jobData.FPLAN_QTY - jobData.FFINISH_QTY).ToString("#,##0.######"));
                    result.StatusCode = 1010;
                    return await OK(result);
                }
                if (model.FEXTERNAL_QUOTE && model.FPASS_QTY < 0 && jobData.FFINISH_QTY_CRAFT + model.FPASS_QTY < 0)
                {
                    result.Message = string.Format(_multiLang["本次合格数 {0}, 本次不良数 {1}, 已完工数加总已小于0."],
                        model.FPASS_QTY.ToString("#,##0.######"),
                        model.FNG_QTY.ToString("#,##0.######"));
                    result.StatusCode = 1010;
                    return await OK(result);
                }
            }
            //

            //后工艺报工允许超过前工艺的完工总数量(0-则不允许)
            if (await GetSysParamValue(_MES_FinishQtyAllowGreatePre) == "0")
            {
                //检查前一工艺是否有完工记录
                var woCrafts = (await db.Queryable<T_MESD_WORK_ORDER_CRAFT>()
                    .Where(p => p.FWORK_ORDER_ID == jobData.FWORK_ORDER_ID)
                    .Select(p => new { p.FWORK_ORDER_ID, p.FWORK_ORDER_CRAFT_ID, p.FCRAFT_ID, p.FSHOW_SEQNO })
                    .ToListAsync())
                    .OrderBy(p => p.FSHOW_SEQNO).ToList();

                var schCraft = woCrafts.FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == jobData.FWORK_ORDER_CRAFT_ID);

                if (schCraft == null)
                {
                    result.Message = _multiLang["执行失败, 排程任务 {0}, 对应工艺信息不存在于工单工艺列表."];
                    result.StatusCode = 1010;
                    return await OK(result);
                }

                if (woCrafts.IndexOf(schCraft) > 0)
                {
                    var preWoCraft = woCrafts[woCrafts.IndexOf(schCraft) - 1];
                    var preFinishQty = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>("wocraftstatus")
                        .Where((wocraftstatus) => wocraftstatus.FWORK_ORDER_CRAFT_ID == preWoCraft.FWORK_ORDER_CRAFT_ID)
                        .Select((wocraftstatus) => wocraftstatus.FFINISH_QTY)
                        .FirstAsync();

                    //当前工艺已完工+本次完工 大于 上一工艺完工数量
                    if (jobData.FFINISH_QTY_CRAFT + model.FNG_QTY + model.FPASS_QTY > preFinishQty)
                    {
                        //取出工艺
                        string craft = string.Empty;
                        var craftResult = await GetCraftByIdsAsync(new List<string> { preWoCraft.FCRAFT_ID });
                        if (craftResult.StatusCode == 200 && craftResult.Entity.Count > 0)
                        {
                            var entity = craftResult.Entity[0];
                            craft = entity.FCRAFT_NAME;
                        }

                        //取出单位
                        string unit = string.Empty;
                        var unitsResult = await _businessService.GetUnitNameByIdsAsync(new List<string> { jobData.FPRO_UNIT_ID });
                        if (unitsResult.StatusCode == 200 && unitsResult.Entity.Count > 0)
                        {
                            unit = unitsResult.Entity[0].FUNIT_NAME;
                        }
                        result.Message = string.Format(_multiLang["执行失败, 当前工艺完工数量超出 上一工艺 {0} {1} {2}"], craft,
                            (jobData.FFINISH_QTY_CRAFT + model.FNG_QTY + model.FPASS_QTY - preFinishQty).ToString("#,##0.######"), unit);
                        result.StatusCode = 1010;
                        return await OK(result);
                    }
                }
                if (woCrafts.IndexOf(schCraft) > 0 && model.FEXTERNAL_QUOTE)
                {
                    var preWoCraft = woCrafts[woCrafts.IndexOf(schCraft) + 1];
                    var preFinishQty = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>("wocraftstatus")
                        .Where((wocraftstatus) => wocraftstatus.FWORK_ORDER_CRAFT_ID == preWoCraft.FWORK_ORDER_CRAFT_ID)
                        .Select((wocraftstatus) => wocraftstatus.FFINISH_QTY)
                        .FirstAsync();

                    //当前工艺已完工+本次完工 大于 上一工艺完工数量
                    if (jobData.FFINISH_QTY_CRAFT + model.FNG_QTY + model.FPASS_QTY < preFinishQty)
                    {
                        //取出工艺
                        string craft = string.Empty;
                        var craftResult = await GetCraftByIdsAsync(new List<string> { preWoCraft.FCRAFT_ID });
                        if (craftResult.StatusCode == 200 && craftResult.Entity.Count > 0)
                        {
                            var entity = craftResult.Entity[0];
                            craft = entity.FCRAFT_NAME;
                        }

                        //取出单位
                        string unit = string.Empty;
                        var unitsResult = await _businessService.GetUnitNameByIdsAsync(new List<string> { jobData.FPRO_UNIT_ID });
                        if (unitsResult.StatusCode == 200 && unitsResult.Entity.Count > 0)
                        {
                            unit = unitsResult.Entity[0].FUNIT_NAME;
                        }
                        result.Message = string.Format(_multiLang["执行失败, 当前工艺完工数量小于 下一工艺 {0} {1} {2}"], craft,
                            (jobData.FFINISH_QTY_CRAFT + model.FNG_QTY + model.FPASS_QTY - preFinishQty).ToString("#,##0.######"), unit);
                        result.StatusCode = 1010;
                        return await OK(result);
                    }
                }
            }

            return await OK(result);

        }
        #endregion


        #region 委外完工申报
        /// <summary>
        /// 委外完工申报
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<CraftJobBookingModel>> OutFinishJobAsync(JobBookingOperateModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            //验证完工
            var validate = await ValidateFinishJobAsync(model);
            if (validate.StatusCode != 200)
            {
                ERROR(null, 103001, _multiLang["验证完工申报失败.错误信息:" + validate.Message]);
            }

            // 若启用了参数数：品质管理，且不良数量大于0，则必须选择：不良原因、检验员工。
            await CheckQcAsync(model);

            var db = _isugar.DB;


            //如果部份完工, 本次完工数量+本次不良数量大于等于未完工数量, 则当成完工            
            if (model.OperateType == OperateType.partfinish)
            {
                var schData = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                    ((sch, schsts) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schsts.FCRAFT_SCHEDULE_ID))
                    .Where((sch, schsts) => sch.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                    .Select((sch, schsts) => new { sch.FPLAN_QTY, schsts.FFINISH_QTY })
                    .FirstAsync();
                //if (string.IsNullOrEmpty(model.FCRAFT_JOB_BOOKING_ID))
                //{
                //    //部份完工，重新开工
                //    JobBookingOperateModel model2 = _serialize.Deserialize<string, JobBookingOperateModel>(_serialize.Serialize(model));
                //    model2.OperateType = OperateType.start;
                //    model2.FCRAFT_JOB_BOOKING_ID = string.Empty;
                //    model2.FCRAFT_SCHEDULE_ID = model.FCRAFT_SCHEDULE_ID;
                //    model2.FSTATION_ID = model.FSTATION_ID;
                //    model2.FNG_QTY = 0;
                //    model2.FNG_WEIGHT = 0;
                //    model2.FPASS_QTY = 0;
                //    model2.FPASS_WEIGHT = 0;
                //    model2.FEXTERNAL_QUOTE = true;
                //    var jobInfo = await StartJobAsync(model2);
                //    var jobModel = jobInfo.Entity;
                //    model.FCRAFT_JOB_BOOKING_ID = jobModel.FCRAFT_JOB_BOOKING_ID;
                //}
                //else 
                if (model.FPASS_QTY + model.FNG_QTY >= schData.FPLAN_QTY - schData.FFINISH_QTY)
                {
                    model.OperateType = OperateType.finish;
                }
            }
            //查询原数据行
            var daoJob = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(p => p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                .Select<T_MESD_CRAFT_JOB_BOOKING>()
                .FirstAsync();

            //设定dao实体属性值 
            DateTime curDate = _iauth.GetCurDateTime();

            daoJob.FACT_ED_DATE = curDate;
            daoJob.FEMP_ID = user.UserPsnId;
            daoJob.FEMP_NAME = user.UserPsnName;

            daoJob.FNG_QTY += model.FNG_QTY;
            daoJob.FPASS_QTY += model.FPASS_QTY;

            daoJob.FNG_WEIGHT += model.FNG_WEIGHT;
            daoJob.FPASS_WEIGHT += model.FPASS_WEIGHT;

            //顺序不能与下句调换
            daoJob.FACT_USE_HOUR = CalcActUseHour(daoJob.FACT_USE_HOUR, daoJob.FLAST_RESUME_DATE, daoJob.FACT_ST_DATE, curDate, daoJob.FWORK_STATUS);

            //部份完工当成完工,并生成新的加工任务
            daoJob.FWORK_STATUS = WorkStatus.finished.ToString();

            // 品质字段
            daoJob.FBAD_REASON_ID = model.FBAD_REASON_ID;
            daoJob.FCHECK_PERSON_ID = model.FCHECK_PERSON_ID;

            db = _isugar.DB;

            var craftInfo = await db.Queryable<T_MESM_CRAFT>().Where(p => p.FCRAFT_ID == daoJob.FCRAFT_ID).FirstAsync();

            T_MESD_CRAFT_JOB_BOOKING_QC daoQCjob = null;
            if (craftInfo.FIF_QC)
            {
                if (daoJob.FNG_QTY > 0)
                {
                    daoQCjob = daoJob.MapToDestObj<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_JOB_BOOKING_QC>();

                    daoQCjob.FPASS_QTY = 0;
                    daoQCjob.FPASS_WEIGHT = 0;

                    daoJob.FNG_QTY = 0;
                    daoJob.FNG_WEIGHT = 0;
                }
            }

            try
            {
                db.BeginTran();

                if (daoQCjob != null)
                {
                    await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_QC>(daoQCjob).ExecuteCommandAsync();
                }

                //更新加工任务字段
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(daoJob)
                    .UpdateColumns(p => new
                    {
                        p.FACT_ED_DATE,
                        p.FEMP_ID,
                        p.FEMP_NAME,
                        p.FNG_QTY,
                        p.FPASS_QTY,
                        p.FNG_WEIGHT,
                        p.FPASS_WEIGHT,
                        p.FACT_USE_HOUR,
                        p.FWORK_STATUS,
                        // 品质字段
                        p.FBAD_REASON_ID,
                        p.FCHECK_PERSON_ID,
                    })
                    .ExecuteCommandAsync();

                //更新排程完工时间,完工数量,排程结案
                await UpdateSchedulesFinishAsync(new List<string> { daoJob.FCRAFT_SCHEDULE_ID }, db, user, curDate);

                //更新工单工艺完工数
                await UpdateWorkOrderCraftsFinishAsync(new List<string> { daoJob.FWORK_ORDER_CRAFT_ID }, db);

                db.CommitTran();
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }

            //移到事务外, 保存,根据参数审核过账
            //检查是否最后一道排程,调用生成入库单
            await GenrateWorkOrderInAsync(new List<T_MESD_CRAFT_JOB_BOOKING> { daoJob });

            //保存日志
            Dictionary<string, string> logData = new Dictionary<string, string>();

            logData.Add("FCRAFT_JOB_BOOKING_ID", daoJob.FCRAFT_JOB_BOOKING_ID);
            logData.Add("FCRAFT_JOB_BOOKING_NO", daoJob.FCRAFT_JOB_BOOKING_NO);

            logData.Add("FCRAFT_ID", daoJob.FCRAFT_ID);
            logData.Add("FSTATION_ID", daoJob.FSTATION_ID);

            logData.Add("FEMP_ID", daoJob.FEMP_ID);
            logData.Add("FEMP_NAME", daoJob.FEMP_NAME);

            logData.Add("FACT_ST_DATE", daoJob.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            if (daoJob.FACT_ED_DATE != null)
            {
                logData.Add("FACT_ED_DATE", daoJob.FACT_ED_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            }


            logData.Add("FMATERIAL_ID", daoJob.FMATERIAL_ID);
            logData.Add("FACT_USE_HOUR", daoJob.FACT_USE_HOUR.ToString());

            logData.Add("FNG_QTY", daoJob.FNG_QTY.ToString());
            logData.Add("FPASS_QTY", daoJob.FPASS_QTY.ToString());

            logData.Add("FWORK_ORDER_ID", daoJob.FWORK_ORDER_ID);
            logData.Add("FCRAFT_SCHEDULE_ID", daoJob.FCRAFT_SCHEDULE_ID);

            logData.Add("FWORK_STATUS", daoJob.FWORK_STATUS);

            _ = SaveOperateLogAsync(daoJob, model.OperateType, _serialize.Serialize(logData));


            if (model.OperateType == OperateType.partfinish)
            {
                //部份完工，重新开工
                JobBookingOperateModel model2 = _serialize.Deserialize<string, JobBookingOperateModel>(_serialize.Serialize(model));

                model2.OperateType = OperateType.start;
                model2.FCRAFT_JOB_BOOKING_ID = string.Empty;
                model2.FCRAFT_SCHEDULE_ID = daoJob.FCRAFT_SCHEDULE_ID;
                model2.FSTATION_ID = daoJob.FSTATION_ID;

                model2.FNG_QTY = 0;
                model2.FNG_WEIGHT = 0;
                model2.FPASS_QTY = 0;
                model2.FPASS_WEIGHT = 0;

                return await StartJobAsync(model2);
            }
            else
            {
                //完工，返回加工任务
                var jobBooking = await GetJobBookingByIdAsync(daoJob.FCRAFT_JOB_BOOKING_ID);
                DataResult<CraftJobBookingModel> result = new DataResult<CraftJobBookingModel>()
                {
                    Entity = jobBooking,
                    StatusCode = 200,
                };
                await DeleteWorkJobBooKingAsync(daoJob.FCRAFT_SCHEDULE_ID);
                return await OK(result);
            }

        }
        #endregion

        #region 更换工位
        /// <summary>
        /// 更换工位
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<CraftScheduleJobModel>> ChangeStationAsync(JobBookingOperateModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            //取消开工验证
            if (!await ValidateChangeStationAsync(model))
            {
                ERROR(null, 107001, _multiLang["更换工位验证失败."]);
            }

            //是否存在加工中,暂停,
            var db = _isugar.DB;

            //排程任务是否有加工中的任务
            var workingData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE>
               ((job, sch) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID))
               .Where((job, sch) => job.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID &&
                       (job.FWORK_STATUS == WorkStatus.working.ToString() ||
                       job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                       job.FWORK_STATUS == WorkStatus.paused.ToString()))
               .Select((job, sch) => new { job.FCRAFT_JOB_BOOKING_ID })
               .FirstAsync();

            if (workingData != null)
            {
                //若存在加工中，则取消开工
                JobBookingOperateModel model2 = _serialize.Deserialize<string, JobBookingOperateModel>(_serialize.Serialize(model));
                model2.OperateType = OperateType.cancel;
                model2.FCRAFT_JOB_BOOKING_ID = workingData.FCRAFT_JOB_BOOKING_ID;
                var cancelResult = await CancelJobAsync(model2);
                if (cancelResult.StatusCode != 200)
                {
                    ERROR(cancelResult, cancelResult.StatusCode, cancelResult.Message);
                }
            }

            //更换排程任务工位
            var rpcServer = this.GetService<IModuleServices.MES005_CraftSchedule.IMES005CraftScheduleService>("MES005CraftSchedule");
            ChangeStationModel model3 = model.MapToDestObj<JobBookingOperateModel, ChangeStationModel>();
            var changeResult = await rpcServer.ChangeStationAsync(model3);

            if (changeResult.StatusCode != 200)
            {
                ERROR(changeResult, changeResult.StatusCode, changeResult.Message);
            }

            //若原来已开工，则重新开工
            if (workingData != null)
            {
                JobBookingOperateModel model4 = _serialize.Deserialize<string, JobBookingOperateModel>(_serialize.Serialize(model));
                model4.OperateType = OperateType.start;
                var startResult = await StartJobAsync(model4);
                if (startResult.StatusCode != 200)
                {
                    ERROR(startResult, startResult.StatusCode, startResult.Message);
                }
            }

            //返回查询结果
            CraftScheduleJobQueryModel query = new CraftScheduleJobQueryModel
            {
                FQRCODE = changeResult.Entity.FQRCODE,
            };
            var queryResult = await QueryCraftScheduleJobAsync(query);
            if (queryResult.StatusCode != 200)
            {
                ERROR(queryResult, queryResult.StatusCode, queryResult.Message);
            }

            if (queryResult.Entity == null)
            {
                ERROR(queryResult, 100780, _multiLang["执行失败, 修改工位返回结果为空"]);
            }

            //保存日志
            Dictionary<string, string> logData = new Dictionary<string, string>();
            T_MESD_CRAFT_JOB_BOOKING daoJob = new T_MESD_CRAFT_JOB_BOOKING { FCRAFT_SCHEDULE_ID = queryResult.Entity.FCRAFT_SCHEDULE_ID };

            if (queryResult.Entity.HandlingJobs != null && queryResult.Entity.HandlingJobs.Count > 0)
            {
                var job = queryResult.Entity.HandlingJobs[0];
                logData.Add("FCRAFT_JOB_BOOKING_ID", job.FCRAFT_JOB_BOOKING_ID);
                logData.Add("FCRAFT_JOB_BOOKING_NO", job.FCRAFT_JOB_BOOKING_NO);

                logData.Add("FEMP_ID", job.FEMP_ID);
                logData.Add("FEMP_NAME", job.FEMP_NAME);

                logData.Add("FACT_ST_DATE", job.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));

                logData.Add("FWORK_STATUS", job.FWORK_STATUS);

                daoJob.FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID;
            }

            logData.Add("FCRAFT_ID", queryResult.Entity.FCRAFT_ID);

            logData.Add("FSTATION_ID", queryResult.Entity.FSTATION_ID);
            logData.Add("FOLD_STATION_ID", model.FOLD_STATION_ID);

            logData.Add("FMATERIAL_ID", queryResult.Entity.FMATERIAL_ID);

            logData.Add("FWORK_ORDER_ID", queryResult.Entity.FWORK_ORDER_ID);
            logData.Add("FCRAFT_SCHEDULE_ID", queryResult.Entity.FCRAFT_SCHEDULE_ID);

            _ = SaveOperateLogAsync(null, model.OperateType, _serialize.Serialize(logData));

            //返回
            return await OK(queryResult);

        }

        /// <summary>
        ///  验证更换工位
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<bool> ValidateChangeStationAsync(JobBookingOperateModel model)
        {

            if (model == null)
            {
                ERROR(null, 107001, string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model"));
            }

            if (string.IsNullOrWhiteSpace(model.FCRAFT_SCHEDULE_ID))
            {
                ERROR(null, 107002, string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model.FCRAFT_SCHEDULE_ID"));
            }

            if (string.IsNullOrWhiteSpace(model.FSTATION_ID))
            {
                ERROR(null, 107003, string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model.FSTATION_ID"));
            }


            if (string.IsNullOrWhiteSpace(model.FOLD_STATION_ID))
            {
                ERROR(null, 107003, string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model.FOLD_STATION_ID"));
            }

            if (model.FOLD_STATION_ID == model.FSTATION_ID)
            {
                ERROR(null, 107100, _multiLang["执行失败, 更换后工位不能与原工位相等."]);
            }

            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                ERROR(null, 107102, _multiLang["执行失败, 当前用户员工信息为空."]);
            }


            //排程任务
            var db = _isugar.DB;
            var schData = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((sch, schStatus) => sch.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                .Select((sch, schStatus) => new
                {
                    sch.FCRAFT_SCHEDULE_ID,
                    sch.FCRAFT_SCHEDULE_NO,
                    schStatus.FIF_CLOSE,
                    schStatus.FRECV_QTY,
                    sch.FPLAN_QTY,
                    schStatus.FRELEASE_STATUS,
                    schStatus.FFINISH_QTY,
                })
                .FirstAsync();
            if (schData == null)
            {
                ERROR(null, 107001, _multiLang["排程任务数据不存在, 请查正."]);
            }

            //任务是否已结案
            if (schData.FIF_CLOSE == 1 || schData.FIF_CLOSE == 3)
            {
                ERROR(null, 107002, string.Format(_multiLang["执行失败, 排程任务 {0} 已结案."], schData.FCRAFT_SCHEDULE_NO));
            }

            //任务是否未下发
            if (!schData.FRELEASE_STATUS)
            {
                ERROR(null, 107003, string.Format(_multiLang["执行失败, 排程任务 {0} 未下发."], schData.FCRAFT_SCHEDULE_NO));
            }

            //任务是否已完工
            if (schData.FFINISH_QTY >= schData.FPLAN_QTY)
            {
                ERROR(null, 107004, string.Format(_multiLang["执行失败, 排程任务 {0} 已全部完工."], schData.FCRAFT_SCHEDULE_NO));
            }

            //工单
            var woData = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>
                ((sch, wo, woStatus) => new JoinQueryInfos(JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                        JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID))
                .Where((sch, wo, woStatus) => sch.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                .Select((sch, wo, woStatus) => new
                {
                    wo.FWORK_ORDER_ID,
                    wo.FWORK_ORDER_NO,
                    woStatus.FIF_CLOSE,
                    woStatus.FIF_CANCEL,
                    sch.FWORK_ORDER_CRAFT_ID,
                    sch.FCRAFT_SCHEDULE_NO
                })
                .FirstAsync();
            if (woData == null)
            {
                ERROR(null, 107005, _multiLang["排程任务对应的工单数据不存在, 请查正."]);
            }

            //工单是否已结案
            if (woData.FIF_CLOSE == 1 || woData.FIF_CLOSE == 3)
            {
                ERROR(null, 107006, string.Format(_multiLang["执行失败, 排程任务 {0} , 工单 {1} 已结案."], schData.FCRAFT_SCHEDULE_NO, woData.FWORK_ORDER_NO));
            }

            //工单是否已作废
            if (woData.FIF_CANCEL)
            {
                ERROR(null, 107007, string.Format(_multiLang["执行失败, 排程任务 {0} , 工单 {1} 已作废."], schData.FCRAFT_SCHEDULE_NO, woData.FWORK_ORDER_NO));
            }

            return await Task.FromResult(true);

        }
        #endregion

        #region 保存日志

        /// <summary>
        /// 保存日志
        /// </summary>
        /// <param name="daoJob"></param>
        /// <param name="log"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        private async Task SaveOperateLogAsync(T_MESD_CRAFT_JOB_BOOKING daoJob, OperateType operateType, string log)
        {
            ISqlSugarClient db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();
            T_MESD_CRAFT_JOB_BOOKING_LOG daoLog = new T_MESD_CRAFT_JOB_BOOKING_LOG()
            {
                FOPERATE_TYPE = operateType.ToString(),
                FCRAFT_JOB_BOOKING_ID = daoJob.FCRAFT_JOB_BOOKING_ID,

                FCRAFT_JOB_BOOKING_LOG_ID = GuidHelper.NewGuid(),
                FCRAFT_SCHEDULE_ID = daoJob.FCRAFT_SCHEDULE_ID,

                FOPERATE_LOG = log,
            };
            await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_LOG>(daoLog).IgnoreColumns(p => new { p.FMODIDATE, p.FMODIFIER, p.FMODIFIER_ID })
                .ExecuteCommandAsync();
        }



        /// <summary>
        /// 保存日志
        /// </summary>
        /// <param name="daoJob"></param>
        /// <param name="log"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        private async Task SaveOperateLogsAsync(List<T_MESD_CRAFT_JOB_BOOKING_LOG> logs)
        {
            ISqlSugarClient db = _isugar.DB;
            await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_LOG>(logs).IgnoreColumns(p => new { p.FMODIDATE, p.FMODIFIER, p.FMODIFIER_ID })
                .ExecuteCommandAsync();
        }

        /// <summary>
        /// 保存批量完工并拆分操作的日志
        /// </summary>
        /// <param name="splitModels">拆分模型列表</param>
        /// <param name="jobsToUpdate">需要更新的报工记录</param>
        /// <param name="jobsToInsert">需要插入的报工记录</param>
        /// <param name="finalizedJobsForDownstream">已完工的报工记录</param>
        /// <param name="schedulesToInsert">需要插入的排程</param>
        /// <returns></returns>
        private async Task SaveBatchFinishWithSplitLogsAsync(
            List<JobBookingOperateModel> splitModels,
            List<T_MESD_CRAFT_JOB_BOOKING> jobsToUpdate,
            List<T_MESD_CRAFT_JOB_BOOKING> jobsToInsert,
            List<T_MESD_CRAFT_JOB_BOOKING> finalizedJobsForDownstream,
            List<T_MESD_CRAFT_SCHEDULE> schedulesToInsert)
        {
            var logs = new List<T_MESD_CRAFT_JOB_BOOKING_LOG>();

            // 为更新的报工记录创建日志
            foreach (var job in jobsToUpdate)
            {
                var logData = CreateJobBookingLogData(job, "批量完工并拆分-更新原报工记录");
                var log = new T_MESD_CRAFT_JOB_BOOKING_LOG()
                {
                    FCRAFT_JOB_BOOKING_LOG_ID = GuidHelper.NewGuid(),
                    FOPERATE_TYPE = OperateType.finish.ToString(),
                    FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                    FCRAFT_SCHEDULE_ID = job.FCRAFT_SCHEDULE_ID,
                    FOPERATE_LOG = _serialize.Serialize(logData)
                };
                logs.Add(log);
            }

            // 为新插入的报工记录创建日志
            foreach (var job in jobsToInsert)
            {
                var logData = CreateJobBookingLogData(job, "批量完工并拆分-创建新报工记录");
                var log = new T_MESD_CRAFT_JOB_BOOKING_LOG()
                {
                    FCRAFT_JOB_BOOKING_LOG_ID = GuidHelper.NewGuid(),
                    FOPERATE_TYPE = OperateType.finish.ToString(),
                    FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                    FCRAFT_SCHEDULE_ID = job.FCRAFT_SCHEDULE_ID,
                    FOPERATE_LOG = _serialize.Serialize(logData)
                };
                logs.Add(log);
            }

            // 为最终完工的报工记录创建日志
            foreach (var job in finalizedJobsForDownstream)
            {
                var logData = CreateJobBookingLogData(job, "批量完工并拆分-最终完工");
                var log = new T_MESD_CRAFT_JOB_BOOKING_LOG()
                {
                    FCRAFT_JOB_BOOKING_LOG_ID = GuidHelper.NewGuid(),
                    FOPERATE_TYPE = OperateType.finish.ToString(),
                    FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                    FCRAFT_SCHEDULE_ID = job.FCRAFT_SCHEDULE_ID,
                    FOPERATE_LOG = _serialize.Serialize(logData)
                };
                logs.Add(log);
            }

            // 批量保存日志
            if (logs.Any())
            {
                _ = SaveOperateLogsAsync(logs);
            }
        }

        /// <summary>
        /// 创建报工记录的日志数据
        /// </summary>
        /// <param name="job">报工记录</param>
        /// <param name="operationType">操作类型描述</param>
        /// <returns></returns>
        private Dictionary<string, string> CreateJobBookingLogData(T_MESD_CRAFT_JOB_BOOKING job, string operationType)
        {
            var logData = new Dictionary<string, string>
            {
                { "FCRAFT_JOB_BOOKING_ID", job.FCRAFT_JOB_BOOKING_ID },
                { "FCRAFT_JOB_BOOKING_NO", job.FCRAFT_JOB_BOOKING_NO },
                { "FCRAFT_ID", job.FCRAFT_ID },
                { "FSTATION_ID", job.FSTATION_ID },
                { "FEMP_ID", job.FEMP_ID },
                { "FEMP_NAME", job.FEMP_NAME },
                { "FWORK_STATUS", job.FWORK_STATUS },
                { "FPASS_QTY", job.FPASS_QTY.ToString() },
                { "FNG_QTY", job.FNG_QTY.ToString() },
                { "FPASS_WEIGHT", job.FPASS_WEIGHT.ToString() },
                { "FNG_WEIGHT", job.FNG_WEIGHT.ToString() },
                { "FACT_USE_HOUR", job.FACT_USE_HOUR.ToString() },
                { "FMATERIAL_ID", job.FMATERIAL_ID },
                { "FWORK_ORDER_ID", job.FWORK_ORDER_ID },
                { "FCRAFT_SCHEDULE_ID", job.FCRAFT_SCHEDULE_ID },
                { "OPERATION_TYPE", operationType }
            };

            // 添加时间字段（如果不为空）
            if (job.FACT_ST_DATE.HasValue)
                logData.Add("FACT_ST_DATE", job.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));

            if (job.FACT_ED_DATE.HasValue)
                logData.Add("FACT_ED_DATE", job.FACT_ED_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));

            if (job.FLAST_PAUSE_DATE.HasValue)
                logData.Add("FLAST_PAUSE_DATE", job.FLAST_PAUSE_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));

            if (job.FLAST_RESUME_DATE.HasValue)
                logData.Add("FLAST_RESUME_DATE", job.FLAST_RESUME_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));

            return logData;
        }

        #endregion



        #region 品质管理
        private async Task CheckQcAsync(JobBookingOperateModel model)
        {
            EntParmModel parmModel = new()
            {
                ProgName = "MainPage",
                ServiceName = ContextUtils.ServiceKey,
                SysParmCode = "QC_IfUseIPQC",
            };
            var paramValue = await _commonDataProvider.GetEntParmValueAsync(parmModel);
            if (int.TryParse(paramValue, out int value))
            {
                if (value == 1)
                {
                    // 启用了品质管理
                    if (model.FNG_QTY > 0)
                    {
                        // 不良数目大于零
                        if (string.IsNullOrWhiteSpace(model.FBAD_REASON_ID) || string.IsNullOrWhiteSpace(model.FCHECK_PERSON_ID))
                        {
                            // 不良原因或品检人为空
                            ERROR(null, 103004, _multiLang["启用品质管理且不良数大于零时, 需要输入不良原因和检验人."]);
                        }
                    }
                }
            }
        }
        #endregion

        #region 排程任务单全部完工后删除多余的加工单数据
        private async Task DeleteWorkJobBooKingAsync(string id)
        {
            var db = _isugar.DB;
            //取出工艺排程完工数
            var jobIds = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(p => p.FCRAFT_SCHEDULE_ID == id && p.FWORK_STATUS == "working" && p.FPASS_QTY == 0 && p.FNG_QTY == 0)
                .Select(p => p.FCRAFT_JOB_BOOKING_ID)
                .ToListAsync();

            if (jobIds.Count > 0)
            {
                //更新工单工艺状态表
                await db.Deleteable<T_MESD_CRAFT_JOB_BOOKING>()
                    .Where(p => jobIds.Contains(p.FCRAFT_JOB_BOOKING_ID))
                    .ExecuteCommandAsync();
            }
        }

        private async Task DeleteWorkJobBooKingAsync(List<string> ids)
        {
            var db = _isugar.DB;
            //取出工艺排程完工数
            var jobIds = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID) && p.FWORK_STATUS == "working" && p.FPASS_QTY == 0 && p.FNG_QTY == 0)
                .Select(p => p.FCRAFT_JOB_BOOKING_ID)
                .ToListAsync();

            if (jobIds.Count > 0)
            {
                //更新工单工艺状态表
                await db.Deleteable<T_MESD_CRAFT_JOB_BOOKING>()
                    .Where(p => jobIds.Contains(p.FCRAFT_JOB_BOOKING_ID))
                    .ExecuteCommandAsync();
            }
        }
        #endregion

        #region 批量开工

        /// <summary>
        /// 批量开工
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public async Task<DataResult<List<CraftJobBookingModel>>> BatchStartJobAsync(List<JobBookingOperateModel> models)
        {
            var user = await _iauth.GetUserAccountAsync();

            //开工验证
            var validate = await BatchValidateStartJobAsync(models);
            if (validate.StatusCode != 200)
            {
                ERROR(null, 102001, _multiLang["开工验证失败.错误信息:" + validate.Message]);
            }

            //产生任务单号
            var billCodes = await GetBillCodesAsync(models.Count);
            if (billCodes.Count < 1)
            {
                ERROR(null, 100102, string.Format(_multiLang["执行失败,产生加工任务编号为空."], 1, billCodes.Count));
            }

            string empId = user.UserPsnId;
            string empNm = user.UserPsnName;

            if (!string.IsNullOrEmpty(models[0].FEMP_ID))
            {
                empId = models[0].FEMP_ID;
                empNm = models[0].FEMP_NAME;
            }

            var stDate = _iauth.GetCurDateTime();

            var db = _isugar.DB;

            var craftSchIds = models.Select(p => p.FCRAFT_SCHEDULE_ID).ToList();

            var schDatas = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
              .Where((sch) => craftSchIds.Contains(sch.FCRAFT_SCHEDULE_ID))
              .ToListAsync();


            List<T_MESD_CRAFT_JOB_BOOKING> daoJobs = new List<T_MESD_CRAFT_JOB_BOOKING>();

            //加工表dao
            foreach (var model in models)
            {
                var schData = schDatas.FirstOrDefault(p => p.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID);
                T_MESD_CRAFT_JOB_BOOKING daoJob = new T_MESD_CRAFT_JOB_BOOKING
                {
                    FCRAFT_JOB_BOOKING_ID = GuidHelper.NewGuid(),
                    FCRAFT_JOB_BOOKING_NO = billCodes[0],

                    FACT_ST_DATE = stDate,
                    FCRAFT_ID = schData.FCRAFT_ID,

                    FSALE_ORDER_ID = schData.FSALE_ORDER_ID,
                    FSTATION_ID = model.FSTATION_ID,

                    FEMP_ID = empId,

                    FEMP_NAME = empNm,
                    FWORK_ORDER_CRAFT_ID = schData.FWORK_ORDER_CRAFT_ID,

                    FWORK_ORDER_ID = schData.FWORK_ORDER_ID,
                    FWORK_STATUS = WorkStatus.working.ToString(),

                    FCRAFT_SCHEDULE_ID = schData.FCRAFT_SCHEDULE_ID,
                    FMATERIAL_ID = schData.FMATERIAL_ID,
                };
                daoJobs.Add(daoJob);
            }

            db = _isugar.DB;
            try
            {
                db.BeginTran();

                //更新加工任务
                await db.Insertable<T_MESD_CRAFT_JOB_BOOKING>(daoJobs)
                    .IgnoreColumns(p => new { p.FMODIDATE, p.FMODIFIER, p.FMODIFIER_ID })
                    .ExecuteCommandAsync();

                //更新排程实际开工时间
                await BatchUpdateScheduleStartDateAsync(daoJobs, db);

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }


            //保存日志
            foreach (var daoJob in daoJobs)
            {
                Dictionary<string, string> logData = new Dictionary<string, string>();

                logData.Add("FCRAFT_JOB_BOOKING_ID", daoJob.FCRAFT_JOB_BOOKING_ID);
                logData.Add("FCRAFT_JOB_BOOKING_NO", daoJob.FCRAFT_JOB_BOOKING_NO);

                logData.Add("FCRAFT_ID", daoJob.FCRAFT_ID);
                logData.Add("FSTATION_ID", daoJob.FSTATION_ID);

                logData.Add("FEMP_ID", daoJob.FEMP_ID);
                logData.Add("FEMP_NAME", daoJob.FEMP_NAME);

                logData.Add("FACT_ST_DATE", daoJob.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                logData.Add("FMATERIAL_ID", daoJob.FMATERIAL_ID);

                logData.Add("FWORK_ORDER_ID", daoJob.FWORK_ORDER_ID);
                logData.Add("FCRAFT_SCHEDULE_ID", daoJob.FCRAFT_SCHEDULE_ID);

                logData.Add("FWORK_STATUS", daoJob.FWORK_STATUS);

                _ = SaveOperateLogAsync(daoJob, OperateType.start, _serialize.Serialize(logData));
            }

            var jobIds = daoJobs.Select(p => p.FCRAFT_JOB_BOOKING_ID).ToList();
            //返回加工任务
            var jobBookings = await GetJobBookingByIdsAsync(jobIds);
            DataResult<List<CraftJobBookingModel>> result = new DataResult<List<CraftJobBookingModel>>()
            {
                Entity = jobBookings,
                StatusCode = 200,
            };

            var Company = await GetSysParamValue(_Company);
            if (Company == "ZK")
            {
                // 兆科开工时需要调用 设备开始
                var station = models[0].FSTATION_ID;
                var craftScheduleId = models[0].FCRAFT_SCHEDULE_ID;
                await StartEquipmentForZKAsync(station, craftScheduleId, db);
            }

            //SendMsg(daoJob.FCRAFT_JOB_BOOKING_ID);//博顺发送
            //SendMsg();//悠悠发送消息通知
            return await OK(result);
        }

        /// <summary>
        ///  验证批量开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<JobBookingOperateModel>>> BatchValidateStartJobAsync(List<JobBookingOperateModel> models)
        {
            var result = new DataResult<List<JobBookingOperateModel>>();
            result.StatusCode = 200;
            var nullModels = models.Where(p => p == null).ToList();
            if (nullModels.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model");
                result.StatusCode = 1010;
                return await OK(result);
            }
            nullModels = models.Where(p => string.IsNullOrWhiteSpace(p.FCRAFT_SCHEDULE_ID)).ToList();
            if (nullModels.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model.FCRAFT_SCHEDULE_ID");
                result.StatusCode = 1010;
                return await OK(result);
            }
            nullModels = models.Where(p => string.IsNullOrWhiteSpace(p.FSTATION_ID)).ToList();
            if (nullModels.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model.FSTATION_ID");
                result.StatusCode = 1010;
                return await OK(result);
            }

            var stationModelItems = models.Select(p => p.FSTATION_ID).Distinct().ToList();
            if (stationModelItems.Count > 1)
            {
                result.Message = _multiLang["排程任务工位不同, 请查正."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                result.Message = _multiLang["执行失败, 当前用户员工信息为空."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            var craftScheduleIds = models.Select(p => p.FCRAFT_SCHEDULE_ID).ToList();

            //排程任务
            var db = _isugar.DB;
            var schData = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>
                ((sch, schStatus, wo, woStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                    JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                    JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID))
                .Where((sch, schStatus, wo, woStatus) => craftScheduleIds.Contains(sch.FCRAFT_SCHEDULE_ID))
                .Select((sch, schStatus, wo, woStatus) => new
                {
                    sch.FCRAFT_SCHEDULE_ID,
                    sch.FCRAFT_SCHEDULE_NO,
                    sch.FWORK_ORDER_CRAFT_ID,
                    sch.FCRAFT_ID,
                    sch.FSTATION_ID,
                    schStatus.FIF_CLOSE,
                    schStatus.FRECV_QTY,
                    sch.FPLAN_QTY,
                    schStatus.FRELEASE_STATUS,
                    schStatus.FFINISH_QTY,
                    wo.FBOOKING_TYPE,
                    wo.FWORK_ORDER_ID,
                    wo.FWORK_ORDER_NO,
                    WORK_FIF_CLOSE = woStatus.FIF_CLOSE,
                    WORK_FIF_CANCEL = woStatus.FIF_CANCEL,
                }).ToListAsync();



            if (schData == null || schData.Count != craftScheduleIds.Count)
            {
                result.Message = _multiLang["排程任务数据不存在, 请查正."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            var craftItems = schData.Select(p => p.FCRAFT_ID).Distinct().ToList();
            if (craftItems.Count > 1)
            {
                result.Message = _multiLang["排程任务工艺不同, 请查正."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            var stationItems = schData.Select(p => p.FSTATION_ID).Distinct().ToList();
            if (stationItems.Count > 1)
            {
                result.Message = _multiLang["排程任务工位不同, 请查正."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            //任务是否已结案
            var taskCloseNos = schData.Where(p => !models[0].FEXTERNAL_QUOTE && (p.FIF_CLOSE == 1 || p.FIF_CLOSE == 3)).Select(p => p.FCRAFT_SCHEDULE_NO).ToList();
            if (taskCloseNos.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 排程任务 {0} 已结案."], string.Join(",", taskCloseNos));
                result.StatusCode = 1010;
                return await OK(result);
            }

            //任务是否未下发
            var notReleaseNos = schData.Where(p => !p.FRELEASE_STATUS && !models[0].FEXTERNAL_QUOTE).Select(p => p.FCRAFT_SCHEDULE_NO).ToList();
            if (notReleaseNos.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 排程任务 {0} 未下发."], string.Join(",", notReleaseNos));
                result.StatusCode = 1010;
                return await OK(result);
            }

            //任务是否已完工
            var finishScheduleNos = schData.Where(p => p.FFINISH_QTY >= p.FPLAN_QTY).Select(p => p.FCRAFT_SCHEDULE_NO).ToList();
            if (finishScheduleNos.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 排程任务 {0} 已全部完工."], string.Join(",", finishScheduleNos));
                result.StatusCode = 1010;
                return await OK(result);
            }

            //检查是否为工单工艺报工
            var isCraftNos = schData.Where(p => p.FBOOKING_TYPE != _wocraft).Select(p => p.FWORK_ORDER_NO).ToList();
            if (isCraftNos.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 工单 {0} 的报工方式不是按工单工艺报工."], string.Join(",", isCraftNos));
                result.StatusCode = 1010;
                return await OK(result);
            }

            //工单是否已结案
            var woCloseNos = schData.Where(p => p.WORK_FIF_CLOSE == 1 || p.WORK_FIF_CLOSE == 3).Select(p => " 排程编号" + p.FCRAFT_SCHEDULE_NO + " 工单编号" + p.FWORK_ORDER_NO).ToList();
            if (woCloseNos.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, {0} 已结案."], string.Join(",", woCloseNos));
                result.StatusCode = 1010;
                return await OK(result);
            }

            //工单是否已作废
            var woCancelNos = schData.Where(p => p.WORK_FIF_CANCEL).Select(p => " 排程编号" + p.FCRAFT_SCHEDULE_NO + " 工单编号" + p.FWORK_ORDER_NO).ToList();
            if (woCancelNos.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败,{0} 已作废."], string.Join(",", woCancelNos));
                result.StatusCode = 1010;
                return await OK(result);
            }


            //根据参数配置，允许工位同时开工任务
            if (await GetSysParamValue(_MES_STATION_ALLOW_COMPLEX) != "1")
            {

                //工位是否有其他任务正在加工中
                var stationData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER>
                    ((job, sch, wo) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                                        JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID))
                    .Where((job, sch, wo) => job.FSTATION_ID == models[0].FSTATION_ID &&
                            (job.FWORK_STATUS == WorkStatus.working.ToString() ||
                            job.FWORK_STATUS == WorkStatus.paused.ToString() ||
                            job.FWORK_STATUS == WorkStatus.partfinished.ToString()))
                    .Select((job, sch, wo) => new { job.FWORK_STATUS, sch.FCRAFT_SCHEDULE_NO, sch.FCRAFT_SCHEDULE_ID, job.FCRAFT_JOB_BOOKING_NO, wo.FWORK_ORDER_NO })
                    .FirstAsync();
                if (stationData != null)
                {
                    //取出工位
                    string station = string.Empty;
                    var stationResult = await GetStationByIdsAsync(new List<string> { models[0].FSTATION_ID });
                    if (stationResult.StatusCode == 200 && stationResult.Entity != null && stationResult.Entity.Count > 0)
                    {
                        station = string.Concat(stationResult.Entity[0].FSTATION_CODE, "/", stationResult.Entity[0].FSTATION_NAME);
                    }
                    result.Message = string.Format(_multiLang["不能同时开工: 工位 {0}, 正在加工任务 {1}, 排程任务 {2}, 工单 {3}"],
                        station, stationData.FCRAFT_JOB_BOOKING_NO, stationData.FCRAFT_SCHEDULE_NO, stationData.FWORK_ORDER_NO);
                    result.StatusCode = 1010;
                    return await OK(result);
                }
            }

            //工艺开工检查条件 1-检查前一工艺有完工/部份完工记录，0-检查前一工艺有开工记录
            var checkPre = await GetSysParamValue(_MES_StartJobCheckModel);
            if (string.IsNullOrWhiteSpace(checkPre))
            {
                checkPre = "1";
            }

            var woOrdIds = schData.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();
            //检查前一工艺是否有完工记录
            var woCrafts = (await db.Queryable<T_MESD_WORK_ORDER_CRAFT>()
                .Where(p => woOrdIds.Contains(p.FWORK_ORDER_ID))
                .Select(p => new { p.FWORK_ORDER_ID, p.FWORK_ORDER_CRAFT_ID, p.FCRAFT_ID, p.FSHOW_SEQNO })
                .ToListAsync())
                .OrderBy(p => p.FSHOW_SEQNO).ToList();


            foreach (var woData in schData)
            {
                var woCraft = woCrafts.Where(p => p.FWORK_ORDER_ID == woData.FWORK_ORDER_ID).ToList();
                var schCraft = woCraft.FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == woData.FWORK_ORDER_CRAFT_ID);

                if (schCraft == null)
                {
                    result.Message = _multiLang["执行失败, 排程任务 {0}, 对应工艺信息不存在于工单工艺列表."];
                    result.StatusCode = 1010;
                    return await OK(result);
                }

                if (woCraft.IndexOf(schCraft) > 0)
                {
                    var preWoCraft = woCraft[woCraft.IndexOf(schCraft) - 1];
                    var schJobs = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_JOB_BOOKING>
                        ((sch, job) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == job.FCRAFT_SCHEDULE_ID))
                        .Where((sch, job) => sch.FWORK_ORDER_CRAFT_ID == preWoCraft.FWORK_ORDER_CRAFT_ID)
                        .Select((sch, job) => new
                        {
                            sch.FCRAFT_SCHEDULE_NO,
                            job.FCRAFT_JOB_BOOKING_NO,
                            job.FWORK_STATUS,
                            job.FPASS_QTY,
                            job.FNG_QTY,
                        })
                        .ToListAsync();

                    //1-检查前一工艺有完工/部份完工记录
                    if (checkPre == "1")
                    {
                        if (!(schJobs.Any(p => p.FWORK_STATUS == WorkStatus.finished.ToString() || p.FNG_QTY + p.FPASS_QTY > 0)))
                        {
                            string craft = string.Empty;
                            var craftResult = await GetCraftByIdsAsync(new List<string> { preWoCraft.FCRAFT_ID });
                            if (craftResult.StatusCode == 200 && craftResult.Entity.Count > 0)
                            {
                                var entity = craftResult.Entity[0];
                                craft = entity.FCRAFT_NAME;
                            }
                            var schNos = string.Join(",", schJobs.Select(p => p.FCRAFT_SCHEDULE_NO).Distinct().ToList());
                            result.Message = string.Format(_multiLang["执行失败, 上一工艺 {0} 未完工, 对应排程任务 {1}"], craft, schNos);
                            result.StatusCode = 1010;
                            return await OK(result);
                        }
                    }
                    else if (checkPre == "0")  //0-检查前一工艺有开工记录
                    {

                        if (!(schJobs.Any(p => p.FWORK_STATUS == WorkStatus.working.ToString() || p.FWORK_STATUS == WorkStatus.finished.ToString()
                            || p.FWORK_STATUS == WorkStatus.partfinished.ToString() || p.FWORK_STATUS == WorkStatus.paused.ToString())))
                        {
                            string craft = string.Empty;
                            var craftResult = await GetCraftByIdsAsync(new List<string> { preWoCraft.FCRAFT_ID });
                            if (craftResult.StatusCode == 200 && craftResult.Entity.Count > 0)
                            {
                                var entity = craftResult.Entity[0];
                                craft = entity.FCRAFT_NAME;
                            }
                            var schNos = string.Join(",", schJobs.Select(p => p.FCRAFT_SCHEDULE_NO).Distinct().ToList());
                            result.Message = string.Format(_multiLang["执行失败, 上一工艺 {0} 未开工, 对应排程任务 {1}"], craft, schNos);
                            result.StatusCode = 1010;
                            return await OK(result);
                        }
                    }

                }
            }

            return await OK(result);

        }

        /// <summary>
        /// 批量更新排程实际开工时间
        /// </summary>
        /// <param name="daoJob"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        private async Task BatchUpdateScheduleStartDateAsync(List<T_MESD_CRAFT_JOB_BOOKING> daoJobs, ISqlSugarClient db)
        {
            var craftSchIds = daoJobs.Select(p => p.FCRAFT_SCHEDULE_ID).ToList();

            //取出最小加工时间
            var daoJobDatas = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(p => craftSchIds.Contains(p.FCRAFT_SCHEDULE_ID) &&
                            (p.FWORK_STATUS == WorkStatus.working.ToString() || p.FWORK_STATUS == WorkStatus.paused.ToString()))
                .Select(p => new { p.FACT_ST_DATE })
                .ToListAsync();

            DateTime? startDate = null;
            if (daoJobs.Count > 0)
            {
                startDate = daoJobs.Min(p => p.FACT_ST_DATE);
            }

            //取出排程状态
            var daoSchStatus = await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS>()
                .Where(p => craftSchIds.Contains(p.FCRAFT_SCHEDULE_ID))
                .Select(p => new T_MESD_CRAFT_SCHEDULE_STATUS
                {
                    FACT_ST_DATE = startDate,
                    FCRAFT_SCHEDULE_STATUS_ID = p.FCRAFT_SCHEDULE_STATUS_ID,
                    FECODE = p.FECODE
                })
                .ToListAsync();

            await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daoSchStatus).UpdateColumns(p => new { p.FACT_ST_DATE }).ExecuteCommandAsync();

        }

        #endregion

        #region 批量暂停加工
        /// <summary>
        /// 暂停加工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<CraftJobBookingModel>>> BatchPauseJobAsync(List<JobBookingOperateModel> models)
        {
            var user = await _iauth.GetUserAccountAsync();

            //取消开工验证
            if (!await BatchValidatePauseJobAsync(models))
            {
                ERROR(null, 104001, _multiLang["批量暂停开工验证失败."]);
            }

            //string empId = user.UserPsnId;
            //string empNm = user.UserPsnName;

            var curDate = _iauth.GetCurDateTime();

            var db = _isugar.DB;

            var jobIds = models.Select(p => p.FCRAFT_JOB_BOOKING_ID).ToList();
            List<T_MESD_CRAFT_JOB_BOOKING> daoJobs = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                        .Where(p => jobIds.Contains(p.FCRAFT_JOB_BOOKING_ID))
                        .ToListAsync();

            foreach (var daoJob in daoJobs)
            {
                daoJob.FLAST_PAUSE_DATE = curDate;

                //计算实际工时(与下句顺序不能调)
                daoJob.FACT_USE_HOUR = CalcActUseHour(daoJob.FACT_USE_HOUR, daoJob.FLAST_RESUME_DATE, daoJob.FACT_ST_DATE, curDate, daoJob.FWORK_STATUS);

                daoJob.FWORK_STATUS = WorkStatus.paused.ToString();
            }

            db = _isugar.DB;
            try
            {
                db.BeginTran();

                //更新加工任务
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(daoJobs)
                    .UpdateColumns(p => new { p.FWORK_STATUS, p.FLAST_PAUSE_DATE, p.FACT_USE_HOUR })
                    .ExecuteCommandAsync();

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }
            foreach (var daoJob in daoJobs)
            {
                //保存日志
                Dictionary<string, string> logData = new Dictionary<string, string>();
                logData.Add("FCRAFT_JOB_BOOKING_ID", daoJob.FCRAFT_JOB_BOOKING_ID);
                logData.Add("FCRAFT_JOB_BOOKING_NO", daoJob.FCRAFT_JOB_BOOKING_NO);

                logData.Add("FCRAFT_ID", daoJob.FCRAFT_ID);
                logData.Add("FSTATION_ID", daoJob.FSTATION_ID);

                logData.Add("FEMP_ID", daoJob.FEMP_ID);
                logData.Add("FEMP_NAME", daoJob.FEMP_NAME);

                logData.Add("FACT_ST_DATE", daoJob.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                logData.Add("FLAST_PAUSE_DATE", daoJob.FLAST_PAUSE_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));

                logData.Add("FACT_USE_HOUR", daoJob.FACT_USE_HOUR.ToString());

                logData.Add("FMATERIAL_ID", daoJob.FMATERIAL_ID);

                logData.Add("FWORK_ORDER_ID", daoJob.FWORK_ORDER_ID);
                logData.Add("FCRAFT_SCHEDULE_ID", daoJob.FCRAFT_SCHEDULE_ID);

                logData.Add("FWORK_STATUS", daoJob.FWORK_STATUS);

                _ = SaveOperateLogAsync(daoJob, OperateType.pause, _serialize.Serialize(logData));
            }

            //返回加工任务
            var jobBooking = await GetJobBookingByIdsAsync(jobIds);
            DataResult<List<CraftJobBookingModel>> result = new DataResult<List<CraftJobBookingModel>>()
            {
                Entity = jobBooking,
                StatusCode = 200,
            };

            // SendMsg();//发送消息通知
            return await OK(result);
        }

        /// <summary>
        ///  验证暂停开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<bool> BatchValidatePauseJobAsync(List<JobBookingOperateModel> models)
        {
            var nullModels = models.Where(p => p == null).ToList();
            if (nullModels.Count > 0)
            {
                ERROR(null, 104001, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "models"));
            }
            nullModels = models.Where(p => string.IsNullOrWhiteSpace(p.FCRAFT_JOB_BOOKING_ID)).ToList();
            if (nullModels.Count > 0)
            {
                ERROR(null, 104003, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "models.FCRAFT_JOB_BOOKING_ID"));
            }

            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                ERROR(null, 104004, _multiLang["执行失败, 当前用户员工信息为空."]);
            }
            var jobIds = models.Select(p => p.FCRAFT_JOB_BOOKING_ID).ToList();
            //加工任务
            var jobData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE>
                ((job, sch) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID))
                .Where((job, sch) => jobIds.Contains(job.FCRAFT_JOB_BOOKING_ID))
                .Select((job, sch) => new
                {
                    job.FCRAFT_JOB_BOOKING_ID,
                    job.FCRAFT_JOB_BOOKING_NO,
                    job.FWORK_STATUS,
                    job.FEMP_ID,
                    job.FEMP_NAME,
                    sch.FCRAFT_SCHEDULE_NO
                })
                .ToListAsync();

            if (jobData == null)
            {
                ERROR(null, 104005, _multiLang["加工任务数据不存在, 请查正."]);
            }
            var errorStatus = jobData.Where(p => p.FWORK_STATUS == WorkStatus.cancel.ToString()).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            //检查状态
            if (errorStatus.Count > 0)
            {
                ERROR(null, 104007, string.Format(_multiLang["执行失败, 加工任务 {0}, 已取消加工."], string.Join(",", errorStatus)));
            }
            errorStatus = jobData.Where(p => p.FWORK_STATUS == WorkStatus.finished.ToString()).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            if (errorStatus.Count > 0)
            {
                ERROR(null, 104008, string.Format(_multiLang["执行失败, 加工任务 {0}, 已完工."], string.Join(",", errorStatus)));
            }
            errorStatus = jobData.Where(p => p.FWORK_STATUS == WorkStatus.paused.ToString()).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            if (errorStatus.Count > 0)
            {
                ERROR(null, 104009, string.Format(_multiLang["执行失败, 加工任务 {0}, 已暂停."], string.Join(",", errorStatus)));
            }

            return await Task.FromResult(true);

        }

        #endregion

        #region 批量恢复加工
        /// <summary>
        /// 批量恢复加工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<CraftJobBookingModel>>> BatchResumeJobAsync(List<JobBookingOperateModel> models)
        {
            var user = await _iauth.GetUserAccountAsync();

            //恢复开工验证
            if (!await BatchValidateResumeJobAsync(models))
            {
                ERROR(null, 105001, _multiLang["暂停开工验证失败."]);
            }

            //string empId = user.UserPsnId;
            //string empNm = user.UserPsnName;

            var curDate = _iauth.GetCurDateTime();

            var db = _isugar.DB;
            var jobIds = models.Select(p => p.FCRAFT_JOB_BOOKING_ID).ToList();
            List<T_MESD_CRAFT_JOB_BOOKING> daoJobs = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                        .Where(p => jobIds.Contains(p.FCRAFT_JOB_BOOKING_ID))
                        .ToListAsync();

            foreach (var daoJob in daoJobs)
            {
                daoJob.FLAST_RESUME_DATE = curDate;

                daoJob.FWORK_STATUS = WorkStatus.working.ToString();
            }

            db = _isugar.DB;
            try
            {
                db.BeginTran();

                //更新加工任务
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(daoJobs)
                    .UpdateColumns(p => new { p.FWORK_STATUS, p.FLAST_RESUME_DATE })
                    .ExecuteCommandAsync();

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }
            foreach (var daoJob in daoJobs)
            {
                //保存日志
                Dictionary<string, string> logData = new Dictionary<string, string>();
                logData.Add("FCRAFT_JOB_BOOKING_ID", daoJob.FCRAFT_JOB_BOOKING_ID);
                logData.Add("FCRAFT_JOB_BOOKING_NO", daoJob.FCRAFT_JOB_BOOKING_NO);

                logData.Add("FCRAFT_ID", daoJob.FCRAFT_ID);
                logData.Add("FSTATION_ID", daoJob.FSTATION_ID);

                logData.Add("FEMP_ID", daoJob.FEMP_ID);
                logData.Add("FEMP_NAME", daoJob.FEMP_NAME);

                logData.Add("FACT_ST_DATE", daoJob.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                logData.Add("FLAST_PAUSE_DATE", daoJob.FLAST_PAUSE_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                logData.Add("FLAST_RESUME_DATE", daoJob.FLAST_RESUME_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));

                logData.Add("FACT_USE_HOUR", daoJob.FACT_USE_HOUR.ToString());

                logData.Add("FMATERIAL_ID", daoJob.FMATERIAL_ID);

                logData.Add("FWORK_ORDER_ID", daoJob.FWORK_ORDER_ID);
                logData.Add("FCRAFT_SCHEDULE_ID", daoJob.FCRAFT_SCHEDULE_ID);

                logData.Add("FWORK_STATUS", daoJob.FWORK_STATUS);

                _ = SaveOperateLogAsync(daoJob, OperateType.resume, _serialize.Serialize(logData));
            }


            //返回加工任务
            var jobBooking = await GetJobBookingByIdsAsync(jobIds);
            DataResult<List<CraftJobBookingModel>> result = new DataResult<List<CraftJobBookingModel>>()
            {
                Entity = jobBooking,
                StatusCode = 200,
            };


            // SendMsg();//发送消息通知
            return await OK(result);
        }

        /// <summary>
        ///  验证恢复开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<bool> BatchValidateResumeJobAsync(List<JobBookingOperateModel> models)
        {
            var nullModels = models.Where(p => p == null).ToList();
            if (nullModels.Count > 0)
            {
                ERROR(null, 105001, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "models"));
            }
            nullModels = models.Where(p => string.IsNullOrWhiteSpace(p.FCRAFT_JOB_BOOKING_ID)).ToList();
            if (nullModels.Count > 0)
            {
                ERROR(null, 105003, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "models.FCRAFT_JOB_BOOKING_ID"));
            }

            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                ERROR(null, 105004, _multiLang["执行失败, 当前用户员工信息为空."]);
            }
            var jobIds = models.Select(p => p.FCRAFT_JOB_BOOKING_ID).ToList();
            //加工任务
            var jobData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE>
                ((job, sch) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID))
                .Where((job, sch) => jobIds.Contains(job.FCRAFT_JOB_BOOKING_ID))
                .Select((job, sch) => new
                {
                    job.FCRAFT_JOB_BOOKING_ID,
                    job.FCRAFT_JOB_BOOKING_NO,
                    job.FWORK_STATUS,
                    job.FEMP_ID,
                    job.FEMP_NAME,
                    sch.FCRAFT_SCHEDULE_NO
                })
                .ToListAsync();

            if (jobData == null)
            {
                ERROR(null, 105005, _multiLang["加工任务数据不存在, 请查正."]);
            }

            var errorStatus = jobData.Where(p => p.FWORK_STATUS == WorkStatus.cancel.ToString()).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            //检查状态
            if (errorStatus.Count > 0)
            {
                ERROR(null, 105007, string.Format(_multiLang["执行失败, 加工任务 {0}, 已取消加工."], string.Join(",", errorStatus)));
            }
            errorStatus = jobData.Where(p => p.FWORK_STATUS == WorkStatus.finished.ToString()).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            if (errorStatus.Count > 0)
            {
                ERROR(null, 105008, string.Format(_multiLang["执行失败, 加工任务 {0}, 已完工."], string.Join(",", errorStatus)));
            }
            errorStatus = jobData.Where(p => p.FWORK_STATUS == WorkStatus.working.ToString()).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            if (errorStatus.Count > 0)
            {
                ERROR(null, 105009, string.Format(_multiLang["执行失败, 加工任务 {0}, 已在加工中."], string.Join(",", errorStatus)));
            }

            return await Task.FromResult(true);

        }
        #endregion

        #region 批量取消开工
        /// <summary>
        /// 批量取消开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<CraftJobBookingModel>>> BatchCancelJobAsync(List<JobBookingOperateModel> models)
        {
            var user = await _iauth.GetUserAccountAsync();

            //取消开工验证
            var validate = await BatchValidateCancelJobAsync(models);
            if (validate.StatusCode != 200)
            {
                ERROR(null, 103001, _multiLang["取消开工验证失败.错误信息:" + validate.Message]);
            }

            var db = _isugar.DB;
            var jobIds = models.Select(p => p.FCRAFT_JOB_BOOKING_ID).ToList();

            List<T_MESD_CRAFT_JOB_BOOKING> daoJobs = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                        .Where(p => jobIds.Contains(p.FCRAFT_JOB_BOOKING_ID))
                        .ToListAsync();

            foreach (var daoJob in daoJobs)
            {
                daoJob.FWORK_STATUS = WorkStatus.cancel.ToString();
            }


            db = _isugar.DB;
            try
            {
                db.BeginTran();

                //更新加工任务
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(daoJobs)
                    .UpdateColumns(p => new { p.FWORK_STATUS })
                    .ExecuteCommandAsync();

                //更新排程实际开工时间
                await BatchUpdateScheduleStartDateAsync(daoJobs, db);

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }

            foreach (var daoJob in daoJobs)
            {
                //保存日志
                Dictionary<string, string> logData = new Dictionary<string, string>();
                logData.Add("FCRAFT_JOB_BOOKING_ID", daoJob.FCRAFT_JOB_BOOKING_ID);
                logData.Add("FCRAFT_JOB_BOOKING_NO", daoJob.FCRAFT_JOB_BOOKING_NO);

                logData.Add("FCRAFT_ID", daoJob.FCRAFT_ID);
                logData.Add("FSTATION_ID", daoJob.FSTATION_ID);

                logData.Add("FEMP_ID", daoJob.FEMP_ID);
                logData.Add("FEMP_NAME", daoJob.FEMP_NAME);

                logData.Add("FACT_ST_DATE", daoJob.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                logData.Add("FMATERIAL_ID", daoJob.FMATERIAL_ID);

                logData.Add("FWORK_ORDER_ID", daoJob.FWORK_ORDER_ID);
                logData.Add("FCRAFT_SCHEDULE_ID", daoJob.FCRAFT_SCHEDULE_ID);

                logData.Add("FWORK_STATUS", daoJob.FWORK_STATUS);

                _ = SaveOperateLogAsync(daoJob, OperateType.cancel, _serialize.Serialize(logData));

            }

            //返回加工任务
            var jobBooking = await GetJobBookingByIdsAsync(jobIds);
            DataResult<List<CraftJobBookingModel>> result = new DataResult<List<CraftJobBookingModel>>()
            {
                Entity = jobBooking,
                StatusCode = 200,
            };
            // SendMsg();//发送消息通知
            return await OK(result);
        }

        /// <summary>
        ///  验证取消开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<JobBookingOperateModel>>> BatchValidateCancelJobAsync(List<JobBookingOperateModel> models)
        {
            var result = new DataResult<List<JobBookingOperateModel>>();
            result.StatusCode = 200;
            var nullModels = models.Where(p => p == null).ToList();
            if (nullModels.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "models");
                result.StatusCode = 1010;
                return await OK(result);
            }
            nullModels = models.Where(p => p.FCRAFT_JOB_BOOKING_ID == null).ToList();
            if (nullModels.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "models.FCRAFT_JOB_BOOKING_ID");
                result.StatusCode = 1010;
                return await OK(result);
            }

            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                result.Message = _multiLang["执行失败, 当前用户员工信息为空."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            var jobIds = models.Select(p => p.FCRAFT_JOB_BOOKING_ID).ToList();
            //加工任务
            var jobData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE>
                ((job, sch) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID))
                .Where((job, sch) => jobIds.Contains(job.FCRAFT_JOB_BOOKING_ID))
                .Select((job, sch) => new
                {
                    job.FCRAFT_JOB_BOOKING_ID,
                    job.FCRAFT_JOB_BOOKING_NO,
                    job.FWORK_STATUS,
                    job.FEMP_ID,
                    job.FEMP_NAME,
                    sch.FCRAFT_SCHEDULE_NO,
                    job.FPASS_QTY,
                    job.FNG_QTY,
                })
                .ToListAsync();
            if (jobData == null)
            {
                result.Message = _multiLang["加工任务数据不存在, 请查正."];
                result.StatusCode = 1010;
                return await OK(result);
            }
            //暂时放开，后面做参数控制
            /*
            //只能由本人取消开工
            if (jobData.FEMP_ID != user.UserPsnId)
            {
                ERROR(null, 102006, string.Format(_multiLang["执行失败, 加工任务 {0}, 是由{1}开工的, 请通知本人操作."],
                 jobData.FCRAFT_JOB_BOOKING_NO, jobData.FEMP_NAME));
            }
            */
            var errorStatus = jobData.Where(p => p.FWORK_STATUS == WorkStatus.cancel.ToString()).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            //检查状态
            if (errorStatus.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 加工任务 {0}, 已取消加工."], string.Join(",", errorStatus));
                result.StatusCode = 1010;
                return await OK(result);
            }
            errorStatus = jobData.Where(p => p.FWORK_STATUS == WorkStatus.finished.ToString()).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            if (errorStatus.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 加工任务 {0}, 已完工."], string.Join(",", errorStatus));
                result.StatusCode = 1010;
                return await OK(result);
            }
            errorStatus = jobData.Where(p => p.FNG_QTY + p.FPASS_QTY > 0).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            if (errorStatus.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 加工任务 {0}, 已部份完工."], string.Join(",", errorStatus));
                result.StatusCode = 1010;
                return await OK(result);
            }

            return await OK(result);
        }

        #endregion


        #region 批量完工申报
        /// <summary>
        /// 完工申报
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<CraftJobBookingModel>>> BatchFinishJobAsync(List<JobBookingOperateModel> models)
        {
            var user = await _iauth.GetUserAccountAsync();

            DataResult<List<CraftJobBookingModel>> result = new DataResult<List<CraftJobBookingModel>>()
            {
                Entity = null,
                StatusCode = 200,
            };
            //// 检查是否有拆分请求
            //var hasSplitRequest = models.Any(m => m.FSPLIT_AND_FLOW && m.FSPLIT_FINISH_QTY > 0);
            //if (hasSplitRequest)
            //{
            //    // 如果有拆分请求，调用拆分方法并转换返回结果
            //    var splitResult = await BatchFinishJobWithSplitAsync(models);
            //    if (splitResult.StatusCode != 200)
            //    {
            //        ERROR(null, 103003, _multiLang["拆分完工执行失败.错误信息:" + splitResult.Message]);
            //    }

            //    // 转换拆分结果为标准完工结果格式
            //    result = new DataResult<List<CraftJobBookingModel>>();
            //    result.StatusCode = 200;
            //    result.Entity = new List<CraftJobBookingModel>();
            //    // 这里可以根据需要填充具体的完工任务信息
            //    return await OK(result);
            //}

            //验证完工
            var validate = await BatchValidateFinishJobAsync(models);
            if (validate.StatusCode != 200)
            {
                ERROR(null, 103001, _multiLang["验证完工申报失败.错误信息:" + validate.Message]);
            }

            // 若启用了参数数：品质管理，且不良数量大于0，则必须选择：不良原因、检验员。
            foreach (var model in models)
            {
                await CheckQcAsync(model);
            }


            var db = _isugar.DB;
            var jobIds = models.Select(p => p.FCRAFT_JOB_BOOKING_ID).ToList();

            //查询原数据行
            var daoJobs = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(p => jobIds.Contains(p.FCRAFT_JOB_BOOKING_ID))
                .Select<T_MESD_CRAFT_JOB_BOOKING>()
                .ToListAsync();

            foreach (var djb in daoJobs)
            {
                var model = models.Where(p => p.FCRAFT_JOB_BOOKING_ID == djb.FCRAFT_JOB_BOOKING_ID).FirstOrDefault();
                if (model != null)
                {
                    djb.F_CUSTOM_FIELD2 = model.F_CUSTOM_FIELD2;
                    djb.F_CUSTOM_FIELD3 = model.F_CUSTOM_FIELD3;
                    djb.F_CUSTOM_FIELD4 = model.F_CUSTOM_FIELD4;
                }
            }

            var originalPartFinishJobIds = models
            .Where(m => m.OperateType == OperateType.partfinish)
            .Select(m => m.FCRAFT_JOB_BOOKING_ID)
            .ToHashSet();


            //如果部份完工, 本次完工数量+本次不良数量大于等于未完工数量, 则当成完工            
            if (models[0].OperateType == OperateType.partfinish)
            {
                ///判断是否设置完工超数
                var mesFinishQtyAllowGreatSch = await GetSysParamValue(_MES_FinishQtyAllowGreatSch);

                var craftSchId = daoJobs.Select(p => p.FCRAFT_SCHEDULE_ID).ToList();

                var schDatas = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                    ((sch, schsts) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schsts.FCRAFT_SCHEDULE_ID))
                    .Where((sch, schsts) => craftSchId.Contains(sch.FCRAFT_SCHEDULE_ID))
                    .Select((sch, schsts) => new { sch.FCRAFT_SCHEDULE_ID, sch.FPLAN_QTY, schsts.FFINISH_QTY })
                    .ToListAsync();

                schDatas.AsParallel().WithDegreeOfParallelism(4).ForAll(schData =>
                {
                    var model = models.FirstOrDefault(p => p.FCRAFT_SCHEDULE_ID == schData.FCRAFT_SCHEDULE_ID);
                    if (model.FPASS_QTY + model.FNG_QTY >= schData.FPLAN_QTY - schData.FFINISH_QTY)
                    {
                        model.OperateType = OperateType.finish;
                    }
                });

            }

            var startDates = daoJobs.Select(p => p.FACT_ST_DATE).Distinct().ToList();
            if (startDates.Count > 1)
            {
                ERROR(null, 103001, _multiLang["批量完工时开工时间需一致!"]);
            }

            //设定dao实体属性值 
            DateTime curDate = _iauth.GetCurDateTime();

            var startDate = startDates.FirstOrDefault();
            var sumQty = models.Select(p => p.FPASS_QTY + p.FNG_QTY).Sum();

            TimeSpan ts = curDate - DateTime.Parse(startDate.ToString());
            var seconds = ts.TotalSeconds;
            var secondWorkQty = sumQty / decimal.Parse(seconds.ToString());

            List<T_MESD_CRAFT_JOB_BOOKING_QC> daoQCJobs = new List<T_MESD_CRAFT_JOB_BOOKING_QC>();

            foreach (var daoJob in daoJobs)
            {
                var model = models.FirstOrDefault(p => p.FCRAFT_JOB_BOOKING_ID == daoJob.FCRAFT_JOB_BOOKING_ID);
                double workedSeconds = double.Parse(((model.FNG_QTY + model.FPASS_QTY) / secondWorkQty).ToString());

                daoJob.FACT_ED_DATE = curDate;
                daoJob.FACT_BATCH_ED_DATE = DateTime.Parse(daoJob.FACT_ST_DATE.ToString()).AddSeconds(workedSeconds);
                daoJob.FEMP_ID = user.UserPsnId;
                daoJob.FEMP_NAME = user.UserPsnName;
                //如果传上来的存在 就使用传上来的
                if (!string.IsNullOrEmpty(model.FEMP_ID))
                {
                    daoJob.FEMP_ID = model.FEMP_ID;
                    daoJob.FEMP_NAME = model.FEMP_NAME;
                }

                //File.AppendAllLines("aaaaaa.txt", new List<string>() { $"{model.FCRAFT_JOB_BOOKING_ID}==={model.FEMP_ID}——{model.FEMP_NAME}--job-{daoJob.FEMP_ID}--{daoJob.FEMP_NAME}" });

                daoJob.FNG_QTY += model.FNG_QTY;
                daoJob.FPASS_QTY += model.FPASS_QTY;

                daoJob.FNG_WEIGHT += model.FNG_WEIGHT;
                daoJob.FPASS_WEIGHT += model.FPASS_WEIGHT;

                //顺序不能与下句调换
                daoJob.FACT_USE_HOUR = CalcActUseHour(daoJob.FACT_USE_HOUR, daoJob.FLAST_RESUME_DATE, daoJob.FACT_ST_DATE, DateTime.Parse(daoJob.FACT_BATCH_ED_DATE.ToString()), daoJob.FWORK_STATUS);

                //部份完工当成完工,并生成新的加工任务
                daoJob.FWORK_STATUS = WorkStatus.finished.ToString();

                // 品质字段
                daoJob.FBAD_REASON_ID = model.FBAD_REASON_ID;
                daoJob.FCHECK_PERSON_ID = model.FCHECK_PERSON_ID;

                db = _isugar.DB;

                var craftInfo = await db.Queryable<T_MESM_CRAFT>().Where(p => p.FCRAFT_ID == daoJob.FCRAFT_ID).FirstAsync();

                T_MESD_CRAFT_JOB_BOOKING_QC daoQCjob = null;
                if (craftInfo.FIF_QC)
                {
                    if (daoJob.FNG_QTY > 0)
                    {
                        daoQCjob = daoJob.MapToDestObj<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_JOB_BOOKING_QC>();

                        daoQCjob.FPASS_QTY = 0;
                        daoQCjob.FPASS_WEIGHT = 0;

                        daoJob.FNG_QTY = 0;
                        daoJob.FNG_WEIGHT = 0;

                        daoQCJobs.Add(daoQCjob);
                    }

                }
                //添加标签
                if (model.tagItems != null && model.tagItems.Count > 0)
                {
                    var Ins = model.tagItems.Where(n => (n.FCRAFT_JOB_BOOKING_TAG_ID == null || n.FCRAFT_JOB_BOOKING_TAG_ID == "") && n.FPRODUCT_NUM > 0 && n.FPRODUCT_WEIGHT > 0).ToList();
                    if (Ins != null && Ins.Count > 0)
                    {
                        var NewTagList = new List<T_MESD_CRAFT_JOB_BOOKING_TAG>();
                        foreach (var item in Ins)
                        {
                            NewTagList.Add(new T_MESD_CRAFT_JOB_BOOKING_TAG
                            {
                                FCRAFT_JOB_BOOKING_TAG_ID = GuidHelper.NewGuid(),
                                FCRAFT_JOB_BOOKING_ID = model.FCRAFT_JOB_BOOKING_ID,
                                FCRAFT_SCHEDULE_ID = model.FCRAFT_SCHEDULE_ID,
                                FCREATOR_ID = user.UserId,
                                FCREATOR = user.UserPsnName,
                                FSTUFFLOT_NO = item.FSTUFFLOT_NO,
                                FCDATE = curDate,
                                FECODE = user.CompanyId,
                                FPRODUCT_NUM = item.FPRODUCT_NUM,
                                FPRODUCT_WEIGHT = item.FPRODUCT_WEIGHT,
                                FGROSS_WEIGHT = item.FGROSS_WEIGHT,
                                FTEXT1 = item.FTEXT1,
                                FTEXT2 = item.FTEXT2,
                                FTEXT3 = item.FTEXT3,
                                FTEXT4 = item.FTEXT4,
                                FTEXT5 = item.FTEXT5,
                                FSHOW_SEQNO = item.FSHOW_SEQNO,
                                FTAG_TYPE = "ITEM"
                            });
                        }
                        var _genlist = new List<TagGenModel>();
                        foreach (var item in NewTagList)
                        {
                            _genlist.Add(new TagGenModel { GEN_NO = model.FMATERIAL_CODE, FBILL_SOURCE_ITEM_ID = item.FCRAFT_JOB_BOOKING_TAG_ID, FBILL_SOURCE_NO = model.FCRAFT_SCHEDULE_NO, FSTK_QTY = item.FPRODUCT_NUM, FBILL_TYPE = "FCRAFT_SCHEDULE".ToUpper() });
                        }
                        var rpcServer = this.GetService<IMES017TagService>("MES017Tag");
                        var TagList = await rpcServer.GenerateOrderCodeList(_genlist);
                        if (TagList.StatusCode != 200)
                        {
                            ERROR(null, TagList.StatusCode, TagList.Message);
                        }
                        NewTagList.ForEach(x => x.FBARCODE_NO = TagList.Entity.FirstOrDefault(n => n.FBILL_SOURCE_ITEM_ID == x.FCRAFT_JOB_BOOKING_TAG_ID)?.FBARCODE_NO);
                        await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_TAG>(NewTagList).ExecuteCommandAsync();
                    }

                }
            }

            var craftSchIds = daoJobs.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
            var enableAutoSplit = await GetSysParamValue(_MES_EnableAutoSplitOnCompletion); // 是否自动拆分
            var enableAutoRework = await GetSysParamValue(_MES_EnableAutoReworkOnNg); // 是否自动返工


            try
            {
                db.BeginTran();

                if (daoQCJobs.Count > 0)
                {
                    await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_QC>(daoQCJobs).ExecuteCommandAsync();
                }

                //更新加工任务字段
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(daoJobs)
                    .UpdateColumns(p => new
                    {
                        p.FACT_ED_DATE,
                        p.FACT_BATCH_ED_DATE,
                        p.FEMP_ID,
                        p.FEMP_NAME,
                        p.FNG_QTY,
                        p.FPASS_QTY,
                        p.FNG_WEIGHT,
                        p.FPASS_WEIGHT,
                        p.FACT_USE_HOUR,
                        p.FWORK_STATUS,
                        // 品质字段
                        p.FBAD_REASON_ID,
                        p.FCHECK_PERSON_ID,
                        //佳乐工位机
                        p.F_CUSTOM_FIELD2,
                        p.F_CUSTOM_FIELD3,
                        p.F_CUSTOM_FIELD4,
                    })
                    .ExecuteCommandAsync();

                //更新排程完工时间,完工数量,排程结案

                await UpdateSchedulesFinishAsync(craftSchIds, db, user, curDate);

                //更新工单工艺完工数
                await UpdateWorkOrderCraftsFinishAsync(daoJobs.Select(p => p.FWORK_ORDER_CRAFT_ID).Distinct().ToList(), db);


                if (enableAutoRework == "1")
                {
                    // 直接从本次完工的所有任务(daoJobs)中查找有不良品的记录，不再受 partfinish 类型限制
                    var reworkJobs = daoJobs.Where(j => j.FNG_QTY > 0).ToList();
                    if (reworkJobs.Any())
                    {
                        // 调用返工逻辑
                        await HandleReworkScheduleCreation(reworkJobs, db);
                    }
                }


                if (enableAutoSplit == "1")
                {
                    var jobsForSplitting = daoJobs.Where(j => originalPartFinishJobIds.Contains(j.FCRAFT_JOB_BOOKING_ID)).ToList();

                    // 1. 处理良品（FPASS_QTY > 0），拆分到下游工序
                    var goodJobs = jobsForSplitting.Where(j => j.FPASS_QTY > 0).ToList();
                    if (goodJobs.Any())
                    {
                        await HandleBatchAutoSplitExistingSchedule(goodJobs, db);
                    }
                }


                db.CommitTran();
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }

            //移到事务外, 保存,根据参数审核过账
            //检查是否最后一道排程,调用生成入库单
            await GenrateWorkOrderInAsync(daoJobs);



            List<JobBookingOperateModel> startJobs = new List<JobBookingOperateModel>();

            foreach (var daoJob in daoJobs)
            {
                //保存日志
                Dictionary<string, string> logData = new Dictionary<string, string>();

                logData.Add("FCRAFT_JOB_BOOKING_ID", daoJob.FCRAFT_JOB_BOOKING_ID);
                logData.Add("FCRAFT_JOB_BOOKING_NO", daoJob.FCRAFT_JOB_BOOKING_NO);

                logData.Add("FCRAFT_ID", daoJob.FCRAFT_ID);
                logData.Add("FSTATION_ID", daoJob.FSTATION_ID);

                logData.Add("FEMP_ID", daoJob.FEMP_ID);
                logData.Add("FEMP_NAME", daoJob.FEMP_NAME);

                logData.Add("FACT_ST_DATE", daoJob.FACT_ST_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                if (daoJob.FACT_ED_DATE != null)
                {
                    logData.Add("FACT_ED_DATE", daoJob.FACT_ED_DATE.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                }


                logData.Add("FMATERIAL_ID", daoJob.FMATERIAL_ID);
                logData.Add("FACT_USE_HOUR", daoJob.FACT_USE_HOUR.ToString());

                logData.Add("FNG_QTY", daoJob.FNG_QTY.ToString());
                logData.Add("FPASS_QTY", daoJob.FPASS_QTY.ToString());

                logData.Add("FWORK_ORDER_ID", daoJob.FWORK_ORDER_ID);
                logData.Add("FCRAFT_SCHEDULE_ID", daoJob.FCRAFT_SCHEDULE_ID);

                logData.Add("FWORK_STATUS", daoJob.FWORK_STATUS);

                _ = SaveOperateLogAsync(daoJob, models[0].OperateType, _serialize.Serialize(logData));

                var model = models.FirstOrDefault(p => p.FCRAFT_JOB_BOOKING_ID == daoJob.FCRAFT_JOB_BOOKING_ID);
                if (model.OperateType == OperateType.partfinish)
                {
                    //部份完工，重新开工
                    JobBookingOperateModel model2 = _serialize.Deserialize<string, JobBookingOperateModel>(_serialize.Serialize(model));

                    model2.OperateType = OperateType.start;
                    model2.FCRAFT_JOB_BOOKING_ID = string.Empty;
                    model2.FCRAFT_SCHEDULE_ID = daoJob.FCRAFT_SCHEDULE_ID;
                    model2.FSTATION_ID = daoJob.FSTATION_ID;
                    model2.FNG_QTY = 0;
                    model2.FNG_WEIGHT = 0;
                    model2.FPASS_QTY = 0;
                    model2.FPASS_WEIGHT = 0;

                    startJobs.Add(model2);
                }
            }

            if (startJobs.Count > 0)
            {
                result = await BatchStartJobAsync(startJobs);
            }
            else
            {
                var jobBooking = await GetJobBookingByIdsAsync(jobIds);
                result.Entity = jobBooking;

                await DeleteWorkJobBooKingAsync(craftSchIds);
            }

            var CraftResult = await _businessService.GetCraftByIdsAsync(daoJobs.Select(p => p.FCRAFT_ID).ToList());


            ////判断是否半成品入库
            //await UpdateCheckFinishStore(daoJobs);

            var Company = await GetSysParamValue(_Company);
            if (Company == "ZK")
            {
                // 兆科开工时需要调用 设备开始
                var station = models[0].FSTATION_ID;
                var craftScheduleId = models[0].FCRAFT_SCHEDULE_ID;
                await EndEquipmentForZKAsync(station, craftScheduleId, db);
            }

            return await OK(result);
        }

        #endregion

        #region 批量完工并拆分批次

        /// <summary>
        /// 批量完工并拆分批次
        /// </summary>
        /// <param name="models">包含拆分参数的报工操作模型列表</param>
        /// <returns>返回完工结果和拆分信息</returns>
        public async Task<DataResult<List<BatchSplitResultModel>>> BatchFinishJobWithSplitAsync(List<JobBookingOperateModel> models)
        {
            var user = await _iauth.GetUserAccountAsync();
            var result = new DataResult<List<BatchSplitResultModel>> { Entity = new List<BatchSplitResultModel>(), StatusCode = 200 };

            // 验证拆分参数
            var validateResult = await ValidateBatchSplitAsync(models);
            if (validateResult.StatusCode != 200)
            {
                result.StatusCode = validateResult.StatusCode;
                result.Message = validateResult.Message;
                return await OK(result);
            }

            var db = _isugar.DB;
            var curDate = _iauth.GetCurDateTime();
            var enableAutoRework = await GetSysParamValue(_MES_EnableAutoReworkOnNg) == "1"; // 是否自动返工

            var splitModels = models.Where(m => m.FSPLIT_AND_FLOW && IsValidSplitRequest(m)).ToList();
            if (!splitModels.Any())
            {
                // 如果没有需要拆分的任务，走普通完工流程
                await BatchFinishJobAsync(models); // 调用不带拆分的版本
                return await OK(result);
            }

            try
            {
                // ===================================================================================
                // 步骤 1: 事务外批量查询所有需要的数据
                // ===================================================================================
                var originalScheduleIds = splitModels.Select(m => m.FCRAFT_SCHEDULE_ID).Distinct().ToList();
                var originalJobBookingIds = splitModels.Select(m => m.FCRAFT_JOB_BOOKING_ID).Where(id => !string.IsNullOrEmpty(id)).Distinct().ToList();

                var originalSchedules = (await db.Queryable<T_MESD_CRAFT_SCHEDULE>().In(originalScheduleIds).ToListAsync()).ToDictionary(s => s.FCRAFT_SCHEDULE_ID);
                var historicalFinishedJobs = (await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(j => originalScheduleIds.Contains(j.FCRAFT_SCHEDULE_ID) && j.FWORK_STATUS == "finished").ToListAsync()).GroupBy(j => j.FCRAFT_SCHEDULE_ID).ToDictionary(g => g.Key, g => g.ToList());
                var triggeringJobBookings = originalJobBookingIds.Any() ? (await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().In(originalJobBookingIds).ToListAsync()).ToDictionary(j => j.FCRAFT_JOB_BOOKING_ID) : new Dictionary<string, T_MESD_CRAFT_JOB_BOOKING>();

                // ===================================================================================
                // 步骤 2: 内存中准备待操作的实体列表
                // ===================================================================================
                var schedulesToUpdate = new List<T_MESD_CRAFT_SCHEDULE>();
                var jobsToUpdate = new List<T_MESD_CRAFT_JOB_BOOKING>();
                var schedulesToInsert = new List<T_MESD_CRAFT_SCHEDULE>();
                var scheduleStatusesToInsert = new List<T_MESD_CRAFT_SCHEDULE_STATUS>();
                var jobsToInsert = new List<T_MESD_CRAFT_JOB_BOOKING>();

                var finalizedJobsForDownstream = new List<T_MESD_CRAFT_JOB_BOOKING>();
                var jobsToClearDict = new Dictionary<string, T_MESD_CRAFT_JOB_BOOKING>();


                int splitSchedulesToCreateCount = splitModels.Count(m =>
                {
                    var originalQty = originalSchedules.ContainsKey(m.FCRAFT_SCHEDULE_ID) ? originalSchedules[m.FCRAFT_SCHEDULE_ID].FPLAN_QTY : 0;
                    var existingQty = historicalFinishedJobs.ContainsKey(m.FCRAFT_SCHEDULE_ID) ? historicalFinishedJobs[m.FCRAFT_SCHEDULE_ID].Sum(j => j.FPASS_QTY + j.FNG_QTY) : 0;
                    return CalculateActualSplitQty(m, existingQty) < originalQty;
                });

                int potentialNewSchedules = models.Count * 2; // 每个操作最多产生一个良品排程和一个返工排程
                var splitScheduleBillCodes = await GetBillCodesAsync(splitSchedulesToCreateCount, true);
                var splitJobBillCodes = await GetBillCodesAsync(splitSchedulesToCreateCount, false);
                int billCodeIndex = 0;

                foreach (var model in splitModels)
                {
                    if (!originalSchedules.TryGetValue(model.FCRAFT_SCHEDULE_ID, out var originalSchedule)) continue;

                    var existingJobs = historicalFinishedJobs.GetValueOrDefault(model.FCRAFT_SCHEDULE_ID, new List<T_MESD_CRAFT_JOB_BOOKING>());
                    var existingFinishedQty = existingJobs.Sum(j => j.FPASS_QTY + j.FNG_QTY);
                    var totalSplitQty = CalculateActualSplitQty(model, existingFinishedQty);

                    // =================== 关键逻辑分支 ===================
                    if (totalSplitQty >= originalSchedule.FPLAN_QTY)
                    {
                        // === 情况一：最终完工 ===
                        if (triggeringJobBookings.TryGetValue(model.FCRAFT_JOB_BOOKING_ID, out var jobToFinalize))
                        {
                            jobToFinalize.FACT_USE_HOUR = CalcActUseHour(jobToFinalize.FACT_USE_HOUR, jobToFinalize.FLAST_RESUME_DATE, jobToFinalize.FACT_ST_DATE, curDate, jobToFinalize.FWORK_STATUS);
                            jobToFinalize.FWORK_STATUS = WorkStatus.finished.ToString();
                            jobToFinalize.FACT_ED_DATE = curDate;
                            jobToFinalize.FPASS_QTY += model.FPASS_QTY;
                            jobToFinalize.FNG_QTY += model.FNG_QTY;
                            jobToFinalize.FPASS_WEIGHT += model.FPASS_WEIGHT;
                            jobToFinalize.FNG_WEIGHT += model.FNG_WEIGHT;

                            jobsToUpdate.Add(jobToFinalize);
                            finalizedJobsForDownstream.Add(jobToFinalize);
                        }

                        result.Entity.Add(new BatchSplitResultModel
                        {
                            FORIGINAL_SCHEDULE_ID = model.FCRAFT_SCHEDULE_ID,
                            FSPLIT_FINISHED_SCHEDULE_ID = model.FCRAFT_SCHEDULE_ID,
                            FSPLIT_FINISHED_SCHEDULE_NO = originalSchedule.FCRAFT_SCHEDULE_NO,
                            FSPLIT_FINISH_QTY = totalSplitQty,
                            IsSuccess = true,
                            Message = "原始排程已全部完工"
                        });
                    }
                    else
                    {
                        // === 情况二：部分拆分 ===
                        var historicalPassQty = existingJobs.Sum(j => j.FPASS_QTY);
                        var historicalNgQty = existingJobs.Sum(j => j.FNG_QTY);
                        var passQtyToSplit = (model.FSPLIT_MODE?.ToLower() == "total_finished") ? (historicalPassQty + model.FPASS_QTY) : model.FPASS_QTY;
                        var ngQtyToSplit = (model.FSPLIT_MODE?.ToLower() == "total_finished") ? (historicalNgQty + model.FNG_QTY) : model.FNG_QTY;
                        var totalQtyToSplit = passQtyToSplit + ngQtyToSplit;


                        // 2. 更新原始排程 (FPLAN_QTY 减去总拆分量)
                        originalSchedule.FPLAN_QTY -= totalQtyToSplit;
                        if (string.IsNullOrEmpty(originalSchedule.FLOT_NO))
                        {
                            originalSchedule.FLOT_NO = await GenerateSplitLotNoAsync(originalSchedule.FWORK_ORDER_ID, originalSchedule.FWORK_ORDER_CRAFT_ID, db);
                        }
                        schedulesToUpdate.Add(originalSchedule);

                        var workHours = existingJobs.Sum(j => j.FACT_USE_HOUR);
                        if (triggeringJobBookings.TryGetValue(model.FCRAFT_JOB_BOOKING_ID, out var triggeringJob))
                        {
                            workHours += CalcActUseHour(triggeringJob.FACT_USE_HOUR, triggeringJob.FLAST_RESUME_DATE, triggeringJob.FACT_ST_DATE, curDate, triggeringJob.FWORK_STATUS);
                            if (!jobsToClearDict.ContainsKey(triggeringJob.FCRAFT_JOB_BOOKING_ID))
                            {
                                jobsToClearDict.Add(triggeringJob.FCRAFT_JOB_BOOKING_ID, triggeringJob);
                            }
                        }

                        foreach (var job in existingJobs)
                        {
                            if (!jobsToClearDict.ContainsKey(job.FCRAFT_JOB_BOOKING_ID))
                            {
                                jobsToClearDict.Add(job.FCRAFT_JOB_BOOKING_ID, job);
                            }
                        }

                        var splitFinishedSchedule = await PrepareSplitFinishedScheduleAsync(originalSchedule, totalQtyToSplit, db, splitScheduleBillCodes[billCodeIndex]);
                        schedulesToInsert.Add(splitFinishedSchedule);

                        scheduleStatusesToInsert.Add(new T_MESD_CRAFT_SCHEDULE_STATUS
                        {
                            FCRAFT_SCHEDULE_STATUS_ID = GuidHelper.NewGuid(),
                            FCRAFT_SCHEDULE_ID = splitFinishedSchedule.FCRAFT_SCHEDULE_ID,
                            FRELEASE_STATUS = true,
                            FFINISH_QTY = totalQtyToSplit,
                            FPASS_QTY = passQtyToSplit,
                            FNG_QTY = ngQtyToSplit,
                            FPASS_WEIGHT = passQtyToSplit * originalSchedule.FUNIT_WEIGHT,
                            FNG_WEIGHT = ngQtyToSplit * originalSchedule.FUNIT_WEIGHT,
                            FFINISH_WEIGHT = totalQtyToSplit * originalSchedule.FUNIT_WEIGHT,
                            FIF_CLOSE = 1,
                            FCLOSEDATE = curDate,
                            FCLOSER = user.UserPsnName,
                            FCLOSER_ID = user.UserPsnId
                        });

                        var splitJobBooking = await PrepareFinishedJobBookingForSplitAsync(splitFinishedSchedule, passQtyToSplit, ngQtyToSplit, workHours, user, splitJobBillCodes[billCodeIndex]);
                        jobsToInsert.Add(splitJobBooking);

                        result.Entity.Add(new BatchSplitResultModel
                        {
                            FORIGINAL_SCHEDULE_ID = model.FCRAFT_SCHEDULE_ID,
                            FSPLIT_FINISHED_SCHEDULE_ID = splitFinishedSchedule.FCRAFT_SCHEDULE_ID,
                            FSPLIT_FINISHED_SCHEDULE_NO = splitFinishedSchedule.FCRAFT_SCHEDULE_NO,
                            FSPLIT_FINISH_QTY = totalSplitQty,
                            IsSuccess = true,
                            Message = "成功拆分出新排程"
                        });

                        billCodeIndex++;
                    }
                }

                // ===================================================================================
                // 步骤 3: 事务中执行所有数据库操作
                // ===================================================================================
                db.BeginTran();

                if (schedulesToInsert.Any()) await db.Insertable(schedulesToInsert).ExecuteCommandAsync();
                if (scheduleStatusesToInsert.Any()) await db.Insertable(scheduleStatusesToInsert).ExecuteCommandAsync();
                if (jobsToInsert.Any()) await db.Insertable(jobsToInsert).ExecuteCommandAsync();

                var jobsToClear = jobsToClearDict.Values.ToList();
                if (jobsToClear.Any())
                {
                    foreach (var job in jobsToClear)
                    {
                        job.FPASS_QTY = 0;
                        job.FNG_QTY = 0;
                        job.FPASS_WEIGHT = 0;
                        job.FNG_WEIGHT = 0;
                        job.FACT_USE_HOUR = 0;
                    }
                    await db.Updateable(jobsToClear).UpdateColumns(j => new { j.FPASS_QTY, j.FNG_QTY, j.FPASS_WEIGHT, j.FNG_WEIGHT, j.FACT_USE_HOUR }).ExecuteCommandAsync();
                }

                // 更新原始排程的计划数量和批号
                if (schedulesToUpdate.Any()) await db.Updateable(schedulesToUpdate).UpdateColumns(s => new { s.FPLAN_QTY, s.FLOT_NO }).ExecuteCommandAsync();

                if (jobsToUpdate.Any()) await db.Updateable(jobsToUpdate).ExecuteCommandAsync();

                var allAffectedScheduleIds = originalScheduleIds.Union(schedulesToInsert.Select(s => s.FCRAFT_SCHEDULE_ID)).Distinct().ToList();
                if (allAffectedScheduleIds.Any())
                {
                    await UpdateSchedulesFinishAsync(allAffectedScheduleIds, db, user, curDate);
                }

                var allAffectedWoCraftIds = originalSchedules.Values.Select(s => s.FWORK_ORDER_CRAFT_ID).Union(schedulesToInsert.Select(s => s.FWORK_ORDER_CRAFT_ID)).Distinct().ToList();
                if (allAffectedWoCraftIds.Any())
                {
                    await UpdateWorkOrderCraftsFinishAsync(allAffectedWoCraftIds, db);
                }

                db.CommitTran();

                //保存日志
                await SaveBatchFinishWithSplitLogsAsync(splitModels, jobsToUpdate, jobsToInsert, finalizedJobsForDownstream, schedulesToInsert);

                // ===================================================================================
                // 步骤 4: 处理标签逻辑
                // ===================================================================================

                await HandleBatchSplitTagsAsync(splitModels, result.Entity, jobsToInsert, db);

                // ===================================================================================
                // 步骤 5: 事务成功后，触发下游流程创建
                // ===================================================================================

                // 为"部分拆分"创建的报工记录触发下游 - 只传递良品数量
                if (jobsToInsert.Any())
                {
                    await HandleBatchAutoSplitExistingScheduleWithPassQty(jobsToInsert, db);
                }
                // 为"最终完工"更新的报工记录触发下游 - 只传递良品数量
                if (finalizedJobsForDownstream.Any())
                {
                    await HandleBatchAutoSplitExistingScheduleWithPassQty(finalizedJobsForDownstream, db);

                    await GenrateWorkOrderInAsync(finalizedJobsForDownstream);
                }

                // ===================================================================================
                // 步骤 6: 处理自动返工逻辑 - 在当前工序产生返工排程
                // ===================================================================================

                if (enableAutoRework)
                {
                    // 收集所有有不良品的报工记录ID，重新从数据库查询最新状态
                    var jobBookingIdsWithNG = new List<string>();

                    // 添加部分拆分完工的不良品任务ID
                    if (jobsToInsert.Any())
                    {
                        jobBookingIdsWithNG.AddRange(jobsToInsert.Where(j => j.FNG_QTY > 0).Select(j => j.FCRAFT_JOB_BOOKING_ID));
                    }

                    // 添加最终完工的不良品任务ID
                    if (finalizedJobsForDownstream.Any())
                    {
                        jobBookingIdsWithNG.AddRange(finalizedJobsForDownstream.Where(j => j.FNG_QTY > 0).Select(j => j.FCRAFT_JOB_BOOKING_ID));
                    }

                    if (jobBookingIdsWithNG.Any())
                    {
                        // 重新从数据库查询这些报工记录的最新状态
                        var latestJobBookingsWithNG = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                            .Where(j => jobBookingIdsWithNG.Contains(j.FCRAFT_JOB_BOOKING_ID) && j.FNG_QTY > 0)
                            .ToListAsync();

                        if (latestJobBookingsWithNG.Any())
                        {
                            // 在当前工序为不良品创建返工排程（这个方法会自动清空原记录的不良品）
                            await HandleReworkScheduleCreation(latestJobBookingsWithNG, db);
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                db.RollbackTran();
                ERROR(null, 103002, _multiLang["批量完工并拆分批次执行失败：" + ex.Message]);
            }

            return await OK(result);
        }

        /// <summary>
        /// 验证批次拆分参数
        /// </summary>
        /// <param name="models">报工操作模型列表</param>
        /// <returns>验证结果</returns>
        public async Task<DataResult<List<JobBookingOperateModel>>> ValidateBatchSplitAsync(List<JobBookingOperateModel> models)
        {
            var result = new DataResult<List<JobBookingOperateModel>>();
            result.StatusCode = 200;
            result.Entity = models;

            // 基础验证
            if (models == null || !models.Any())
            {
                result.Message = _multiLang["传入参数为空"];
                result.StatusCode = 1010;
                return await OK(result);
            }

            var db = _isugar.DB;

            // 验证需要拆分的任务
            var splitModels = models.Where(m => m.FSPLIT_AND_FLOW).ToList();

            foreach (var model in splitModels)
            {
                // 获取当前排程信息
                var schedule = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                    .Where(s => s.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                    .FirstAsync();

                if (schedule == null)
                {
                    result.Message = string.Format(_multiLang["找不到排程任务 {0}"], model.FCRAFT_SCHEDULE_ID);
                    result.StatusCode = 1010;
                    return await OK(result);
                }

                // 获取已完工数量
                var finishedQty = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                    .Where(j => j.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID && j.FWORK_STATUS == "finished")
                    .SumAsync(j => j.FPASS_QTY + j.FNG_QTY);

                // 根据拆分模式计算实际拆分数量
                decimal actualSplitQty = CalculateActualSplitQty(model, finishedQty);

                // 验证拆分数量
                if (actualSplitQty <= 0)
                {
                    result.Message = string.Format(_multiLang["任务 {0} 计算出的拆分数量必须大于0，当前为 {1}"],
                        model.FCRAFT_SCHEDULE_ID, actualSplitQty);
                    result.StatusCode = 1010;
                    return await OK(result);
                }

                var totalFinishQty = finishedQty + (model.FSPLIT_MODE == "total_finished" ? 0 : actualSplitQty);

                // 验证拆分数量不能超过计划数量（除非启用强制拆分）
                if (!model.FFORCE_SPLIT && totalFinishQty > schedule.FPLAN_QTY)
                {
                    result.Message = string.Format(_multiLang["任务 {0} 总完工数量 {1} 超过计划数量 {2}"],
                        model.FCRAFT_SCHEDULE_ID, totalFinishQty, schedule.FPLAN_QTY);
                    result.StatusCode = 1010;
                    return await OK(result);
                }
            }

            return await OK(result);
        }

        /// <summary>
        /// 判断是否为有效的拆分请求
        /// </summary>
        /// <param name="model">报工操作模型</param>
        /// <returns>是否为有效拆分请求</returns>
        private bool IsValidSplitRequest(JobBookingOperateModel model)
        {
            if (!model.FSPLIT_AND_FLOW) return false;

            switch (model.FSPLIT_MODE?.ToLower())
            {
                case "use_pass_qty":
                default:
                    // 使用合格数量模式：只要有合格数量就是有效的拆分请求
                    return model.FPASS_QTY > 0;

                case "specify_qty":
                    // 指定数量模式：需要指定拆分数量
                    return model.FSPLIT_FINISH_QTY > 0;

                case "total_finished":
                    // 全部完工模式：只要有合格或不合格数量就是有效的拆分请求
                    return (model.FPASS_QTY + model.FNG_QTY) > 0;
            }
        }

        /// <summary>
        /// 根据拆分模式计算实际拆分数量
        /// </summary>
        /// <param name="model">报工操作模型</param>
        /// <param name="existingFinishedQty">已完工数量</param>
        /// <returns>实际拆分数量</returns>
        private decimal CalculateActualSplitQty(JobBookingOperateModel model, decimal existingFinishedQty)
        {
            switch (model.FSPLIT_MODE?.ToLower())
            {
                case "use_pass_qty":
                default:
                    // 使用本次良品数量（不包括历史，只拆分当前这次的数量）
                    return model.FPASS_QTY;
                case "specify_qty":
                    // 使用指定的拆分数量
                    return model.FSPLIT_FINISH_QTY;
                case "total_finished":
                    // 拆分所有已完工数量（历史 + 本次）
                    return existingFinishedQty + model.FPASS_QTY + model.FNG_QTY;
            }
        }

        /// <summary>
        /// 处理普通完工任务
        /// </summary>
        /// <param name="models">普通完工模型列表</param>
        /// <param name="db">数据库连接</param>
        /// <param name="user">用户信息</param>
        private async Task ProcessNormalFinishJobsAsync(List<JobBookingOperateModel> models, ISqlSugarClient db, UserAccountModel user)
        {
            // 这里可以调用现有的完工逻辑，暂时简化处理
            foreach (var model in models)
            {
                // 创建完工报工记录
                await CreateOrUpdateJobBookingForSplitAsync(model, db, user, OperateType.finish);
            }
        }

        /// <summary>
        /// 处理拆分完工任务
        /// </summary>
        /// <param name="model">拆分模型</param>
        /// <param name="db">数据库连接</param>
        /// <param name="user">用户信息</param>
        /// <returns>拆分结果</returns>
        private async Task<BatchSplitResultModel> ProcessSplitFinishJobAsync(JobBookingOperateModel model, ISqlSugarClient db, UserAccountModel user)
        {
            var splitResult = new BatchSplitResultModel
            {
                FORIGINAL_JOB_BOOKING_ID = model.FCRAFT_JOB_BOOKING_ID,
                FORIGINAL_SCHEDULE_ID = model.FCRAFT_SCHEDULE_ID,
                IsSuccess = false
            };

            try
            {
                // =================================================================================
                // 步骤 1: 查找所有需要被拆分出去的 "源头" 数据
                // =================================================================================

                // 1.1 获取正在被操作的原始排程
                var originalSchedule = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                    .Where(s => s.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                    .FirstAsync();

                if (originalSchedule == null)
                {
                    splitResult.Message = "找不到原始排程任务";
                    return splitResult;
                }
                splitResult.FORIGINAL_LOT_NO = originalSchedule.FLOT_NO;

                // 1.2 查找历史上所有已完成且未被拆分的加工记录 (Job Bookings)
                var historicalFinishedJobs = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                    .Where(j => j.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID && j.FWORK_STATUS == "finished")
                    .ToListAsync();

                // 1.3 查找触发本次操作的那个加工记录
                var triggeringJobBooking = string.IsNullOrWhiteSpace(model.FCRAFT_JOB_BOOKING_ID)
                    ? null
                    : await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().InSingleAsync(model.FCRAFT_JOB_BOOKING_ID);


                // =================================================================================
                // 步骤 2: 计算需要拆分的总数量和总工时
                // =================================================================================

                // 2.1 计算历史已完工数
                var existingFinishedQty = historicalFinishedJobs.Sum(j => j.FPASS_QTY + j.FNG_QTY);

                // 2.2 根据拆分模式，计算最终要拆分出去的总数量
                var totalSplitQty = CalculateActualSplitQty(model, existingFinishedQty);

                // 2.3 计算总工时：历史工时 + 本次操作产生的工时
                var totalWorkHours = historicalFinishedJobs.Sum(j => j.FACT_USE_HOUR);
                var currentTime = _iauth.GetCurDateTime();

                if (triggeringJobBooking != null)
                {
                    // 加上本次操作产生的工时
                    totalWorkHours += CalcActUseHour(
                        triggeringJobBooking.FACT_USE_HOUR,
                        triggeringJobBooking.FLAST_RESUME_DATE,
                        triggeringJobBooking.FACT_ST_DATE,
                        currentTime,
                        triggeringJobBooking.FWORK_STATUS
                    );
                }


                // =================================================================================
                // 步骤 3: 执行拆分操作
                // =================================================================================

                // 3.1 更新原排程A的计划数 (FPLAN_QTY)
                originalSchedule.FPLAN_QTY -= totalSplitQty;
                await db.Updateable(originalSchedule)
                    .UpdateColumns(s => s.FPLAN_QTY)
                    .ExecuteCommandAsync();

                // 3.2 创建新的、已完工的排程B，并将汇总后的总数量和总工时赋予它
                var splitFinishedSchedule = await CreateSplitFinishedScheduleAsync(originalSchedule, totalSplitQty, totalWorkHours, db, user);
                splitResult.FSPLIT_FINISHED_SCHEDULE_ID = splitFinishedSchedule.FCRAFT_SCHEDULE_ID;
                splitResult.FSPLIT_FINISHED_SCHEDULE_NO = splitFinishedSchedule.FCRAFT_SCHEDULE_NO;
                splitResult.FSPLIT_FINISHED_LOT_NO = splitFinishedSchedule.FLOT_NO;

                // 3.3 【关键修复】将所有贡献了拆分数量的源头加工记录"清零"
                var allSourceJobsToClear = new List<T_MESD_CRAFT_JOB_BOOKING>();
                allSourceJobsToClear.AddRange(historicalFinishedJobs);
                if (triggeringJobBooking != null && !allSourceJobsToClear.Any(j => j.FCRAFT_JOB_BOOKING_ID == triggeringJobBooking.FCRAFT_JOB_BOOKING_ID))
                {
                    allSourceJobsToClear.Add(triggeringJobBooking);
                }

                if (allSourceJobsToClear.Any())
                {
                    foreach (var job in allSourceJobsToClear)
                    {
                        job.FWORK_STATUS = WorkStatus.finished.ToString(); // 标记其汇报任务已完成
                        job.FACT_ED_DATE = currentTime;
                        job.FPASS_QTY = 0;
                        job.FNG_QTY = 0;
                        job.FPASS_WEIGHT = 0;
                        job.FNG_WEIGHT = 0;
                        job.FACT_USE_HOUR = 0; // 核心：将工时清零
                    }
                    // 批量更新，将这些记录从原排程的统计中"移除"
                    await db.Updateable(allSourceJobsToClear)
                            .UpdateColumns(it => new { it.FWORK_STATUS, it.FACT_ED_DATE, it.FPASS_QTY, it.FNG_QTY, it.FPASS_WEIGHT, it.FNG_WEIGHT, it.FACT_USE_HOUR })
                            .ExecuteCommandAsync();
                }

                // 3.4 生成下游工序任务
                var downstreamTasks = await CreateDownstreamTasksAsync(originalSchedule, splitFinishedSchedule, totalSplitQty, db, user);
                splitResult.DownstreamTasks = downstreamTasks;

                // 3.5 更新原排程A的状态（此时因为其下所有完工job都已清零，它不会被错误地关闭）
                await UpdateScheduleStatusAfterSplitAsync(model.FCRAFT_SCHEDULE_ID, db, user);


                // =================================================================================
                // 步骤 4: 设置最终返回结果
                // =================================================================================
                splitResult.FSPLIT_FINISH_QTY = totalSplitQty;
                splitResult.IsSuccess = true;
                splitResult.Message = "拆分成功";
            }
            catch (Exception ex)
            {
                splitResult.Message = $"拆分失败：{ex.Message}";
                throw; // 抛出异常，以便外层事务可以回滚
            }

            return splitResult;
        }

        /// <summary>
        /// 创建或更新报工记录（拆分专用）
        /// </summary>
        private async Task<T_MESD_CRAFT_JOB_BOOKING> CreateOrUpdateJobBookingForSplitAsync(JobBookingOperateModel model, ISqlSugarClient db, UserAccountModel user, OperateType operateType)
        {
            var curDate = _iauth.GetCurDateTime();

            T_MESD_CRAFT_JOB_BOOKING jobBooking;

            if (!string.IsNullOrWhiteSpace(model.FCRAFT_JOB_BOOKING_ID))
            {
                // 更新现有报工记录
                jobBooking = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                    .Where(j => j.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                    .FirstAsync();

                if (jobBooking != null)
                {
                    jobBooking.FPASS_QTY = model.FPASS_QTY;
                    jobBooking.FNG_QTY = model.FNG_QTY;
                    jobBooking.FWORK_STATUS = operateType.ToString();
                    jobBooking.FACT_ED_DATE = operateType == OperateType.finish ? curDate : null;

                    await db.Updateable(jobBooking).ExecuteCommandAsync();
                }
            }
            else
            {
                // 创建新的报工记录
                var billCodes = await GetBillCodesAsync(1, false);

                jobBooking = new T_MESD_CRAFT_JOB_BOOKING
                {
                    FCRAFT_JOB_BOOKING_ID = GuidHelper.NewGuid(),
                    FCRAFT_JOB_BOOKING_NO = billCodes[0],
                    FCRAFT_SCHEDULE_ID = model.FCRAFT_SCHEDULE_ID,
                    FACT_ST_DATE = curDate,
                    FACT_ED_DATE = operateType == OperateType.finish ? curDate : null,
                    FPASS_QTY = model.FPASS_QTY,
                    FNG_QTY = model.FNG_QTY,
                    FWORK_STATUS = operateType.ToString(),
                    FEMP_ID = user.UserPsnId,
                    FEMP_NAME = user.UserPsnName,
                    FWORK_ORDER_ID = model.FWORK_ORDER_ID,
                    FWORK_ORDER_CRAFT_ID = await GetWorkOrderCraftIdForSplitAsync(model.FCRAFT_SCHEDULE_ID, db),
                    FCRAFT_ID = model.FCRAFT_ID,
                    FMATERIAL_ID = model.FMATERIAL_ID,
                    FSTATION_ID = model.FSTATION_ID
                };

                await db.Insertable(jobBooking).ExecuteCommandAsync();
            }

            return jobBooking;
        }

        /// <summary>
        /// 创建拆分的已完工排程
        /// </summary>
        private async Task<T_MESD_CRAFT_SCHEDULE> CreateSplitFinishedScheduleAsync(T_MESD_CRAFT_SCHEDULE originalSchedule, decimal splitQty, decimal workHours, ISqlSugarClient db, UserAccountModel user)
        {
            var billCodes = await GetBillCodesAsync(1, true);
            var splitLotNo = await GenerateSplitLotNoAsync(originalSchedule.FWORK_ORDER_ID, originalSchedule.FWORK_ORDER_CRAFT_ID, db);

            var splitSchedule = new T_MESD_CRAFT_SCHEDULE
            {
                FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid(),
                FCRAFT_SCHEDULE_NO = billCodes[0],
                FQRCODE = billCodes[0],
                SCHEDULE_FSHOW_SEQNO = 999,
                FPARENT_CRAFT_SCHEDULE_ID = originalSchedule.FCRAFT_SCHEDULE_ID,
                FSCHEDULE_BATCH_ID = originalSchedule.FSCHEDULE_BATCH_ID,
                FWORK_ORDER_ID = originalSchedule.FWORK_ORDER_ID,
                FMATERIAL_ID = originalSchedule.FMATERIAL_ID,
                FSALE_ORDER_ID = originalSchedule.FSALE_ORDER_ID,
                FWORK_ORDER_CRAFT_ID = originalSchedule.FWORK_ORDER_CRAFT_ID,
                FCRAFT_ID = originalSchedule.FCRAFT_ID,
                FSTATION_ID = originalSchedule.FSTATION_ID,
                FPLAN_QTY = splitQty,
                FORI_PLAN_QTY = splitQty,
                FLOT_NO = splitLotNo,
                FPLAN_ST_DATE = originalSchedule.FPLAN_ST_DATE,
                FPLAN_ED_DATE = _iauth.GetCurDateTime(),
                FREMARK = $"从排程 {originalSchedule.FCRAFT_SCHEDULE_NO} 拆分的已完工任务"
            };

            await db.Insertable(splitSchedule).ExecuteCommandAsync();

            // 创建对应的状态记录
            var splitStatus = new T_MESD_CRAFT_SCHEDULE_STATUS
            {
                FCRAFT_SCHEDULE_STATUS_ID = GuidHelper.NewGuid(),
                FCRAFT_SCHEDULE_ID = splitSchedule.FCRAFT_SCHEDULE_ID,
                FRELEASE_STATUS = true,
                FFINISH_QTY = splitQty
            };

            await db.Insertable(splitStatus).ExecuteCommandAsync();

            // 为拆分的已完工排程创建对应的已完工报工明细, 并传递工时
            var splitJobBooking = await CreateFinishedJobBookingForSplitAsync(splitSchedule, splitQty, workHours, user, db);

            // 更新拆分排程的完工状态（标记为已完工）
            await UpdateSchedulesFinishAsync(new List<string> { splitSchedule.FCRAFT_SCHEDULE_ID }, db, user, _iauth.GetCurDateTime());

            // 更新工单工艺完工数
            await UpdateWorkOrderCraftsFinishAsync(new List<string> { splitSchedule.FWORK_ORDER_CRAFT_ID }, db);

            return splitSchedule;
        }

        /// <summary>
        /// 为拆分的已完工排程创建报工明细
        /// </summary>
        /// <param name="splitSchedule">拆分排程</param>
        /// <param name="finishedQty">完工数量</param>
        /// <param name="workHours">工时</param>
        /// <param name="user">用户信息</param>
        /// <param name="db">数据库连接</param>
        /// <returns>报工记录</returns>
        private async Task<T_MESD_CRAFT_JOB_BOOKING> CreateFinishedJobBookingForSplitAsync(T_MESD_CRAFT_SCHEDULE splitSchedule, decimal finishedQty, decimal workHours, UserAccountModel user, ISqlSugarClient db)
        {
            var curDate = _iauth.GetCurDateTime();
            var billCodes = await GetBillCodesAsync(1, false);

            // 获取工单信息以计算重量
            var workOrder = await db.Queryable<T_MESD_WORK_ORDER>()
                .Where(w => w.FWORK_ORDER_ID == splitSchedule.FWORK_ORDER_ID)
                .FirstAsync();

            var unitWeight = workOrder?.FUNIT_WEIGHT ?? 0;

            var jobBooking = new T_MESD_CRAFT_JOB_BOOKING
            {
                FCRAFT_JOB_BOOKING_ID = GuidHelper.NewGuid(),
                FCRAFT_JOB_BOOKING_NO = billCodes[0],
                FCRAFT_SCHEDULE_ID = splitSchedule.FCRAFT_SCHEDULE_ID,
                FACT_ST_DATE = curDate,
                FACT_ED_DATE = curDate,
                FACT_USE_HOUR = workHours, // <-- 应用传递进来的正确工时
                FPASS_QTY = finishedQty,
                FNG_QTY = 0,
                FPASS_WEIGHT = finishedQty * unitWeight,
                FNG_WEIGHT = 0,
                FWORK_STATUS = "finished",
                FEMP_ID = user.UserPsnId,
                FEMP_NAME = user.UserPsnName,
                FWORK_ORDER_ID = splitSchedule.FWORK_ORDER_ID,
                FWORK_ORDER_CRAFT_ID = splitSchedule.FWORK_ORDER_CRAFT_ID,
                FCRAFT_ID = splitSchedule.FCRAFT_ID,
                FMATERIAL_ID = splitSchedule.FMATERIAL_ID,
                FSTATION_ID = splitSchedule.FSTATION_ID,
                FSALE_ORDER_ID = splitSchedule.FSALE_ORDER_ID,
                FBAD_REASON_ID = "",
                FCHECK_PERSON_ID = "",
            };

            await db.Insertable(jobBooking).ExecuteCommandAsync();
            return jobBooking;
        }

        /// <summary>
        /// 创建下游工序任务
        /// </summary>
        private async Task<List<DownstreamTaskInfo>> CreateDownstreamTasksAsync(T_MESD_CRAFT_SCHEDULE originalSchedule, T_MESD_CRAFT_SCHEDULE splitSchedule, decimal qty, ISqlSugarClient db, UserAccountModel user)
        {
            var downstreamTasks = new List<DownstreamTaskInfo>();
            var releaseImmediately = await GetSysParamValue(_MES_AutoSplitReleaseImmediately) == "1";

            // 获取工单的所有工艺，按顺序排列
            var allCrafts = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>()
                .Where(c => c.FWORK_ORDER_ID == originalSchedule.FWORK_ORDER_ID)
                .OrderBy(c => c.FSHOW_SEQNO)
                .ToListAsync();

            // 找到当前工艺在序列中的位置
            var currentCraftIndex = allCrafts.FindIndex(c => c.FWORK_ORDER_CRAFT_ID == originalSchedule.FWORK_ORDER_CRAFT_ID);

            if (currentCraftIndex >= 0 && currentCraftIndex < allCrafts.Count - 1)
            {
                // 获取下一个工艺
                var nextCraft = allCrafts[currentCraftIndex + 1];

                // 检查是否已存在下游排程
                var existingDownstreamSchedule = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                    .Where(s => s.FWORK_ORDER_ID == originalSchedule.FWORK_ORDER_ID &&
                               s.FWORK_ORDER_CRAFT_ID == nextCraft.FWORK_ORDER_CRAFT_ID &&
                               s.FPARENT_CRAFT_SCHEDULE_ID == null)
                    .FirstAsync();

                if (existingDownstreamSchedule != null)
                {
                    // 创建子排程
                    var downstreamChildSchedule = await CreateDownstreamChildScheduleAsync(existingDownstreamSchedule, splitSchedule, qty, db, user, releaseImmediately);

                    var taskInfo = new DownstreamTaskInfo
                    {
                        FCRAFT_SCHEDULE_ID = downstreamChildSchedule.FCRAFT_SCHEDULE_ID,
                        FCRAFT_SCHEDULE_NO = downstreamChildSchedule.FCRAFT_SCHEDULE_NO,
                        FCRAFT_ID = downstreamChildSchedule.FCRAFT_ID,
                        FSTATION_ID = downstreamChildSchedule.FSTATION_ID,
                        FPLAN_QTY = qty,
                        FLOT_NO = downstreamChildSchedule.FLOT_NO,
                        FRELEASE_STATUS = releaseImmediately
                    };

                    downstreamTasks.Add(taskInfo);

                    // 更新父排程数量
                    existingDownstreamSchedule.FPLAN_QTY -= qty;
                    await db.Updateable(existingDownstreamSchedule)
                        .UpdateColumns(s => s.FPLAN_QTY)
                        .ExecuteCommandAsync();
                }
            }

            return downstreamTasks;
        }

        /// <summary>
        /// 创建下游子排程
        /// </summary>
        private async Task<T_MESD_CRAFT_SCHEDULE> CreateDownstreamChildScheduleAsync(T_MESD_CRAFT_SCHEDULE parentSchedule, T_MESD_CRAFT_SCHEDULE sourceSchedule, decimal qty, ISqlSugarClient db, UserAccountModel user, bool releaseImmediately)
        {
            var billCodes = await GetBillCodesAsync(1, true);

            var childSchedule = new T_MESD_CRAFT_SCHEDULE
            {
                FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid(),
                FCRAFT_SCHEDULE_NO = billCodes[0],
                FQRCODE = billCodes[0],
                FPARENT_CRAFT_SCHEDULE_ID = parentSchedule.FCRAFT_SCHEDULE_ID,
                FSCHEDULE_BATCH_ID = parentSchedule.FSCHEDULE_BATCH_ID,
                FWORK_ORDER_ID = parentSchedule.FWORK_ORDER_ID,
                FMATERIAL_ID = parentSchedule.FMATERIAL_ID,
                FSALE_ORDER_ID = parentSchedule.FSALE_ORDER_ID,
                FWORK_ORDER_CRAFT_ID = parentSchedule.FWORK_ORDER_CRAFT_ID,
                FCRAFT_ID = parentSchedule.FCRAFT_ID,
                FSTATION_ID = parentSchedule.FSTATION_ID,
                FPLAN_QTY = qty,
                FORI_PLAN_QTY = qty,
                FLOT_NO = sourceSchedule.FLOT_NO, // 使用拆分排程的批号
                FPLAN_ST_DATE = _iauth.GetCurDateTime(),
                FPLAN_ED_DATE = _iauth.GetCurDateTime().AddHours(1),
                FSOURCE_JOB_BOOKING_ID = sourceSchedule.FCRAFT_SCHEDULE_ID,
                FREMARK = $"由拆分排程 {sourceSchedule.FCRAFT_SCHEDULE_NO} 生成的下游任务"
            };

            await db.Insertable(childSchedule).ExecuteCommandAsync();

            // 创建对应的状态记录
            var childStatus = new T_MESD_CRAFT_SCHEDULE_STATUS
            {
                FCRAFT_SCHEDULE_STATUS_ID = GuidHelper.NewGuid(),
                FCRAFT_SCHEDULE_ID = childSchedule.FCRAFT_SCHEDULE_ID,
                FRELEASE_STATUS = releaseImmediately
            };

            await db.Insertable(childStatus).ExecuteCommandAsync();

            return childSchedule;
        }

        /// <summary>
        /// 生成唯一的、满足 L{YY}{TTT}{CC} 格式的8位批号。
        /// (时间分段 + 线程安全计数器)
        /// </summary>
        /// <returns>新的唯一批号</returns>
        private Task<string> GenerateLotNumberBase()
        {
            // lock确保在多线程环境下，对静态变量的读写是安全的
            lock (_lotNumberLock)
            {
                var now = _iauth.GetCurDateTime();
                string yearYY = now.ToString("yy");

                // 计算当前所属的时间块 (TTT)
                // 一天有86400秒，除以10，得到0-8639，正好是4位。
                // 为了满足3位，我们将粒度放大到100秒
                int timeBlockValue = (int)now.TimeOfDay.TotalSeconds / 100; // 每天分为0-863个块
                string timeBlockTTT = timeBlockValue.ToString("D3"); // 格式化为3位数字, e.g., "001", "123"

                string currentTimeBlock = $"{yearYY}{timeBlockTTT}"; // e.g., "25123"

                // 判断是否进入了新的时间块
                if (currentTimeBlock != _lastTimeBlock)
                {
                    // 如果是新的时间块，重置计数器和上一次的时间块记录
                    _sequenceCounter = 0;
                    _lastTimeBlock = currentTimeBlock;
                }

                // 获取当前的序列号并递增
                int currentSequence = _sequenceCounter;
                _sequenceCounter++;

                // 检查计数器是否超出范围（2位，0-99）
                if (currentSequence > 99)
                {
                    // 理论上在100秒内生成超过100个批号的可能性极小，但作为健壮性代码，需要处理此情况。
                    // 可以选择抛出异常，或等待到下一个时间块。这里我们选择抛出异常。
                    throw new InvalidOperationException("批号生成过于频繁，在当前时间块内已超出序列上限。请稍后重试。");
                }

                string sequenceCC = currentSequence.ToString("D2"); // 格式化为2位数字, e.g., "00", "01"

                // 组合成最终的8位批号
                string lotNumber = $"{currentTimeBlock}{sequenceCC}";

                return Task.FromResult(lotNumber);
            }
        }

        /// <summary>
        /// 为拆分的排程生成唯一批号
        /// </summary>
        /// <param name="workOrderId">工单ID</param>
        /// <param name="craftId">工单工艺ID</param>
        /// <param name="db">数据库上下文</param>
        /// <returns>新的唯一批号</returns>
        private async Task<string> GenerateSplitLotNoAsync(string workOrderId, string craftId, ISqlSugarClient db)
        {
            // 1. 标准方法生成批号的基础部分 (例如: "L2540451")
            string lotBase = await GenerateLotNumberBase();
            return $"L{lotBase}";
            //// 2. 查询数据库，找出当前工单工艺下，所有使用相同基础批号的现有记录
            //var existingLots = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
            //    .Where(p => p.FWORK_ORDER_ID == workOrderId &&
            //                p.FWORK_ORDER_CRAFT_ID == craftId &&
            //                p.FLOT_NO.StartsWith(lotBase))
            //    .Select(p => p.FLOT_NO)
            //    .ToListAsync();

            //// 3. 从现有批号中解析出最大的序列号
            //int maxSeq = 0;
            //if (existingLots.Any())
            //{
            //    maxSeq = existingLots
            //        // 安全地截取并转换序列号部分为整数
            //        .Select(lot => int.TryParse(lot.Substring(lotBase.Length), out int seq) ? seq : 0)
            //        .DefaultIfEmpty(0)
            //        .Max();
            //}

            //// 4. 计算下一个可用的序列号
            //int nextSeq = maxSeq + 1;

            //// 5. 生成新的唯一批号，使用3位补零的序列号
            //return $"{lotBase}{nextSeq:D3}";
        }

        /// <summary>
        /// 获取工单工艺ID（拆分专用）
        /// </summary>
        private async Task<string> GetWorkOrderCraftIdForSplitAsync(string scheduleId, ISqlSugarClient db)
        {
            var schedule = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .Where(s => s.FCRAFT_SCHEDULE_ID == scheduleId)
                .FirstAsync();

            return schedule?.FWORK_ORDER_CRAFT_ID ?? "";
        }

        /// <summary>
        /// 更新拆分后的排程状态
        /// </summary>
        private async Task UpdateScheduleStatusAfterSplitAsync(string scheduleId, ISqlSugarClient db, UserAccountModel user)
        {
            var curDate = _iauth.GetCurDateTime();

            // 更新排程状态
            await UpdateSchedulesFinishAsync(new List<string> { scheduleId }, db, user, curDate);
        }

        #endregion

        #region 批量完工验证（原有方法）

        /// <summary>
        ///  验证完工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<JobBookingOperateModel>>> BatchValidateFinishJobAsync(List<JobBookingOperateModel> models)
        {
            var result = new DataResult<List<JobBookingOperateModel>>();
            result.StatusCode = 200;
            var nullModels = models.Where(p => p == null).ToList();
            if (nullModels.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "models");
                result.StatusCode = 1010;
                return await OK(result);
            }
            nullModels = models.Where(p => string.IsNullOrWhiteSpace(p.FCRAFT_JOB_BOOKING_ID)).ToList();
            if (nullModels.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model.FCRAFT_JOB_BOOKING_ID");
                result.StatusCode = 1010;
                return await OK(result);
            }

            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                result.Message = _multiLang["执行失败, 当前用户员工信息为空."];
                result.StatusCode = 1010;
                return await OK(result);
            }
            var jobIds = models.Select(p => p.FCRAFT_JOB_BOOKING_ID).ToList();


            //加工任务
            var jobData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT_STATUS>
                ((job, sch, schStatus, wo, woStatus, woCraftStatus) => new JoinQueryInfos(
                    JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                    JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                    JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                    JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                    JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == woCraftStatus.FWORK_ORDER_CRAFT_ID))
                .Where((job, sch, schStatus, wo, woStatus, woCraftStatus) =>
                jobIds.Contains(job.FCRAFT_JOB_BOOKING_ID))
                .Select((job, sch, schStatus, wo, woStatus, woCraftStatus) => new
                {
                    FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                    FCRAFT_JOB_BOOKING_NO = job.FCRAFT_JOB_BOOKING_NO,

                    FCRAFT_ID = job.FCRAFT_ID,
                    FSTATION_ID = job.FSTATION_ID,

                    FWORK_STATUS = job.FWORK_STATUS,

                    FEMP_ID = job.FEMP_ID,
                    FEMP_NAME = job.FEMP_NAME,

                    FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,

                    FIF_CLOSE_SCHEDULE = schStatus.FIF_CLOSE,
                    FIF_CLOSE_WORK_ORDER = woStatus.FIF_CLOSE,

                    FIF_CANCEL_WORK_ORDER = woStatus.FIF_CANCEL,
                    FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                    FPLAN_QTY = sch.FPLAN_QTY,
                    FFINISH_QTY = schStatus.FFINISH_QTY,

                    FBOOKING_TYPE = wo.FBOOKING_TYPE,

                    FWORK_ORDER_ID = wo.FWORK_ORDER_ID,
                    FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,

                    FFINISH_QTY_CRAFT = woCraftStatus.FFINISH_QTY,

                    FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                })
                .ToListAsync();

            if (jobData == null)
            {
                result.Message = _multiLang["加工任务数据不存在, 请查正."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            var errorJobDatas = jobData.Select(p => p.FCRAFT_ID).Distinct().ToList();
            if (errorJobDatas.Count > 1)
            {
                result.Message = _multiLang["批量完工不允许多工艺操作!"];
                result.StatusCode = 1010;
                return await OK(result);
            }
            errorJobDatas = jobData.Select(p => p.FSTATION_ID).Distinct().ToList();
            if (errorJobDatas.Count > 1)
            {
                result.Message = _multiLang["批量完工不允许多工位操作!"];
                result.StatusCode = 1010;
                return await OK(result);
            }

            //判断工单入库是否已审核
            var workInDatas = await db.Queryable<T_MESD_WORK_IN_PRODUCT, T_MESD_WORK_IN, T_MESD_WORK_IN_STATUS>((a, b, c) =>
            new JoinQueryInfos(
                JoinType.Left, a.FWORK_IN_ID == b.FWORK_IN_ID,
                JoinType.Left, b.FWORK_IN_ID == c.FWORK_IN_ID))
                .Where((a, b, c) => jobIds.Contains(a.FCRAFT_JOB_BOOKING_ID) && c.FCFLAG == 1)
                .Select((a, b, c) => new { a.FCRAFT_JOB_BOOKING_ID, a.FCRAFT_JOB_BOOKING_NO, b.FWORK_IN_NO, c.FCFLAG }).ToListAsync();

            if (workInDatas.Count > 0)
            {
                result.Message = $"加工任务单 {workInDatas[0].FCRAFT_JOB_BOOKING_NO} 工单入库单 {workInDatas[0].FWORK_IN_NO} 已审核";
                result.StatusCode = 1010;
                return await OK(result);
            }

            //取出工位
            string station = string.Empty;
            var stationIds = models.Select(p => p.FSTATION_ID).Distinct().ToList();
            var stationResult = await GetStationByIdsAsync(stationIds);
            if (stationResult.StatusCode == 200 && stationResult.Entity != null && stationResult.Entity.Count > 0)
            {
                station = string.Concat(stationResult.Entity[0].FSTATION_CODE, "/", stationResult.Entity[0].FSTATION_NAME);
            }

            //暂时放开，后面做参数控制
            //只能由本人取消开工
            /*
            if (jobData.FEMP_ID != user.UserPsnId)
            {
                ERROR(null, 103006, string.Format(_multiLang["执行失败, 加工任务 {0}, 是由{1}开工的, 请通知本人操作."],
                 jobData.FCRAFT_JOB_BOOKING_NO, jobData.FEMP_NAME));
            }
            */
            var errorStatus = jobData.Where(p => p.FWORK_STATUS == WorkStatus.cancel.ToString()).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            //检查状态
            if (errorStatus.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 加工任务 {0}, 已取消加工."], string.Join(",", errorStatus));
                result.StatusCode = 1010;
                return await OK(result);
            }
            errorStatus = jobData.Where(p => p.FWORK_STATUS == WorkStatus.finished.ToString() && !models[0].FEXTERNAL_QUOTE).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            if (errorStatus.Count > 0)
            {
                result.Message = string.Format(_multiLang["加工任务 {0}, 已完工, 不能重复加工."], string.Join(",", errorStatus));
                result.StatusCode = 1010;
                return await OK(result);
            }

            //检查排程任务是否已结案
            errorStatus = jobData.Where(p => (p.FIF_CLOSE_SCHEDULE == 1 || p.FIF_CLOSE_SCHEDULE == 3) && !models[0].FEXTERNAL_QUOTE).Select(p => p.FCRAFT_JOB_BOOKING_NO).ToList();
            if (errorStatus.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 排程任务 {0}, 已结案."], string.Join(",", errorStatus));
                result.StatusCode = 1010;
                return await OK(result);
            }

            //检查工单是否已结案
            errorStatus = jobData.Where(p => p.FIF_CLOSE_WORK_ORDER == 1 || p.FIF_CLOSE_WORK_ORDER == 3).Select(p => p.FWORK_ORDER_NO).ToList();
            if (errorStatus.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 工单 {0}, 已结案."], string.Join(",", errorStatus));
                result.StatusCode = 1010;
                return await OK(result);
            }

            //检查工单是否已作废
            errorStatus = jobData.Where(p => p.FIF_CANCEL_WORK_ORDER).Select(p => p.FWORK_ORDER_NO).ToList();
            if (errorStatus.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 工单 {0}, 已作废."], string.Join(",", errorStatus));
                result.StatusCode = 1010;
                return await OK(result);
                ERROR(null, 103011, string.Format(_multiLang["执行失败, 工单 {0}, 已作废."], string.Join(",", errorStatus)));
            }

            //检查是否为工单工艺报工
            errorStatus = jobData.Where(p => p.FBOOKING_TYPE != _wocraft).Select(p => p.FWORK_ORDER_NO).ToList();
            if (errorStatus.Count > 0)
            {
                result.Message = string.Format(_multiLang["执行失败, 工单 {0} 的报工方式不是按工单工艺报工."], string.Join(",", errorStatus));
                result.StatusCode = 1010;
                return await OK(result);
            }


            var errorModels = models.Where(model => model.FPASS_QTY <= 0 && model.FNG_QTY <= 0 && !model.FEXTERNAL_QUOTE).ToList();
            if (errorModels.Count > 0)
            {
                result.Message = _multiLang["执行失败, 合格数量与不良数量不能同时为0."];
                result.StatusCode = 1010;
                return await OK(result);
            }

            var jobDataAll = from modelInfo in models
                             join jobInfo in jobData on modelInfo.FCRAFT_JOB_BOOKING_ID equals jobInfo.FCRAFT_JOB_BOOKING_ID
                             select new
                             {
                                 jobInfo.FCRAFT_JOB_BOOKING_ID,
                                 jobInfo.FCRAFT_JOB_BOOKING_NO,
                                 jobInfo.FCRAFT_SCHEDULE_NO,
                                 jobInfo.FPLAN_QTY,
                                 jobInfo.FFINISH_QTY,
                                 jobInfo.FFINISH_QTY_CRAFT,
                                 jobInfo.FPRO_UNIT_ID,
                                 jobInfo.FWORK_ORDER_ID,
                                 jobInfo.FWORK_ORDER_CRAFT_ID,
                                 modelInfo.FPASS_QTY,
                                 modelInfo.FNG_QTY,
                                 modelInfo.FEXTERNAL_QUOTE
                             };
            var messageStr = "";

            //根据参数检查，是否允许超出排程未完工数量
            if (await GetSysParamValue(_MES_FinishQtyAllowGreatSch) == "0")
            {
                //检查是否超出排程未完工数
                var errorDatas = jobDataAll.Where(p => p.FPASS_QTY + p.FNG_QTY > p.FPLAN_QTY - p.FFINISH_QTY).ToList();
                if (errorDatas.Count > 0)
                {

                    foreach (var item in errorDatas)
                    {
                        messageStr += string.Format(_multiLang["本次合格数 {0}, 本次不良数 {1}, 加总已超出排程任务 {2} 未完工数{3}."],
                        item.FPASS_QTY.ToString("#,##0.######"),
                        item.FNG_QTY.ToString("#,##0.######"),
                        item.FCRAFT_SCHEDULE_NO,
                        (item.FPLAN_QTY - item.FFINISH_QTY).ToString("#,##0.######"));
                    }
                    result.Message = messageStr;
                    result.StatusCode = 1010;
                    return await OK(result);
                }
                errorDatas = jobDataAll.Where(p => p.FEXTERNAL_QUOTE && p.FPASS_QTY < 0 && p.FFINISH_QTY_CRAFT + p.FPASS_QTY < 0).ToList();
                if (errorDatas.Count > 0)
                {
                    foreach (var item in errorDatas)
                    {
                        messageStr += string.Format(_multiLang["本次合格数 {0}, 本次不良数 {1}, 已完工数加总已小于0."],
                           item.FPASS_QTY.ToString("#,##0.######"),
                           item.FNG_QTY.ToString("#,##0.######"));
                    }
                    result.Message = messageStr;
                    result.StatusCode = 1010;
                    return await OK(result);
                }
            }

            //后工艺报工允许超过前工艺的完工总数量(0-则不允许)
            if (await GetSysParamValue(_MES_FinishQtyAllowGreatePre) == "0")
            {
                var woOrdIds = jobData.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();
                //检查前一工艺是否有完工记录
                var woCraftAlls = (await db.Queryable<T_MESD_WORK_ORDER_CRAFT>()
                    .Where(p => woOrdIds.Contains(p.FWORK_ORDER_ID))
                    .Select(p => new { p.FWORK_ORDER_ID, p.FWORK_ORDER_CRAFT_ID, p.FCRAFT_ID, p.FSHOW_SEQNO })
                    .ToListAsync())
                    .OrderBy(p => p.FSHOW_SEQNO).ToList();

                foreach (var item in jobDataAll)
                {
                    var woCrafts = woCraftAlls.Where(p => p.FWORK_ORDER_ID == item.FWORK_ORDER_ID).ToList();
                    var schCraft = woCrafts.FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == item.FWORK_ORDER_CRAFT_ID);

                    if (schCraft == null)
                    {
                        result.Message = _multiLang["执行失败, 排程任务 {0}, 对应工艺信息不存在于工单工艺列表."];
                        result.StatusCode = 1010;
                        return await OK(result);
                    }

                    if (woCrafts.IndexOf(schCraft) > 0)
                    {
                        var preWoCraft = woCrafts[woCrafts.IndexOf(schCraft) - 1];
                        var preFinishQty = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>("wocraftstatus")
                            .Where((wocraftstatus) => wocraftstatus.FWORK_ORDER_CRAFT_ID == preWoCraft.FWORK_ORDER_CRAFT_ID)
                            .Select((wocraftstatus) => wocraftstatus.FFINISH_QTY)
                            .FirstAsync();

                        //当前工艺已完工+本次完工 大于 上一工艺完工数量
                        if (item.FFINISH_QTY_CRAFT + item.FNG_QTY + item.FPASS_QTY > preFinishQty)
                        {
                            //取出工艺
                            string craft = string.Empty;
                            var craftResult = await GetCraftByIdsAsync(new List<string> { preWoCraft.FCRAFT_ID });
                            if (craftResult.StatusCode == 200 && craftResult.Entity.Count > 0)
                            {
                                var entity = craftResult.Entity[0];
                                craft = entity.FCRAFT_NAME;
                            }

                            //取出单位
                            string unit = string.Empty;
                            var unitsResult = await _businessService.GetUnitNameByIdsAsync(new List<string> { item.FPRO_UNIT_ID });
                            if (unitsResult.StatusCode == 200 && unitsResult.Entity.Count > 0)
                            {
                                unit = unitsResult.Entity[0].FUNIT_NAME;
                            }
                            result.Message = string.Format(_multiLang["执行失败, 当前工艺完工数量超出 上一工艺 {0} {1} {2}"], craft,
                                (item.FFINISH_QTY_CRAFT + item.FNG_QTY + item.FPASS_QTY - preFinishQty).ToString("#,##0.######"), unit);
                            result.StatusCode = 1010;
                            return await OK(result);
                        }
                    }
                    if (woCrafts.IndexOf(schCraft) > 0 && item.FEXTERNAL_QUOTE)
                    {
                        var preWoCraft = woCrafts[woCrafts.IndexOf(schCraft) + 1];
                        var preFinishQty = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>("wocraftstatus")
                            .Where((wocraftstatus) => wocraftstatus.FWORK_ORDER_CRAFT_ID == preWoCraft.FWORK_ORDER_CRAFT_ID)
                            .Select((wocraftstatus) => wocraftstatus.FFINISH_QTY)
                            .FirstAsync();

                        //当前工艺已完工+本次完工 大于 上一工艺完工数量
                        if (item.FFINISH_QTY_CRAFT + item.FNG_QTY + item.FPASS_QTY < preFinishQty)
                        {
                            //取出工艺
                            string craft = string.Empty;
                            var craftResult = await GetCraftByIdsAsync(new List<string> { preWoCraft.FCRAFT_ID });
                            if (craftResult.StatusCode == 200 && craftResult.Entity.Count > 0)
                            {
                                var entity = craftResult.Entity[0];
                                craft = entity.FCRAFT_NAME;
                            }

                            //取出单位
                            string unit = string.Empty;
                            var unitsResult = await _businessService.GetUnitNameByIdsAsync(new List<string> { item.FPRO_UNIT_ID });
                            if (unitsResult.StatusCode == 200 && unitsResult.Entity.Count > 0)
                            {
                                unit = unitsResult.Entity[0].FUNIT_NAME;
                            }
                            result.Message = string.Format(_multiLang["执行失败, 当前工艺完工数量小于 下一工艺 {0} {1} {2}"], craft,
                                (item.FFINISH_QTY_CRAFT + item.FNG_QTY + item.FPASS_QTY - preFinishQty).ToString("#,##0.######"), unit);
                            result.StatusCode = 1010;
                            return await OK(result);
                        }
                    }
                }

            }

            return await OK(result);

        }
        #endregion


        #region 自动拆分已存在的下游工序排程

        /// <summary>
        /// 自动拆分已存在的下游工序排程（用于"一次性全工序排程"模式）
        /// </summary>
        /// <param name="completedBooking">刚刚完成的报工记录</param>
        /// <param name="db">数据库上下文</param>
        /// <returns></returns>
        private async Task HandleAutoSplitExistingSchedule(T_MESD_CRAFT_JOB_BOOKING completedBooking, ISqlSugarClient db)
        {
            await HandleBatchAutoSplitExistingSchedule(new List<T_MESD_CRAFT_JOB_BOOKING> { completedBooking }, db);
        }
        #endregion


        #region 自动拆分已存在的下游工序排程, 包含返工

        /// <summary>
        /// 批量处理自动拆分已存在的下游工序排程（只传递良品数量，用于拆分完工场景）
        /// </summary>
        /// <param name="completedBookings">一组刚刚完成的报工记录</param>
        /// <param name="db">数据库上下文</param>
        private async Task HandleBatchAutoSplitExistingScheduleWithPassQty(List<T_MESD_CRAFT_JOB_BOOKING> completedBookings, ISqlSugarClient db)
        {
            if (completedBookings == null || !completedBookings.Any()) return;

            var releaseImmediately = await GetSysParamValue(_MES_AutoSplitReleaseImmediately) == "1";

            var workOrderIds = completedBookings.Select(b => b.FWORK_ORDER_ID).Distinct().ToList();
            var completedScheduleIds = completedBookings.Select(b => b.FCRAFT_SCHEDULE_ID).Distinct().ToList();

            var allCraftsForWOs = (await db.Queryable<T_MESD_WORK_ORDER_CRAFT>()
                .Where(c => workOrderIds.Contains(c.FWORK_ORDER_ID))
                .ToListAsync())
                .OrderBy(c => c.FSHOW_SEQNO)
                .ToList();

            var sourceSchedules = (await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .In(completedScheduleIds)
                .ToListAsync())
                .ToDictionary(s => s.FCRAFT_SCHEDULE_ID);

            var nextCraftIdMap = new Dictionary<string, string>();
            foreach (var woGroup in allCraftsForWOs.GroupBy(c => c.FWORK_ORDER_ID))
            {
                var craftsInOrder = woGroup.ToList();
                for (int i = 0; i < craftsInOrder.Count - 1; i++)
                {
                    nextCraftIdMap[craftsInOrder[i].FWORK_ORDER_CRAFT_ID] = craftsInOrder[i + 1].FWORK_ORDER_CRAFT_ID;
                }
            }

            // 关键修改：创建带有良品数量信息的任务列表
            var splitSchedulesWithPassQty = new List<(T_MESD_CRAFT_SCHEDULE schedule, decimal passQty)>();

            foreach (var booking in completedBookings.Where(b => b.FPASS_QTY > 0)) // 只处理有良品的记录
            {
                if (sourceSchedules.TryGetValue(booking.FCRAFT_SCHEDULE_ID, out var schedule))
                {
                    splitSchedulesWithPassQty.Add((schedule, booking.FPASS_QTY));
                }
            }

            if (splitSchedulesWithPassQty.Any())
            {
                // 调用修改后的下游任务创建方法，传递良品数量
                await BatchCreateDownstreamTasksWithPassQtyAsync(splitSchedulesWithPassQty, allCraftsForWOs.GroupBy(c => c.FWORK_ORDER_ID).ToDictionary(g => g.Key, g => g.ToList()), db, releaseImmediately);
            }
        }

        /// <summary>
        /// 批量处理自动拆分已存在的下游工序排程（用于"一次性全工序排程"模式）
        /// </summary>
        /// <param name="completedBookings">一组刚刚完成的报工记录</param>
        /// <param name="db">数据库上下文</param>
        private async Task HandleBatchAutoSplitExistingSchedule(List<T_MESD_CRAFT_JOB_BOOKING> completedBookings, ISqlSugarClient db)
        {
            if (completedBookings == null || !completedBookings.Any()) return;

            var releaseImmediately = await GetSysParamValue(_MES_AutoSplitReleaseImmediately) == "1";

            var workOrderIds = completedBookings.Select(b => b.FWORK_ORDER_ID).Distinct().ToList();
            var completedScheduleIds = completedBookings.Select(b => b.FCRAFT_SCHEDULE_ID).Distinct().ToList();

            var allCraftsForWOs = (await db.Queryable<T_MESD_WORK_ORDER_CRAFT>()
                .Where(c => workOrderIds.Contains(c.FWORK_ORDER_ID))
                .ToListAsync())
                .OrderBy(c => c.FSHOW_SEQNO)
                .ToList();

            var sourceSchedules = (await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .In(completedScheduleIds)
                .ToListAsync())
                .ToDictionary(s => s.FCRAFT_SCHEDULE_ID);

            var nextCraftIdMap = new Dictionary<string, string>();
            foreach (var woGroup in allCraftsForWOs.GroupBy(c => c.FWORK_ORDER_ID))
            {
                var craftsInOrder = woGroup.ToList();
                for (int i = 0; i < craftsInOrder.Count - 1; i++)
                {
                    nextCraftIdMap[craftsInOrder[i].FWORK_ORDER_CRAFT_ID] = craftsInOrder[i + 1].FWORK_ORDER_CRAFT_ID;
                }
            }

            var bookingsWithNextStep = completedBookings
                .Select(b => new { Booking = b, NextWoCraftId = nextCraftIdMap.GetValueOrDefault(b.FWORK_ORDER_CRAFT_ID) })
                .Where(x => x.NextWoCraftId != null)
                .ToList();

            if (!bookingsWithNextStep.Any()) return;

            var nextWoCraftIds = bookingsWithNextStep.Select(b => b.NextWoCraftId).Distinct().ToList();
            var targetParentSchedules = (await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .Where(s => nextWoCraftIds.Contains(s.FWORK_ORDER_CRAFT_ID) && s.FPARENT_CRAFT_SCHEDULE_ID == null)
                .ToListAsync())
                .ToDictionary(s => s.FWORK_ORDER_CRAFT_ID);

            var targetParentScheduleIds = targetParentSchedules.Values.Select(s => s.FCRAFT_SCHEDULE_ID).ToList();
            var targetParentStatuses = (await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS>()
                .In(targetParentScheduleIds)
                .ToListAsync())
                .ToDictionary(s => s.FCRAFT_SCHEDULE_ID);

            var newChildSchedules = new List<T_MESD_CRAFT_SCHEDULE>();
            var newChildStatuses = new List<T_MESD_CRAFT_SCHEDULE_STATUS>();
            var finalParentSchedulesToUpdate = new Dictionary<string, T_MESD_CRAFT_SCHEDULE>();
            var partialParentSchedulesToUpdate = new Dictionary<string, T_MESD_CRAFT_SCHEDULE>();
            var parentStatusesToUpdate = new Dictionary<string, T_MESD_CRAFT_SCHEDULE_STATUS>();

            var billCodes = await GetBillCodesAsync(bookingsWithNextStep.Count, true);
            int billCodeIndex = 0;

            foreach (var item in bookingsWithNextStep)
            {
                var completedBooking = item.Booking;
                if (!sourceSchedules.TryGetValue(completedBooking.FCRAFT_SCHEDULE_ID, out var sourceSchedule)) continue;
                if (!targetParentSchedules.TryGetValue(item.NextWoCraftId, out var parentSchedule)) continue;

                if (parentSchedule.FPLAN_QTY <= completedBooking.FPASS_QTY)
                {
                    // 复用父排程的逻辑 (适用于"最后一次完工"和"超产完工")
                    parentSchedule.FLOT_NO = sourceSchedule.FLOT_NO;
                    parentSchedule.FSOURCE_JOB_BOOKING_ID = completedBooking.FCRAFT_JOB_BOOKING_ID;

                    parentSchedule.FPLAN_QTY = completedBooking.FPASS_QTY;

                    finalParentSchedulesToUpdate[parentSchedule.FCRAFT_SCHEDULE_ID] = parentSchedule;

                    if (releaseImmediately)
                    {
                        if (targetParentStatuses.TryGetValue(parentSchedule.FCRAFT_SCHEDULE_ID, out var parentStatus) && !parentStatus.FRELEASE_STATUS)
                        {
                            parentStatus.FRELEASE_STATUS = true;
                            parentStatusesToUpdate[parentStatus.FCRAFT_SCHEDULE_ID] = parentStatus;
                        }
                    }
                }
                else if (parentSchedule.FPLAN_QTY > completedBooking.FPASS_QTY)
                {
                    // 部分完成：创建子排程，扣减父排程数量
                    var newChildSchedule = new T_MESD_CRAFT_SCHEDULE
                    {
                        FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid(),
                        FCRAFT_SCHEDULE_NO = billCodes[billCodeIndex],
                        FQRCODE = billCodes[billCodeIndex],
                        FPARENT_CRAFT_SCHEDULE_ID = parentSchedule.FCRAFT_SCHEDULE_ID,
                        FSCHEDULE_BATCH_ID = parentSchedule.FSCHEDULE_BATCH_ID,
                        FWORK_ORDER_ID = parentSchedule.FWORK_ORDER_ID,
                        FMATERIAL_ID = parentSchedule.FMATERIAL_ID,
                        FSALE_ORDER_ID = parentSchedule.FSALE_ORDER_ID,
                        FWORK_ORDER_CRAFT_ID = parentSchedule.FWORK_ORDER_CRAFT_ID,
                        FCRAFT_ID = parentSchedule.FCRAFT_ID,
                        FSTATION_ID = parentSchedule.FSTATION_ID,
                        FPLAN_QTY = completedBooking.FPASS_QTY,
                        FORI_PLAN_QTY = completedBooking.FPASS_QTY,
                        FLOT_NO = sourceSchedule.FLOT_NO,
                        FPLAN_ST_DATE = DateTime.Now,
                        FPLAN_ED_DATE = DateTime.Now.AddHours(1),
                        FSOURCE_JOB_BOOKING_ID = completedBooking.FCRAFT_JOB_BOOKING_ID,
                    };
                    newChildSchedules.Add(newChildSchedule);
                    billCodeIndex++;

                    newChildStatuses.Add(new T_MESD_CRAFT_SCHEDULE_STATUS
                    {
                        FCRAFT_SCHEDULE_STATUS_ID = GuidHelper.NewGuid(),
                        FCRAFT_SCHEDULE_ID = newChildSchedule.FCRAFT_SCHEDULE_ID,
                        FRELEASE_STATUS = releaseImmediately,
                    });

                    parentSchedule.FPLAN_QTY -= completedBooking.FPASS_QTY;
                    partialParentSchedulesToUpdate[parentSchedule.FCRAFT_SCHEDULE_ID] = parentSchedule;
                }
            }

            // 执行批量数据库操作
            if (newChildSchedules.Any())
            {
                await db.Insertable(newChildSchedules).ExecuteCommandAsync();
                await db.Insertable(newChildStatuses).ExecuteCommandAsync();
            }

            if (partialParentSchedulesToUpdate.Any())
            {
                await db.Updateable(partialParentSchedulesToUpdate.Values.ToList())
                        .UpdateColumns(it => new { it.FPLAN_QTY, it.FREMARK }) // 同时更新备注
                        .ExecuteCommandAsync();
            }

            if (finalParentSchedulesToUpdate.Any())
            {
                await db.Updateable(finalParentSchedulesToUpdate.Values.ToList())
                        .UpdateColumns(it => new { it.FLOT_NO, it.FSOURCE_JOB_BOOKING_ID, it.FPLAN_QTY })
                        .ExecuteCommandAsync();
            }

            if (parentStatusesToUpdate.Any())
            {
                await db.Updateable(parentStatusesToUpdate.Values.ToList())
                        .UpdateColumns(it => new { it.FRELEASE_STATUS })
                        .ExecuteCommandAsync();
            }

            var allAffectedDownstreamWoCraftIds = nextWoCraftIds
                .Union(finalParentSchedulesToUpdate.Values.Select(s => s.FWORK_ORDER_CRAFT_ID))
                .Distinct().ToList();

            if (allAffectedDownstreamWoCraftIds.Any())
            {
                await UpdateWorkOrderCraftReleaseQtyAsync(allAffectedDownstreamWoCraftIds, db);
            }
        }

        /// <summary>
        /// 批量为不良品创建返工排程任务
        /// </summary>
        /// <param name="completedBookingsWithNG">包含不良品的已完成报工记录</param>
        /// <param name="db">数据库上下文</param>
        private async Task HandleReworkScheduleCreation(List<T_MESD_CRAFT_JOB_BOOKING> completedBookingsWithNG, ISqlSugarClient db)
        {
            // 是否自动下发
            var releaseReworkImmediately = await GetSysParamValue(_MES_AutoSplitReleaseImmediately) == "1";

            // 1. 一次性获取所有相关的原始排程信息，用于继承
            var sourceScheduleIds = completedBookingsWithNG.Select(b => b.FCRAFT_SCHEDULE_ID).Distinct().ToList();
            var sourceSchedules = (await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .Where(s => sourceScheduleIds.Contains(s.FCRAFT_SCHEDULE_ID))
                .ToListAsync())
                .ToDictionary(s => s.FCRAFT_SCHEDULE_ID);

            // 2. 准备用于批量数据库操作的列表
            var newReworkSchedules = new List<T_MESD_CRAFT_SCHEDULE>();
            var newReworkStatuses = new List<T_MESD_CRAFT_SCHEDULE_STATUS>();

            // 3. 批量生成所有返工任务需要的单号
            var billCodes = await GetBillCodesAsync(completedBookingsWithNG.Count, true);
            int billCodeIndex = 0;

            // 4. 遍历每一条产生不良品的报工记录，创建对应的返工排程
            foreach (var booking in completedBookingsWithNG)
            {
                if (!sourceSchedules.TryGetValue(booking.FCRAFT_SCHEDULE_ID, out var sourceSchedule))
                {
                    // 如果源排程找不到，跳过此条记录，或记录日志
                    continue;
                }

                // 创建新的返工批号，例如 OP01 -> OP01-R1
                string reworkLotNo = $"{sourceSchedule.FLOT_NO}-R{DateTime.Now.ToString("HHmmss")}";

                // 创建新的返工排程（Rework Schedule）
                var reworkSchedule = new T_MESD_CRAFT_SCHEDULE
                {
                    // 基础信息
                    FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid(),
                    FCRAFT_SCHEDULE_NO = $"{billCodes[billCodeIndex]}-R", // 单号加-R以区分
                    FQRCODE = $"{billCodes[billCodeIndex]}-R",
                    SCHEDULE_FSHOW_SEQNO = 999,

                    // 核心：关联父排程，用于追溯
                    FPARENT_CRAFT_SCHEDULE_ID = sourceSchedule.FCRAFT_SCHEDULE_ID,

                    // 核心：返工任务的工艺、工位等信息与源任务保持一致
                    // 因为返工是在当前工序重新执行
                    FWORK_ORDER_CRAFT_ID = booking.FWORK_ORDER_CRAFT_ID,
                    FCRAFT_ID = booking.FCRAFT_ID,
                    FSTATION_ID = booking.FSTATION_ID, // 默认返回原工位，也可配置为专门的返工工位

                    // 继承工单、物料等信息
                    FSCHEDULE_BATCH_ID = sourceSchedule.FSCHEDULE_BATCH_ID,
                    FWORK_ORDER_ID = booking.FWORK_ORDER_ID,
                    FMATERIAL_ID = booking.FMATERIAL_ID,
                    FSALE_ORDER_ID = booking.FSALE_ORDER_ID,

                    // 核心：返工排程的计划数量 = 本次报工产生的不良品数量
                    FPLAN_QTY = booking.FNG_QTY,
                    FORI_PLAN_QTY = booking.FNG_QTY,

                    // 设置返工批号
                    FLOT_NO = reworkLotNo,

                    // 其他信息
                    FPLAN_ST_DATE = DateTime.Now,
                    FPLAN_ED_DATE = DateTime.Now.AddHours(2), // 预估一个返工时间
                    FSOURCE_JOB_BOOKING_ID = booking.FCRAFT_JOB_BOOKING_ID, // 追溯来源：由哪次报工创建
                                                                            // 可以在备注或新增字段中明确标识为返工
                    FREMARK = $"由报工[{booking.FCRAFT_JOB_BOOKING_NO}]创建的返工任务",
                };
                newReworkSchedules.Add(reworkSchedule);

                // 为新的返工排程创建状态记录
                newReworkStatuses.Add(new T_MESD_CRAFT_SCHEDULE_STATUS
                {
                    FCRAFT_SCHEDULE_STATUS_ID = GuidHelper.NewGuid(),
                    FCRAFT_SCHEDULE_ID = reworkSchedule.FCRAFT_SCHEDULE_ID,
                    FRELEASE_STATUS = releaseReworkImmediately, // 根据参数决定是否立即下发
                });

                billCodeIndex++;
            }

            // 5. 如果有新生成的返工排程，则执行批量数据库插入操作
            if (newReworkSchedules.Any())
            {
                await db.Insertable(newReworkSchedules).ExecuteCommandAsync();
                await db.Insertable(newReworkStatuses).ExecuteCommandAsync();

                // 6. 关键修复：清空原始报工记录中的不良品数量，避免重复计算
                foreach (var booking in completedBookingsWithNG)
                {
                    // 将不良品数量和重量清空，因为已经转移到返工排程中
                    booking.FNG_QTY = 0;
                    booking.FNG_WEIGHT = 0;
                }

                // 批量更新原始报工记录，清空不良品数据
                await db.Updateable(completedBookingsWithNG)
                    .UpdateColumns(b => new { b.FNG_QTY, b.FNG_WEIGHT })
                    .ExecuteCommandAsync();

                // 如果返工任务自动下发了，需要更新工单工艺的已下发数
                if (releaseReworkImmediately)
                {
                    var woCraftIds = newReworkSchedules.Select(s => s.FWORK_ORDER_CRAFT_ID).Distinct().ToList();
                    await UpdateWorkOrderCraftReleaseQtyAsync(woCraftIds, db);
                }
            }
        }


        /// <summary>
        /// 下游工序退回不良品，并创建返工任务
        /// </summary>
        /// <param name="model">退回操作的数据模型</param>
        /// <returns>返回操作结果，成功则Entity为新的返工排程ID</returns>
        public async Task<DataResult<string>> RejectAndReturnAsync(RejectAndReturnModel model)
        {
            // 1. ========= 输入验证 =========
            if (model == null || string.IsNullOrWhiteSpace(model.FCRAFT_JOB_BOOKING_ID) || model.FRET_QTY <= 0)
            {
                ERROR(null, 110001, _multiLang["执行失败, 传入的退回参数无效."]);
            }
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;
            var releaseReworkImmediately = await GetSysParamValue(_MES_AutoSplitReleaseImmediately) == "1";



            // 2. ========= 获取原始数据并校验 =========
            // 查找被退回的、源头的上游报工记录
            var sourceJobBooking = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
             .Where(it => it.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
             .FirstAsync();

            if (sourceJobBooking == null)
            {
                ERROR(null, 110002, _multiLang["执行失败, 找不到来源报工记录."]);
            }

            if (sourceJobBooking.FPASS_QTY < model.FRET_QTY)
            {
                ERROR(null, 110003, string.Format(_multiLang["退回数量 {0} 不能大于来源报工的良品数 {1}."], model.FRET_QTY, sourceJobBooking.FPASS_QTY));
            }

            // 获取源排程信息，用于创建新的返工排程
            var sourceSchedule = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().InSingleAsync(sourceJobBooking.FCRAFT_SCHEDULE_ID);
            if (sourceSchedule == null)
            {
                ERROR(null, 110004, _multiLang["执行失败, 找不到来源排程信息."]);
            }

            // 3. ========= 核心逻辑：在一个事务中完成所有操作 =========
            try
            {
                db.BeginTran();

                // 3.1. 追溯更新上游的原始报工记录
                sourceJobBooking.FPASS_QTY -= model.FRET_QTY; // 良品数减少
                sourceJobBooking.FNG_QTY += model.FRET_QTY;   // 不良品数增加
                                                              // 可以在备注中记录是谁在下游退回的
                sourceJobBooking.F_CUSTOM_FIELD1 = $"由工位[{model.FSTATION_ID}]在{DateTime.Now:yyyy-MM-dd HH:mm}退回{model.FRET_QTY}件不良品。";

                await db.Updateable(sourceJobBooking)
                    .UpdateColumns(it => new { it.FPASS_QTY, it.FNG_QTY, it.F_CUSTOM_FIELD1 })
                    .ExecuteCommandAsync();

                // 3.2. 因为原始报工数量变了，需要重新计算排程和工单工艺的汇总数
                await UpdateSchedulesFinishAsync(new List<string> { sourceJobBooking.FCRAFT_SCHEDULE_ID }, db, user, DateTime.Now);
                await UpdateWorkOrderCraftsFinishAsync(new List<string> { sourceJobBooking.FWORK_ORDER_CRAFT_ID }, db);

                var billCodes = await GetBillCodesAsync(1, true);
                string reworkLotNo = $"{sourceSchedule.FLOT_NO}-R-Ret"; // 返工批号

                var reworkSchedule = new T_MESD_CRAFT_SCHEDULE
                {
                    FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid(),
                    FCRAFT_SCHEDULE_NO = $"{billCodes[0]}-R",
                    FQRCODE = $"{billCodes[0]}-R",
                    FPARENT_CRAFT_SCHEDULE_ID = sourceSchedule.FCRAFT_SCHEDULE_ID,
                    FWORK_ORDER_CRAFT_ID = sourceJobBooking.FWORK_ORDER_CRAFT_ID, // 返工的工艺与源头一致
                    FCRAFT_ID = sourceJobBooking.FCRAFT_ID,
                    FSTATION_ID = sourceJobBooking.FSTATION_ID, // 任务回到源头工位
                    FPLAN_QTY = model.FRET_QTY, // 计划数 = 退回数量
                    FORI_PLAN_QTY = model.FRET_QTY,
                    FLOT_NO = reworkLotNo,
                    FSOURCE_JOB_BOOKING_ID = sourceJobBooking.FCRAFT_JOB_BOOKING_ID,
                    FREMARK = $"由工位[{model.FSTATION_ID}]退回的不良品返工任务。",
                    // 记录真正的来源, 目前没有表结构支持
                    // FSOURCE_STATION_ID = model.FSTATION_ID, 
                };
                await db.Insertable(reworkSchedule).ExecuteCommandAsync();

                var reworkStatus = new T_MESD_CRAFT_SCHEDULE_STATUS
                {
                    FCRAFT_SCHEDULE_STATUS_ID = GuidHelper.NewGuid(),
                    FCRAFT_SCHEDULE_ID = reworkSchedule.FCRAFT_SCHEDULE_ID,
                    FRELEASE_STATUS = releaseReworkImmediately,
                };
                await db.Insertable(reworkStatus).ExecuteCommandAsync();

                if (releaseReworkImmediately)
                {
                    // 如果返工任务自动下发了，需要更新工单工艺的已下发数
                    await UpdateWorkOrderCraftReleaseQtyAsync(new List<string> { reworkSchedule.FWORK_ORDER_CRAFT_ID }, db);
                }

                db.CommitTran();

                return await OK(new DataResult<string> { Entity = reworkSchedule.FCRAFT_SCHEDULE_ID, StatusCode = 200 });
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }
        }


        /// <summary>
        /// （辅助方法）更新工单工艺的已下发数量
        /// </summary>
        private async Task UpdateWorkOrderCraftReleaseQtyAsync(List<string> woCraftIds, ISqlSugarClient db)
        {
            if (woCraftIds == null || !woCraftIds.Any()) return;

            var releasedSchedules = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                ((sch, schStatus) => new JoinQueryInfos(
                    JoinType.Inner, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID
                ))
                .Where((sch, schStatus) => woCraftIds.Contains(sch.FWORK_ORDER_CRAFT_ID) && schStatus.FRELEASE_STATUS == true)
                .Select(sch => new
                {
                    sch.FWORK_ORDER_CRAFT_ID,
                    sch.FPLAN_QTY
                })
                .ToListAsync();

            var craftReleaseQtyDict = releasedSchedules
                .GroupBy(item => item.FWORK_ORDER_CRAFT_ID)
                .ToDictionary(
                    group => group.Key,
                    group => group.Sum(item => item.FPLAN_QTY)
                );

            var statusesToUpdate = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>()
                .Where(status => woCraftIds.Contains(status.FWORK_ORDER_CRAFT_ID))
                .ToListAsync();

            foreach (var status in statusesToUpdate)
            {
                status.FRELEASE_QTY = craftReleaseQtyDict.GetValueOrDefault(status.FWORK_ORDER_CRAFT_ID, 0);
            }

            if (statusesToUpdate.Any())
            {
                await db.Updateable(statusesToUpdate)
                    .UpdateColumns(it => it.FRELEASE_QTY)
                    .ExecuteCommandAsync();
            }
        }

        /// <summary>
        /// (辅助) 准备要插入的拆分完工排程实体，不执行数据库操作
        /// </summary>
        private async Task<T_MESD_CRAFT_SCHEDULE> PrepareSplitFinishedScheduleAsync(T_MESD_CRAFT_SCHEDULE originalSchedule, decimal splitQty, ISqlSugarClient db, string billCode)
        {
            var splitLotNo = await GenerateSplitLotNoAsync(originalSchedule.FWORK_ORDER_ID, originalSchedule.FWORK_ORDER_CRAFT_ID, db);
            return new T_MESD_CRAFT_SCHEDULE
            {
                FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid(),
                FCRAFT_SCHEDULE_NO = billCode,
                FQRCODE = billCode,
                SCHEDULE_FSHOW_SEQNO = 999,
                FPARENT_CRAFT_SCHEDULE_ID = originalSchedule.FCRAFT_SCHEDULE_ID,
                FSCHEDULE_BATCH_ID = originalSchedule.FSCHEDULE_BATCH_ID,
                FWORK_ORDER_ID = originalSchedule.FWORK_ORDER_ID,
                FMATERIAL_ID = originalSchedule.FMATERIAL_ID,
                FSALE_ORDER_ID = originalSchedule.FSALE_ORDER_ID,
                FWORK_ORDER_CRAFT_ID = originalSchedule.FWORK_ORDER_CRAFT_ID,
                FCRAFT_ID = originalSchedule.FCRAFT_ID,
                FSTATION_ID = originalSchedule.FSTATION_ID,
                FPLAN_QTY = splitQty,
                FORI_PLAN_QTY = splitQty,
                FLOT_NO = splitLotNo,
                FPLAN_ST_DATE = originalSchedule.FPLAN_ST_DATE,
                FPLAN_ED_DATE = _iauth.GetCurDateTime(),
                FREMARK = $"从排程 {originalSchedule.FCRAFT_SCHEDULE_NO} 拆分的已完工任务"
            };
        }

        /// <summary>
        /// (辅助) 准备要插入的拆分完工报工实体
        /// </summary>
        private async Task<T_MESD_CRAFT_JOB_BOOKING> PrepareFinishedJobBookingForSplitAsync(T_MESD_CRAFT_SCHEDULE splitSchedule, decimal passQty, decimal ngQty, decimal workHours, UserAccountModel user, string billCode)
        {
            var curDate = _iauth.GetCurDateTime();
            //var workOrder = await _isugar.DB.Queryable<T_MESD_WORK_ORDER>().InSingleAsync(splitSchedule.FWORK_ORDER_ID);
            //var unitWeight = workOrder?.FUNIT_WEIGHT ?? 0;

            return new T_MESD_CRAFT_JOB_BOOKING
            {
                FCRAFT_JOB_BOOKING_ID = GuidHelper.NewGuid(),
                FCRAFT_JOB_BOOKING_NO = billCode,
                FCRAFT_SCHEDULE_ID = splitSchedule.FCRAFT_SCHEDULE_ID,
                FACT_ST_DATE = curDate,
                FACT_ED_DATE = curDate,
                FACT_USE_HOUR = workHours,
                FPASS_QTY = passQty,             // 良品数
                FNG_QTY = ngQty,                 // 不良品数
                //FPASS_WEIGHT = passQty * unitWeight,
                //FNG_WEIGHT = ngQty * unitWeight,
                FWORK_STATUS = "finished",
                FEMP_ID = user.UserPsnId,
                FEMP_NAME = user.UserPsnName,
                FWORK_ORDER_ID = splitSchedule.FWORK_ORDER_ID,
                FWORK_ORDER_CRAFT_ID = splitSchedule.FWORK_ORDER_CRAFT_ID,
                FCRAFT_ID = splitSchedule.FCRAFT_ID,
                FMATERIAL_ID = splitSchedule.FMATERIAL_ID,
                FSTATION_ID = splitSchedule.FSTATION_ID,
                FSALE_ORDER_ID = splitSchedule.FSALE_ORDER_ID
            };
        }

        /// <summary>
        /// 批量创建下游任务（使用良品数量）
        /// </summary>
        private async Task BatchCreateDownstreamTasksWithPassQtyAsync(List<(T_MESD_CRAFT_SCHEDULE schedule, decimal passQty)> splitSchedulesWithPassQty, Dictionary<string, List<T_MESD_WORK_ORDER_CRAFT>> allCraftsForWOs, ISqlSugarClient db, bool releaseImmediately)
        {
            if (splitSchedulesWithPassQty == null || !splitSchedulesWithPassQty.Any()) return;

            var workOrderIds = splitSchedulesWithPassQty.Select(s => s.schedule.FWORK_ORDER_ID).Distinct().ToList();

            // 构建下游工艺的查找字典
            var nextCraftIdMap = new Dictionary<string, string>();
            foreach (var woId in workOrderIds)
            {
                if (allCraftsForWOs.TryGetValue(woId, out var craftsInOrder))
                {
                    for (int i = 0; i < craftsInOrder.Count - 1; i++)
                    {
                        nextCraftIdMap[craftsInOrder[i].FWORK_ORDER_CRAFT_ID] = craftsInOrder[i + 1].FWORK_ORDER_CRAFT_ID;
                    }
                }
            }

            var nextWoCraftIds = splitSchedulesWithPassQty
                .Select(s => nextCraftIdMap.GetValueOrDefault(s.schedule.FWORK_ORDER_CRAFT_ID))
                .Where(id => id != null).Distinct().ToList();

            if (!nextWoCraftIds.Any()) return;

            // 批量查找所有潜在的父排程
            var targetParentSchedules = (await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .Where(s => nextWoCraftIds.Contains(s.FWORK_ORDER_CRAFT_ID) && s.FPARENT_CRAFT_SCHEDULE_ID == null)
                .ToListAsync()).ToDictionary(s => s.FWORK_ORDER_CRAFT_ID);

            var newChildSchedules = new List<T_MESD_CRAFT_SCHEDULE>();
            var newChildStatuses = new List<T_MESD_CRAFT_SCHEDULE_STATUS>();
            var parentsToUpdate = new Dictionary<string, T_MESD_CRAFT_SCHEDULE>();

            var billCodes = await GetBillCodesAsync(splitSchedulesWithPassQty.Count, true);
            int billCodeIndex = 0;

            foreach (var (splitSchedule, passQty) in splitSchedulesWithPassQty)
            {
                if (!nextCraftIdMap.TryGetValue(splitSchedule.FWORK_ORDER_CRAFT_ID, out var nextWoCraftId) ||
                    !targetParentSchedules.TryGetValue(nextWoCraftId, out var parentSchedule))
                {
                    continue;
                }

                // 关键修改：使用良品数量而不是总数量
                var qty = passQty;

                // 准备要插入的子排程
                var childSchedule = await PrepareDownstreamChildScheduleAsync(parentSchedule, splitSchedule, qty, billCodes[billCodeIndex++]);
                newChildSchedules.Add(childSchedule);
                newChildStatuses.Add(new T_MESD_CRAFT_SCHEDULE_STATUS
                {
                    FCRAFT_SCHEDULE_STATUS_ID = GuidHelper.NewGuid(),
                    FCRAFT_SCHEDULE_ID = childSchedule.FCRAFT_SCHEDULE_ID,
                    FRELEASE_STATUS = releaseImmediately,
                });

                // 准备要更新的父排程
                if (!parentsToUpdate.ContainsKey(parentSchedule.FCRAFT_SCHEDULE_ID))
                {
                    parentsToUpdate[parentSchedule.FCRAFT_SCHEDULE_ID] = parentSchedule;
                }
                parentsToUpdate[parentSchedule.FCRAFT_SCHEDULE_ID].FPLAN_QTY -= qty;
            }

            // 批量数据库操作
            if (newChildSchedules.Any()) await db.Insertable(newChildSchedules).ExecuteCommandAsync();
            if (newChildStatuses.Any()) await db.Insertable(newChildStatuses).ExecuteCommandAsync();
            if (parentsToUpdate.Any()) await db.Updateable(parentsToUpdate.Values.ToList()).UpdateColumns(it => it.FPLAN_QTY).ExecuteCommandAsync();
        }


        /// <summary>
        /// 准备下游子排程实体
        /// </summary>
        private async Task<T_MESD_CRAFT_SCHEDULE> PrepareDownstreamChildScheduleAsync(T_MESD_CRAFT_SCHEDULE parentSchedule, T_MESD_CRAFT_SCHEDULE sourceSchedule, decimal qty, string billCode)
        {

            // 【修改】查找源排程对应的最新完工报工记录，用于建立正确的关联
            var sourceJobBooking = await _isugar.DB.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(j => j.FCRAFT_SCHEDULE_ID == sourceSchedule.FCRAFT_SCHEDULE_ID && j.FWORK_STATUS == "finished")
                .OrderBy(j => j.FACT_ED_DATE, OrderByType.Desc)
                .FirstAsync();

            // 使用报工ID作为源，如果找不到则使用排程ID
            var sourceId = sourceJobBooking?.FCRAFT_JOB_BOOKING_ID ?? sourceSchedule.FCRAFT_SCHEDULE_ID;

            return new T_MESD_CRAFT_SCHEDULE
            {
                FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid(),
                FCRAFT_SCHEDULE_NO = billCode,
                FQRCODE = billCode,
                FPARENT_CRAFT_SCHEDULE_ID = parentSchedule.FCRAFT_SCHEDULE_ID,
                FLOT_NO = sourceSchedule.FLOT_NO, // 继承拆分批次的批号
                FPLAN_QTY = qty,
                FORI_PLAN_QTY = qty,
                FSOURCE_JOB_BOOKING_ID = sourceId, // 【修改】使用报工ID而不是排程ID
                FREMARK = $"由拆分排程 {sourceSchedule.FCRAFT_SCHEDULE_NO} 生成的下游任务",
                FSCHEDULE_BATCH_ID = parentSchedule.FSCHEDULE_BATCH_ID,
                FWORK_ORDER_ID = parentSchedule.FWORK_ORDER_ID,
                FMATERIAL_ID = parentSchedule.FMATERIAL_ID,
                FSALE_ORDER_ID = parentSchedule.FSALE_ORDER_ID,
                FWORK_ORDER_CRAFT_ID = parentSchedule.FWORK_ORDER_CRAFT_ID,
                FCRAFT_ID = parentSchedule.FCRAFT_ID,
                FSTATION_ID = parentSchedule.FSTATION_ID,
                FPLAN_ST_DATE = _iauth.GetCurDateTime(),
                FPLAN_ED_DATE = _iauth.GetCurDateTime().AddHours(1)
            };
        }



        #endregion



    }
}
