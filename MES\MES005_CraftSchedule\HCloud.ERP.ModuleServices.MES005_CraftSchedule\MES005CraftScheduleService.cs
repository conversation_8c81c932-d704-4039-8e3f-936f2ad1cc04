﻿using HCloud.Core.Common;
using HCloud.Core.HCPlatform.Auth.Internal;
using HCloud.Core.HCPlatform.Ioc;
using HCloud.Core.HCPlatform.Multilingual;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.ProxyGenerator.Models;
using HCloud.Core.Sugar;
using HCloud.ERP.IModuleServices.MES002_Craft;
using HCloud.ERP.IModuleServices.MES003_WorkOrder.Models;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models;
using HCloud.ERP.IModuleServices.MSD002_Material;
using HCloud.ERP.IModuleServices.MSD002_Material.Models;
using HCloud.ERP.IModuleServices.MES002_Craft.Models;
using HCloud.ERP.IModuleServices.ADM024_Employee;
using HCloud.ERP.IModuleServices.ADM024_Employee.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HCloud.ERP.IModuleServices.MSD001_Unit.Models;
using HCloud.ERP.IModuleServices.MSD001_Unit;
using HCloud.ERP.IModuleServices.PUR013_OutApply;
using HCloud.ERP.IModuleServices.PUR013_OutApply.Models;
using HCloud.Core.HCPlatform.CommonData;
using HCloud.Core.HCPlatform.Serialization;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models.Request;
using HCloud.ERP.IModuleServices.COP001_SaleOrder;
using HCloud.ERP.IModuleServices.COP001_SaleOrder.Models;
using HCloud.ERP.IModuleServices.MES003_WorkOrder;
using HCloud.ERP.IModuleServices.MES007_JobBooking;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models;
using HCloud.ERP.IModuleServices.MES010_WorkOrderChange.Models;
using HCloud.ERP.IModuleServices.MES001WorkArea;
using HCloud.Core.HCPlatform.ThirdParty;
using System.Net.Http;
using HCloud.ERP.IModuleServices.MES016_WoRec.Models;

namespace HCloud.ERP.ModuleServices.MES005_CraftSchedule
{
    [ModuleName("MES005CraftSchedule")]
    public partial class MES005CraftScheduleService : ProxyServiceBase, IMES005CraftScheduleService
    {
        private readonly ILogger _Logger = null;
        private readonly IAuth _iauth = null;
        private readonly ISugar<MES005CraftScheduleService> _isugar = null;
        private readonly IMultilingualResource _multiLang = null;
        private readonly ICommonDataProvider _commonDataProvider = null;
        private readonly ISerializer<string> _serializer = null;


        private readonly IHttpClientFactory _httpClient = null;
        //注入获取参数
        private readonly IThirdPartySetting _ThirdPartySetting = null;


        public MES005CraftScheduleService(ILogger<MES005CraftScheduleService> logger,
             ISugar<MES005CraftScheduleService> sugar, IAuth auth, IMultilingualResource multiLang,
             ICommonDataProvider commonDataProvider, ISerializer<string> serializer, IThirdPartySetting ThirdPartySetting, IHttpClientFactory httpClient
             )
        {
            _Logger = logger;
            _iauth = auth;
            _isugar = sugar;
            _multiLang = multiLang;
            _commonDataProvider = commonDataProvider;
            _serializer = serializer;

            _ThirdPartySetting = ThirdPartySetting;
            _httpClient = httpClient;
        }


        /// <summary>
        /// 查询排程任务 获取生产工单工时
        /// </summary>

        public async Task<DataResult<List<OrderSchedule>>> QueryOrderScheduleAsync(List<string> Ids)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            var sche = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().Where(p => Ids.Contains(p.FWORK_ORDER_ID)).ToListAsync();

            var schesum = sche.GroupBy(p => p.FWORK_ORDER_ID).Select(p => new OrderSchedule()
            {
                FWORK_ORDER_ID = p.Key,
                FPLAN_USE_HOUR = p.Sum(a => a.FPLAN_USE_HOUR),

            }).ToList();

            //result         
            DataResult<List<OrderSchedule>> result = new DataResult<List<OrderSchedule>>
            {
                Entity = schesum,
                StatusCode = 200,
            };
            return await OK(result);

        }

        /// <summary>
        /// 简易排程任务
        /// </summary>

        public async Task<DataResult<List<SimpleScheduleModel>>> SimpleScheduleAsync(List<string> ids)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            var sche = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                       ((a, b) => new JoinQueryInfos(JoinType.Left, a.FCRAFT_SCHEDULE_ID == b.FCRAFT_SCHEDULE_ID))
                .Where((a, b) => ids.Contains(a.FCRAFT_SCHEDULE_ID)).Select<SimpleScheduleModel>().Distinct().ToListAsync();

            DataResult<List<SimpleScheduleModel>> result = new DataResult<List<SimpleScheduleModel>>
            {
                Entity = sche,
                StatusCode = 200,
            };
            return await OK(result);

        }

        /// <summary>
        /// 简易排程任务-包含工位
        /// </summary>
        public async Task<DataResult<List<SimpleCrfatScheduleModel>>> SimpleCrfatScheduleAsync(List<string> ids)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            var sche = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                       ((a, b) => new JoinQueryInfos(JoinType.Left, a.FCRAFT_SCHEDULE_ID == b.FCRAFT_SCHEDULE_ID))
                .Where((a, b) => ids.Contains(a.FCRAFT_SCHEDULE_ID)).Select<SimpleCrfatScheduleModel>().ToListAsync();

            DataResult<List<SimpleCrfatScheduleModel>> result = new DataResult<List<SimpleCrfatScheduleModel>>
            {
                Entity = sche,
                StatusCode = 200,
            };
            return await OK(result);

        }


        /// <summary>
        /// 根据生产工单查询是否存在排程任务
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WOCraftScheduleModel>>> WorkOrdIsScheduleAsync(List<string> ids)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            var sche = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().Where(p => ids.Contains(p.FWORK_ORDER_ID)).Select<WOCraftScheduleModel>().ToListAsync();

            DataResult<List<WOCraftScheduleModel>> result = new DataResult<List<WOCraftScheduleModel>>
            {
                Entity = sche,
                StatusCode = 200,
            };
            return await OK(result);
        }

        /// <summary>
        /// 根据工位查询工单ids
        /// </summary>
        public async Task<DataResult<List<string>>> WorkOrdsByStationAsync(List<string> ids)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            var sche = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().Where(p => ids.Contains(p.FSTATION_ID)).Select(p => p.FWORK_ORDER_ID).Distinct().ToListAsync();

            DataResult<List<string>> result = new DataResult<List<string>>
            {
                Entity = sche,
                StatusCode = 200,
            };
            return await OK(result);
        }


        #region 查询待排程
        /// <summary>
        /// 查询待排程工单数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<ScheduleModel>> QueryWaitScheduleAsync(QueryRequestModel model)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            #region 查出工艺id列表
            string craft = string.Empty;
            List<string> craftIds = new List<string>();
            var craftItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FCRAFT");
            if (craftItem != null)
            {
                model.WhereGroup.Items.Remove(craftItem);
                if (!string.IsNullOrWhiteSpace(craftItem.Value))
                {
                    craft = craftItem.Value;
                    var craftResult = await RpcGetCraftIdsAsync(craft);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }
                    craftIds = craftResult.Entity;
                }
            }
            #endregion

            #region 查出产品id列表
            string material = string.Empty;
            List<string> materialIds = new List<string>();
            var materialItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FMATERIAL");
            if (materialItem != null)
            {
                model.WhereGroup.Items.Remove(materialItem);
                if (!string.IsNullOrWhiteSpace(materialItem.Value))
                {
                    material = materialItem.Value;
                    var materialResult = await RpcGetMaterialIdsAsync(material);
                    if (materialResult.StatusCode != 200)
                    {
                        ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                    }
                    materialIds = materialResult.Entity;
                }
            }
            #endregion

            #region 查出订单id列表
            string saleOrderNo = string.Empty;
            List<string> saleOrderIds = new List<string>();
            var saleOrderItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FSALE_ORDER_NO");
            if (saleOrderItem != null)
            {
                model.WhereGroup.Items.Remove(saleOrderItem);
                if (!string.IsNullOrWhiteSpace(saleOrderItem.Value))
                {
                    saleOrderNo = saleOrderItem.Value;
                    var soResult = await RpcGetSaleOrderIdsAsync(saleOrderNo);
                    if (soResult.StatusCode != 200)
                    {
                        ERROR(soResult, soResult.StatusCode, soResult.Message);
                    }
                    saleOrderIds = soResult.Entity;
                }
            }
            #endregion

            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);

            #region 查询主表数据
            // 排除变更中(已建变更单未审核）的生产工单
            var lstExcludeOrderId = await db.Queryable<T_MESD_WORK_ORDER_CHANGE, T_MESD_WORK_ORDER_CHANGE_STATUS>(
                (a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_CHANGE_ID == b.FWORK_ORDER_CHANGE_ID))
                .Where((a, b) => !b.FIF_CANCEL && (b.FCFLAG == 0 || b.FCFLAG == 2))
                .Select((a, b) => a.FWORK_ORDER_ID).Distinct().ToListAsync();
            RefAsync<int> totalSize = new RefAsync<int>();
            List<WOScheduleModel> woModels = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>(
                                    (wo, woStatus) => new JoinQueryInfos(JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID))
                .Where((wo, woStatus) => !wo.FIF_OUT && woStatus.FIF_CANCEL == false && woStatus.FCFLAG == 1 && (woStatus.FIF_CLOSE == 0 || woStatus.FIF_CLOSE == 2))
                .Where((wo, woStatus) => SqlFunc.Subqueryable<T_MESD_WORK_ORDER_CRAFT_STATUS>().Where(
                                                        craftStatus => wo.FWORK_ORDER_ID == craftStatus.FWORK_ORDER_ID && wo.FPRO_QTY > craftStatus.FPLAN_QTY).Any())
                .WhereIF(!string.IsNullOrWhiteSpace(craft), (wo, woStatus) => SqlFunc.Subqueryable<T_MESD_WORK_ORDER_CRAFT>().Where(
                                                        craft => wo.FWORK_ORDER_ID == craft.FWORK_ORDER_ID && craftIds.Contains(craft.FCRAFT_ID)).Any())
                .WhereIF(!string.IsNullOrWhiteSpace(material), (wo) => materialIds.Contains(wo.FMATERIAL_ID))
                .WhereIF(!string.IsNullOrWhiteSpace(saleOrderNo), (wo) => saleOrderIds.Contains(wo.FSALE_ORDER_ID))
                .WhereIF(lstExcludeOrderId != null && lstExcludeOrderId.Count > 0, (wo) => !lstExcludeOrderId.Contains(wo.FWORK_ORDER_ID)) // 排除建变更单的生产工单
                .Select((wo, woStatus) => new WOScheduleModel()
                {


                    FCFLAG = woStatus.FCFLAG,
                    FECODE = wo.FECODE,

                    FFINISH_QTY = woStatus.FFINISH_QTY,
                    FIF_CANCEL = woStatus.FIF_CANCEL,

                    FIF_CLOSE = woStatus.FIF_CLOSE,
                    FLEVEL = wo.FLEVEL,

                    FLOT_NO = wo.FLOT_NO,
                    FMATERIAL_ID = wo.FMATERIAL_ID,

                    FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                    FPLAN_ED_DATE = wo.FPLAN_ED_DATE,

                    FPLAN_EMP_ID = wo.FPLAN_EMP_ID,
                    FPLAN_ST_DATE = wo.FPLAN_ST_DATE,

                    FPRO_QTY = wo.FPRO_QTY,
                    FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                    FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                    FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                    FWORK_ORDER_ID = wo.FWORK_ORDER_ID,
                    FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                    FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                    //重量
                    FPLAN_WEIGHT = wo.FPRO_WEIGHT,
                    FWEIGHT_UNIT_ID = wo.FWEIGHT_UNIT_ID,
                    FUNIT_WEIGHT = wo.FUNIT_WEIGHT,

                    FLAYOUT_NUM = wo.FLAYOUT_NUM,
                    FSHEET_NUM = wo.FSHEET_NUM,

                })
                .Where(wheres)
                .OrderBy((wo) => wo.FWORK_ORDER_NO, OrderByType.Desc)
                .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            if (woModels.Count > 0)
            {
                List<string> materialIds1 = woModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                //取出物料信息
                DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(materialIds1);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }
                woModels.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                {
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == workOrder.FMATERIAL_ID);
                    if (material != null)
                    {
                        workOrder.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        workOrder.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        workOrder.FSPEC_DESC = material.FSPEC_DESC;
                        workOrder.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                        workOrder.FGOODS_MODEL = material.FGOODS_MODEL;
                    }
                });

                //取计划员姓名
                var empIds = woModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                if (empIds.Count > 0)
                {
                    var empResult = await RpcGetEmployeesAsync(empIds);
                    if (empResult.StatusCode == 200)
                    {
                        woModels.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                        {
                            if (!string.IsNullOrWhiteSpace(workOrder.FPLAN_EMP_ID))
                            {
                                var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == workOrder.FPLAN_EMP_ID);
                                if (emp != null)
                                {
                                    workOrder.FPLAN_EMP_NAME = emp.FEMP_NAME;
                                }
                            }
                        });
                    }
                    else
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }

                //取出生产单位
                var unitIds = woModels.SelectMany(p => { var ids = new List<string>() { p.FWEIGHT_UNIT_ID, p.FPRO_UNIT_ID }; return ids; }).Distinct().ToList();
                if (unitIds.Count > 0)
                {
                    var unitResult = await RpcGetUnitsAsync(unitIds);
                    if (unitResult.StatusCode == 200)
                    {
                        woModels.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                        {
                            if (!string.IsNullOrWhiteSpace(workOrder.FPRO_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrder.FPRO_UNIT_ID);
                                if (unit != null)
                                {
                                    workOrder.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                    workOrder.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                                }

                            }

                            if (!string.IsNullOrWhiteSpace(workOrder.FWEIGHT_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrder.FWEIGHT_UNIT_ID);
                                if (unit != null)
                                {
                                    workOrder.FWEIGHT_UNIT_CODE = unit.FUNIT_CODE;
                                    workOrder.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                                }

                            }
                        });
                    }
                    else
                    {
                        ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                    }
                }


                //取出订单编号
                var saleOrderIds2 = woModels.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID)).Select(p => p.FSALE_ORDER_ID).ToList();
                if (saleOrderIds2.Count > 0)
                {
                    var soResult = await GetSaleOrdersAsync(saleOrderIds2);
                    if (soResult.StatusCode != 200)
                    {
                        ERROR(soResult, soResult.StatusCode, soResult.Message);
                    }

                    woModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FSALE_ORDER_ID))
                        {
                            var so = soResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == woCraft.FSALE_ORDER_ID);
                            if (so != null)
                            {
                                woCraft.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                            }
                        }

                    });

                }
            }
            #endregion

            #region 查询工艺子表数据
            List<WOCraftScheduleModel> woCraftModels = null;
            if (woModels.Count > 0)
            {
                var woIds = woModels.Select(p => p.FWORK_ORDER_ID).ToList();
                woCraftModels = (await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>(
                                      (wo, woStatus, craft, craftStatus) => new JoinQueryInfos(JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                      JoinType.Left, wo.FWORK_ORDER_ID == craft.FWORK_ORDER_ID,
                                      JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
                  .Where((wo, woStatus) => woIds.Contains(wo.FWORK_ORDER_ID) && !wo.FIF_OUT)//排除委外
                  .Where((wo, woStatus, craft, craftStatus) => wo.FPRO_QTY > craftStatus.FPLAN_QTY)
                  .WhereIF(!string.IsNullOrWhiteSpace(craft), (wo, woStatus, craft) => craftIds.Contains(craft.FCRAFT_ID))
                  .WhereIF(!string.IsNullOrWhiteSpace(saleOrderNo), (wo) => saleOrderIds.Contains(wo.FSALE_ORDER_ID))
                  .Select((wo, woStatus, craft, craftStatus) => new WOCraftScheduleModel()
                  {
                      FECODE = wo.FECODE,

                      //工艺的已完工数
                      FFINISH_QTY = craftStatus.FFINISH_QTY,

                      //工艺的已接收数
                      FRECV_QTY = craftStatus.FRECV_QTY,

                      FIF_CLOSE = 0,   //排程任务的结案状态
                      FLEVEL = wo.FLEVEL,

                      FLOT_NO = wo.FLOT_NO,
                      FMATERIAL_ID = wo.FMATERIAL_ID,

                      FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                      FPLAN_EMP_ID = wo.FPLAN_EMP_ID,

                      FPRO_QTY = wo.FPRO_QTY,
                      FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                      FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                      FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                      FWORK_ORDER_ID = wo.FWORK_ORDER_ID,
                      FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                      FCRAFT_ID = craft.FCRAFT_ID,
                      FGEN_RATE = craft.FGEN_RATE,

                      FPROCESS_FEE = craft.FPROCESS_FEE,
                      FSHOW_SEQNO = craft.FSHOW_SEQNO,

                      FSTATION_ID = craft.FSTATION_ID,
                      FWORK_ORDER_CRAFT_ID = craft.FWORK_ORDER_CRAFT_ID,
                      FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                      FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY) < 0 ? 0 : (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                      FPLAN_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY) < 0 ? 0 : (wo.FPRO_QTY - craftStatus.FPLAN_QTY),

                      FPLAN_ED_DATE = craft.FPLAN_ED_DATE ?? wo.FPLAN_ED_DATE,
                      FPLAN_ST_DATE = craft.FPLAN_ST_DATE ?? wo.FPLAN_ST_DATE,


                  })
                  .Where(wheres)
                  .ToListAsync()).OrderByDescending(p => p.FWORK_ORDER_NO).ThenBy(p => p.FSHOW_SEQNO).ToList();


                if (woCraftModels.Count > 0)
                {
                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        //计算计划工时
                        woCraft.FPLAN_USE_HOUR = Math.Round(woCraft.FPLAN_ED_DATE.Value.Subtract(woCraft.FPLAN_ST_DATE.Value).TotalHours,
                            2, MidpointRounding.AwayFromZero).ObjToDecimal();

                        //产品编号
                        var woModel = woModels.FirstOrDefault(p => p.FWORK_ORDER_ID == woCraft.FWORK_ORDER_ID);
                        if (woModel != null)
                        {
                            woCraft.FMATERIAL_CODE = woModel.FMATERIAL_CODE;
                            woCraft.FMATERIAL_NAME = woModel.FMATERIAL_NAME;
                            woCraft.FSPEC_DESC = woModel.FSPEC_DESC;
                            woCraft.FPIC_ATTACH_ID = woModel.FPIC_ATTACH_ID;

                            woCraft.FPRO_UNIT_ID = woModel.FPRO_UNIT_ID;
                            woCraft.FPRO_UNIT_CODE = woModel.FPRO_UNIT_CODE;
                            woCraft.FPRO_UNIT_NAME = woModel.FPRO_UNIT_NAME;

                            woCraft.FSALE_ORDER_NO = woModel.FSALE_ORDER_NO;

                            woCraft.FWEIGHT_UNIT_ID = woModel.FWEIGHT_UNIT_ID;
                            woCraft.FWEIGHT_UNIT_CODE = woModel.FWEIGHT_UNIT_CODE;
                            woCraft.FWEIGHT_UNIT_NAME = woModel.FWEIGHT_UNIT_NAME;

                            woCraft.FUNIT_WEIGHT = woModel.FUNIT_WEIGHT;
                            woCraft.FPLAN_WEIGHT = Math.Round(woCraft.FPLAN_QTY * woModel.FUNIT_WEIGHT, 6);
                            woCraft.FGOODS_MODEL = woModel.FGOODS_MODEL;
                        }

                        //生成任务id
                        woCraft.FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid();

                    });

                    //取出工艺信息
                    var craftIds1 = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                    if (craftIds1.Count > 0)
                    {
                        var craftResult = await RpcGetCraftsAsync(craftIds1);
                        if (craftResult.StatusCode != 200)
                        {
                            ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                        }

                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                        {
                            var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                            if (craft != null)
                            {
                                woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                                woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                                woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                                woCraft.FCRAFT_TYPE = craft.FCRAFT_TYPE;
                            }
                        });
                    }

                    //取出投入物料
                    var subWOMaterials = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => p.FWORK_ORDER_CRAFT_ID != string.Empty && woIds.Contains(p.FWORK_ORDER_ID))
                        .Select(p => new { p.FWORK_ORDER_ID, p.FWORK_ORDER_CRAFT_ID, p.FSUB_MATERIAL_ID })
                        .ToListAsync();
                    if (subWOMaterials.Count > 0)
                    {
                        var subMaterialIds = subWOMaterials.Select(p => p.FSUB_MATERIAL_ID).Distinct().ToList();
                        var materialsResult = await RpcGetMaterialsAsync(subMaterialIds);
                        if (materialsResult.StatusCode == 200)
                        {
                            var daoMaterials = materialsResult.Entity;
                            woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                            {
                                var subMaterils = (from subWOMaterial in subWOMaterials.Where(p => p.FWORK_ORDER_ID == woCraft.FWORK_ORDER_ID && p.FWORK_ORDER_CRAFT_ID == woCraft.FWORK_ORDER_CRAFT_ID)
                                                   join daoMaterial in daoMaterials
                                                   on subWOMaterial.FSUB_MATERIAL_ID equals daoMaterial.FMATERIAL_ID
                                                   select string.Concat(daoMaterial.FMATERIAL_CODE, "/", daoMaterial.FMATERIAL_NAME)).ToList();

                                woCraft.FSUB_MATERIAL = string.Join(",", subMaterils);
                            });
                        }
                        else
                        {
                            ERROR(materialsResult, materialsResult.StatusCode, materialsResult.Message);
                        }
                    }



                }
            }
            #endregion

            ScheduleModel waitModel = new ScheduleModel()
            {
                WOModels = woModels,
                WOCraftModels = woCraftModels ?? new List<WOCraftScheduleModel>(),
            };






            DataResult<ScheduleModel> dataResult = new DataResult<ScheduleModel>()
            {
                Entity = waitModel,
                StatusCode = 200,
                Pager = new PagerResult()
                {
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                    TotalRecords = totalSize.Value,
                },
            };

            return await OK(dataResult);

        }
        #endregion


        /// <summary>
        /// 查询待排程工单数据 主表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WOScheduleModel>>> QueryWaitScheduleMainAsync(QueryRequestModel model)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            #region 查出工艺id列表
            string craft = string.Empty;
            List<string> craftIds = new List<string>();
            var craftItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FCRAFT");
            if (craftItem != null)
            {
                model.WhereGroup.Items.Remove(craftItem);
                if (!string.IsNullOrWhiteSpace(craftItem.Value))
                {
                    craft = craftItem.Value;
                    var craftResult = await RpcGetCraftIdsAsync(craft);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }
                    craftIds = craftResult.Entity;
                }
            }
            #endregion

            #region 查出产品id列表
            string material = string.Empty;
            List<string> materialIds = new List<string>();
            var materialItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FMATERIAL");
            if (materialItem != null)
            {
                model.WhereGroup.Items.Remove(materialItem);
                if (!string.IsNullOrWhiteSpace(materialItem.Value))
                {
                    material = materialItem.Value;
                    var materialResult = await RpcGetMaterialIdsAsync(material);
                    if (materialResult.StatusCode != 200)
                    {
                        ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                    }
                    materialIds = materialResult.Entity;
                }
            }
            #endregion

            #region 查出订单id列表
            string saleOrderNo = string.Empty;
            List<string> saleOrderIds = new List<string>();
            var saleOrderItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FSALE_ORDER_NO");
            if (saleOrderItem != null)
            {
                model.WhereGroup.Items.Remove(saleOrderItem);
                if (!string.IsNullOrWhiteSpace(saleOrderItem.Value))
                {
                    saleOrderNo = saleOrderItem.Value;
                    var soResult = await RpcGetSaleOrderIdsAsync(saleOrderNo);
                    if (soResult.StatusCode != 200)
                    {
                        ERROR(soResult, soResult.StatusCode, soResult.Message);
                    }
                    saleOrderIds = soResult.Entity;
                }
            }
            #endregion

            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);

            #region 查询主表数据
            // 排除变更中(已建变更单未审核）的生产工单
            var lstExcludeOrderId = await db.Queryable<T_MESD_WORK_ORDER_CHANGE, T_MESD_WORK_ORDER_CHANGE_STATUS>(
                (a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_CHANGE_ID == b.FWORK_ORDER_CHANGE_ID))
                .Where((a, b) => !b.FIF_CANCEL && (b.FCFLAG == 0 || b.FCFLAG == 2))
                .Select((a, b) => a.FWORK_ORDER_ID).Distinct().ToListAsync();
            RefAsync<int> totalSize = new RefAsync<int>();
            List<WOScheduleModel> woModels = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>(
                                    (wo, woStatus) => new JoinQueryInfos(JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID))
                .Where((wo, woStatus) => woStatus.FIF_CANCEL == false && woStatus.FCFLAG == 1 && (woStatus.FIF_CLOSE == 0 || woStatus.FIF_CLOSE == 2))
                .Where((wo, woStatus) => SqlFunc.Subqueryable<T_MESD_WORK_ORDER_CRAFT_STATUS>().Where(
                                                        craftStatus => wo.FWORK_ORDER_ID == craftStatus.FWORK_ORDER_ID && wo.FPRO_QTY > craftStatus.FPLAN_QTY).Any())
                .WhereIF(!string.IsNullOrWhiteSpace(craft), (wo, woStatus) => SqlFunc.Subqueryable<T_MESD_WORK_ORDER_CRAFT>().Where(
                                                        craft => wo.FWORK_ORDER_ID == craft.FWORK_ORDER_ID && craftIds.Contains(craft.FCRAFT_ID)).Any())
                .WhereIF(!string.IsNullOrWhiteSpace(material), (wo) => materialIds.Contains(wo.FMATERIAL_ID))
                .WhereIF(!string.IsNullOrWhiteSpace(saleOrderNo), (wo) => saleOrderIds.Contains(wo.FSALE_ORDER_ID))
                .WhereIF(lstExcludeOrderId != null && lstExcludeOrderId.Count > 0, (wo) => !lstExcludeOrderId.Contains(wo.FWORK_ORDER_ID)) // 排除建变更单的生产工单
                .Select((wo, woStatus) => new WOScheduleModel()
                {


                    FCFLAG = woStatus.FCFLAG,
                    FECODE = wo.FECODE,

                    FFINISH_QTY = woStatus.FFINISH_QTY,
                    FIF_CANCEL = woStatus.FIF_CANCEL,

                    FIF_CLOSE = woStatus.FIF_CLOSE,
                    FLEVEL = wo.FLEVEL,

                    FLOT_NO = wo.FLOT_NO,
                    FMATERIAL_ID = wo.FMATERIAL_ID,

                    FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                    FPLAN_ED_DATE = wo.FPLAN_ED_DATE,

                    FPLAN_EMP_ID = wo.FPLAN_EMP_ID,
                    FPLAN_ST_DATE = wo.FPLAN_ST_DATE,

                    FPRO_QTY = wo.FPRO_QTY,
                    FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                    FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                    FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                    FWORK_ORDER_ID = wo.FWORK_ORDER_ID,
                    FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                    FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                    //重量
                    FPLAN_WEIGHT = wo.FPRO_WEIGHT,
                    FWEIGHT_UNIT_ID = wo.FWEIGHT_UNIT_ID,
                    FUNIT_WEIGHT = wo.FUNIT_WEIGHT,
                })
                .Where(wheres)
                .OrderBy((wo) => wo.FWORK_ORDER_NO, OrderByType.Desc)
                .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            if (woModels.Count > 0)
            {
                List<string> materialIds1 = woModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                //取出物料信息
                DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(materialIds1);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }
                woModels.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                {
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == workOrder.FMATERIAL_ID);
                    if (material != null)
                    {
                        workOrder.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        workOrder.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        workOrder.FSPEC_DESC = material.FSPEC_DESC;
                        workOrder.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                        workOrder.FGOODS_MODEL = material.FGOODS_MODEL;
                    }
                });

                //取计划员姓名
                var empIds = woModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                if (empIds.Count > 0)
                {
                    var empResult = await RpcGetEmployeesAsync(empIds);
                    if (empResult.StatusCode == 200)
                    {
                        woModels.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                        {
                            if (!string.IsNullOrWhiteSpace(workOrder.FPLAN_EMP_ID))
                            {
                                var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == workOrder.FPLAN_EMP_ID);
                                if (emp != null)
                                {
                                    workOrder.FPLAN_EMP_NAME = emp.FEMP_NAME;
                                }
                            }
                        });
                    }
                    else
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }

                //取出生产单位
                var unitIds = woModels.SelectMany(p => { var ids = new List<string>() { p.FWEIGHT_UNIT_ID, p.FPRO_UNIT_ID }; return ids; }).Distinct().ToList();
                if (unitIds.Count > 0)
                {
                    var unitResult = await RpcGetUnitsAsync(unitIds);
                    if (unitResult.StatusCode == 200)
                    {
                        woModels.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                        {
                            if (!string.IsNullOrWhiteSpace(workOrder.FPRO_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrder.FPRO_UNIT_ID);
                                if (unit != null)
                                {
                                    workOrder.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                    workOrder.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                                }

                            }

                            if (!string.IsNullOrWhiteSpace(workOrder.FWEIGHT_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrder.FWEIGHT_UNIT_ID);
                                if (unit != null)
                                {
                                    workOrder.FWEIGHT_UNIT_CODE = unit.FUNIT_CODE;
                                    workOrder.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                                }

                            }
                        });
                    }
                    else
                    {
                        ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                    }
                }


                //取出订单编号
                var saleOrderIds2 = woModels.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID)).Select(p => p.FSALE_ORDER_ID).ToList();
                if (saleOrderIds2.Count > 0)
                {
                    var soResult = await GetSaleOrdersAsync(saleOrderIds2);
                    if (soResult.StatusCode != 200)
                    {
                        ERROR(soResult, soResult.StatusCode, soResult.Message);
                    }

                    woModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FSALE_ORDER_ID))
                        {
                            var so = soResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == woCraft.FSALE_ORDER_ID);
                            if (so != null)
                            {
                                woCraft.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                            }
                        }

                    });

                }
            }

            DataResult<List<WOScheduleModel>> dataResult = new DataResult<List<WOScheduleModel>>()
            {
                Entity = woModels,
                StatusCode = 200,
                Pager = new PagerResult()
                {
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                    TotalRecords = totalSize.Value,
                },
            };

            return await OK(dataResult);

        }

        /// <summary>
        /// 查询待排程工单数据明细--根据ID
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WOCraftScheduleModel>>> QueryWaitScheduleDetailAsync(string id)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;
            #region 查询工艺子表数据

            //var woIds = woModels.Select(p => p.FWORK_ORDER_ID).ToList();
            var woIds = new List<string>() { id };
            var woCraftModels = (await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>(
                                   (wo, woStatus, craft, craftStatus) => new JoinQueryInfos(JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                   JoinType.Left, wo.FWORK_ORDER_ID == craft.FWORK_ORDER_ID,
                                   JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
               .Where((wo, woStatus) => woIds.Contains(wo.FWORK_ORDER_ID))
               .Where((wo, woStatus, craft, craftStatus) => wo.FPRO_QTY > craftStatus.FPLAN_QTY)

               .Select((wo, woStatus, craft, craftStatus) => new WOCraftScheduleModel()
               {
                   FECODE = wo.FECODE,

                   //工艺的已完工数
                   FFINISH_QTY = craftStatus.FFINISH_QTY,

                   //工艺的已接收数
                   FRECV_QTY = craftStatus.FRECV_QTY,

                   FIF_CLOSE = 0,   //排程任务的结案状态
                   FLEVEL = wo.FLEVEL,

                   FLOT_NO = wo.FLOT_NO,
                   FMATERIAL_ID = wo.FMATERIAL_ID,

                   FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                   FPLAN_EMP_ID = wo.FPLAN_EMP_ID,

                   FPRO_QTY = wo.FPRO_QTY,
                   FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                   FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                   FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                   FWORK_ORDER_ID = wo.FWORK_ORDER_ID,
                   FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                   FCRAFT_ID = craft.FCRAFT_ID,
                   FGEN_RATE = craft.FGEN_RATE,

                   FPROCESS_FEE = craft.FPROCESS_FEE,
                   FSHOW_SEQNO = craft.FSHOW_SEQNO,

                   FSTATION_ID = craft.FSTATION_ID,
                   FWORK_ORDER_CRAFT_ID = craft.FWORK_ORDER_CRAFT_ID,
                   FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                   FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY) < 0 ? 0 : (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                   FPLAN_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY) < 0 ? 0 : (wo.FPRO_QTY - craftStatus.FPLAN_QTY),

                   FPLAN_ED_DATE = craft.FPLAN_ED_DATE ?? wo.FPLAN_ED_DATE,
                   FPLAN_ST_DATE = craft.FPLAN_ST_DATE ?? wo.FPLAN_ST_DATE,


               })
               //.Where(wheres)
               .ToListAsync()).OrderBy(p => p.FSHOW_SEQNO).ToList();


            if (woCraftModels.Count > 0)
            {
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                {
                    //计算计划工时
                    woCraft.FPLAN_USE_HOUR = Math.Round(woCraft.FPLAN_ED_DATE.Value.Subtract(woCraft.FPLAN_ST_DATE.Value).TotalHours,
                        2, MidpointRounding.AwayFromZero).ObjToDecimal();



                    //生成任务id
                    woCraft.FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid();

                });

                //取出工艺信息
                var craftIds1 = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                if (craftIds1.Count > 0)
                {
                    var craftResult = await RpcGetCraftsAsync(craftIds1);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }

                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                        if (craft != null)
                        {
                            woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                            woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                            woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                        }
                    });
                }

                //取出投入物料
                var subWOMaterials = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => p.FWORK_ORDER_CRAFT_ID != string.Empty && woIds.Contains(p.FWORK_ORDER_ID))
                    .Select(p => new { p.FWORK_ORDER_ID, p.FWORK_ORDER_CRAFT_ID, p.FSUB_MATERIAL_ID })
                    .ToListAsync();
                if (subWOMaterials.Count > 0)
                {
                    var subMaterialIds = subWOMaterials.Select(p => p.FSUB_MATERIAL_ID).Distinct().ToList();
                    var materialsResult = await RpcGetMaterialsAsync(subMaterialIds);
                    if (materialsResult.StatusCode == 200)
                    {
                        var daoMaterials = materialsResult.Entity;
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                        {
                            var subMaterils = (from subWOMaterial in subWOMaterials.Where(p => p.FWORK_ORDER_ID == woCraft.FWORK_ORDER_ID && p.FWORK_ORDER_CRAFT_ID == woCraft.FWORK_ORDER_CRAFT_ID)
                                               join daoMaterial in daoMaterials
                                               on subWOMaterial.FSUB_MATERIAL_ID equals daoMaterial.FMATERIAL_ID
                                               select string.Concat(daoMaterial.FMATERIAL_CODE, "/", daoMaterial.FMATERIAL_NAME)).ToList();

                            woCraft.FSUB_MATERIAL = string.Join(",", subMaterils);
                        });
                    }
                    else
                    {
                        ERROR(materialsResult, materialsResult.StatusCode, materialsResult.Message);
                    }
                }




                List<string> materialIds1 = woCraftModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                //取出物料信息
                DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(materialIds1);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                {
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == workOrder.FMATERIAL_ID);
                    if (material != null)
                    {
                        workOrder.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        workOrder.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        workOrder.FSPEC_DESC = material.FSPEC_DESC;
                        workOrder.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                        workOrder.FGOODS_MODEL = material.FGOODS_MODEL;
                    }
                });

                //取计划员姓名
                var empIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                if (empIds.Count > 0)
                {
                    var empResult = await RpcGetEmployeesAsync(empIds);
                    if (empResult.StatusCode == 200)
                    {
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                        {
                            if (!string.IsNullOrWhiteSpace(workOrder.FPLAN_EMP_ID))
                            {
                                var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == workOrder.FPLAN_EMP_ID);
                                if (emp != null)
                                {
                                    workOrder.FPLAN_EMP_NAME = emp.FEMP_NAME;
                                }
                            }
                        });
                    }
                    else
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }

                //取出生产单位
                var unitIds = woCraftModels.SelectMany(p => { var ids = new List<string>() { p.FWEIGHT_UNIT_ID, p.FPRO_UNIT_ID }; return ids; }).Distinct().ToList();
                if (unitIds.Count > 0)
                {
                    var unitResult = await RpcGetUnitsAsync(unitIds);
                    if (unitResult.StatusCode == 200)
                    {
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                        {
                            if (!string.IsNullOrWhiteSpace(workOrder.FPRO_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrder.FPRO_UNIT_ID);
                                if (unit != null)
                                {
                                    workOrder.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                    workOrder.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                                }

                            }

                            if (!string.IsNullOrWhiteSpace(workOrder.FWEIGHT_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrder.FWEIGHT_UNIT_ID);
                                if (unit != null)
                                {
                                    workOrder.FWEIGHT_UNIT_CODE = unit.FUNIT_CODE;
                                    workOrder.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                                }

                            }
                        });
                    }
                    else
                    {
                        ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                    }
                }


                //取出订单编号
                var saleOrderIds2 = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID)).Select(p => p.FSALE_ORDER_ID).ToList();
                if (saleOrderIds2.Count > 0)
                {
                    var soResult = await GetSaleOrdersAsync(saleOrderIds2);
                    if (soResult.StatusCode != 200)
                    {
                        ERROR(soResult, soResult.StatusCode, soResult.Message);
                    }

                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FSALE_ORDER_ID))
                        {
                            var so = soResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == woCraft.FSALE_ORDER_ID);
                            if (so != null)
                            {
                                woCraft.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                            }
                        }

                    });

                }
            }

            #endregion


            DataResult<List<WOCraftScheduleModel>> dataResult = new DataResult<List<WOCraftScheduleModel>>()
            {
                Entity = woCraftModels,
                StatusCode = 200,

            };

            return await OK(dataResult);

        }










        #endregion
        /// <summary>
        /// 查询是工单否存在排程
        /// </summary>

        public async Task<DataResult<List<string>>> QueryWorkOrdScheduleAsync()
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var WorkOrdSchedules = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().Select(p => p.FWORK_ORDER_ID).Distinct().ToListAsync();

            DataResult<List<string>> dataResult = new DataResult<List<string>>()
            {
                Entity = WorkOrdSchedules,
                StatusCode = 200,
            };

            return await OK(dataResult);

        }

        /// <summary>
        /// 查询排程任务明细
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WOCraftScheduleModel>>> QueryScheduleTaskDetailAsync(QueryRequestModel model)
        {
            var scheduleTask = await QueryScheduleTaskAsync(model);

            var scheduleIds = scheduleTask.Entity.Select(p => p.FCRAFT_SCHEDULE_ID).ToList();

            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var jobBookings = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => scheduleIds.Contains(p.FCRAFT_SCHEDULE_ID)).ToListAsync();

            scheduleTask.Entity.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var jobs = jobBookings.Where(p => p.FCRAFT_SCHEDULE_ID == item.FCRAFT_SCHEDULE_ID).ToList();


                if (jobs.Count(j => j.FWORK_STATUS == "working") > 0)
                {
                    item.FCRAFT_STATUS = "working";//加工中

                }
                else if (jobs.Count(j => j.FWORK_STATUS == "paused") > 0)
                {
                    item.FCRAFT_STATUS = "paused";//暂停中
                }
                else if (jobs.Count(j => j.FWORK_STATUS == "finished") > 0)
                {
                    item.FCRAFT_STATUS = "finished";//已完工
                }
                else if (jobs.Count(j => j.FWORK_STATUS == "cancel") > 0)
                {
                    item.FCRAFT_STATUS = "cancel";//取消加工
                }
                else
                {
                    item.FCRAFT_STATUS = "unstart";//未开工
                }
            });
            return await OK(scheduleTask);

        }


        /// <summary>
        /// 根据工位查询 未结案的排程任务 和  未完工的加工任务 工位排单列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<StationWorkList>>> QueryScheduleTaskByStationAsync(string id)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var jobBookings = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER>((a, b, c) => new JoinQueryInfos(JoinType.Left, a.FCRAFT_SCHEDULE_ID == b.FCRAFT_SCHEDULE_ID, JoinType.Left, a.FWORK_ORDER_ID == c.FWORK_ORDER_ID)).Where((a, b, c) => a.FSTATION_ID == id && a.FWORK_STATUS != "finished" && a.FWORK_STATUS != "cancel").Select<StationWorkList>().ToListAsync();

            var scheduleIds = jobBookings.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();

            var schedules = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((a, b, c, d) => new JoinQueryInfos(JoinType.Left, a.FCRAFT_SCHEDULE_ID == b.FCRAFT_SCHEDULE_ID, JoinType.Left, a.FWORK_ORDER_ID == c.FWORK_ORDER_ID, JoinType.Left, c.FWORK_ORDER_ID == d.FWORK_ORDER_ID)).Where((a, b, c, d) => !d.FIF_CANCEL && b.FIF_CLOSE == 0 && a.FSTATION_ID == id && !scheduleIds.Contains(a.FCRAFT_SCHEDULE_ID)).Select<StationWorkList>().ToListAsync();
            var schids = schedules.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
            schids = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(a => schids.Contains(a.FCRAFT_SCHEDULE_ID) && a.FWORK_STATUS != "finished" && a.FWORK_STATUS != "cancel").Select(a => a.FCRAFT_SCHEDULE_ID).ToListAsync();

            schedules = schedules.Where(p => !schids.Contains(p.FCRAFT_SCHEDULE_ID)).ToList();

            schedules.AddRange(jobBookings);
            var station = await RpcGetStationsAsync(schedules.Select(p => p.FSTATION_ID).Distinct().ToList());

            var matIds = schedules.Select(p => p.FMATERIAL_ID).ToList();
            var matInfos = await RpcGetMaterialsAsync(matIds);

            schedules.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {

                if (string.IsNullOrEmpty(item.FWORK_STATUS))
                {
                    item.FWORK_STATUS = "unstart";
                }

                var stationData = station.Entity.FirstOrDefault(p => p.FSTATION_ID == item.FSTATION_ID);
                if (stationData != null)
                {
                    item.FSTATION_CODE = stationData.FSTATION_CODE;
                    item.FSTATION_NAME = stationData.FSTATION_NAME;
                }

                var matData = matInfos.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                if (matData != null)
                {
                    item.FMATERIAL_CODE = matData.FMATERIAL_CODE;
                    item.FMATERIAL_NAME = matData.FMATERIAL_NAME;
                }

            });

            return await OK(new DataResult<List<StationWorkList>>() { Entity = schedules, StatusCode = 200 });

        }

        #region 查询排程
        /// <summary>
        /// 查询排程任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WOCraftScheduleModel>>> QueryScheduleTaskAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            #region 查出工艺id列表
            string craft = string.Empty;
            List<string> craftIds = new List<string>();
            var craftItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FCRAFT");
            if (craftItem != null)
            {
                model.WhereGroup.Items.Remove(craftItem);
                if (!string.IsNullOrWhiteSpace(craftItem.Value))
                {
                    craft = craftItem.Value;
                    var craftResult = await RpcGetCraftIdsAsync(craft);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }
                    craftIds = craftResult.Entity;
                }
            }
            #endregion

            #region 查出工位id列表
            string station = string.Empty;
            List<string> stationIds = new List<string>();
            var stationItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FSTATION");
            if (stationItem != null)
            {
                model.WhereGroup.Items.Remove(stationItem);
                if (!string.IsNullOrWhiteSpace(stationItem.Value))
                {
                    station = stationItem.Value;
                    var stationResult = await RpcGetStationIdsByStationAsync(station);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                    stationIds = stationResult.Entity;
                }
            }
            #endregion


            #region 查出订单id列表
            string saleord = string.Empty;
            List<string> saleOrdIds = new List<string>();
            var saleOrdItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FSALE_ORDER_NO");
            if (saleOrdItem != null)
            {
                model.WhereGroup.Items.Remove(saleOrdItem);
                if (!string.IsNullOrWhiteSpace(saleOrdItem.Value))
                {


                    saleord = saleOrdItem.Value;
                    var saleIdsRPC = await GetService<ICOP001SaleOrderService>("COP001SaleOrder").GetSaleOrdIdsByCodeAsync(saleord);
                    saleOrdIds = saleIdsRPC.Entity;
                }
            }
            #endregion


            #region 查出产品id列表
            string material = string.Empty;
            List<string> materialIds = new List<string>();
            var materialItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FMATERIAL");
            if (materialItem != null)
            {
                model.WhereGroup.Items.Remove(materialItem);
                if (!string.IsNullOrWhiteSpace(materialItem.Value))
                {
                    material = materialItem.Value;
                    var materialResult = await RpcGetMaterialIdsAsync(material);
                    if (materialResult.StatusCode != 200)
                    {
                        ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                    }
                    materialIds = materialResult.Entity;
                }
            }
            #endregion

            #region 根据车间查出工位id列表
            string workShopName = string.Empty;
            List<string> workShopStationIds = new List<string>();
            var workShopQuery = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FWORKSHOP");
            if (workShopQuery != null)
            {
                model.WhereGroup.Items.Remove(workShopQuery);
                if (!string.IsNullOrWhiteSpace(workShopQuery.Value))
                {
                    workShopName = workShopQuery.Value;
                    var stationResult = await RpcGetStationIdsByWorkShopAsync(workShopName);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                    workShopStationIds = stationResult.Entity;
                }
            }
            #endregion

            #region 根据产线查出工位id列表
            string proLineName = string.Empty;
            List<string> proLineStationIds = new List<string>();
            var proLineQuery = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "PROLINE");
            if (proLineQuery != null)
            {
                model.WhereGroup.Items.Remove(proLineQuery);
                if (!string.IsNullOrWhiteSpace(proLineQuery.Value))
                {
                    proLineName = proLineQuery.Value;
                    var stationResult = await RpcGetStationIdsByProLineAsync(proLineName);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                    proLineStationIds = stationResult.Entity;
                }
            }
            #endregion



            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);

            //查询任务
            var scheduleResult = await QueryTaskAsync(craft, craftIds, material, materialIds, workShopName, workShopStationIds, proLineName, proLineStationIds, wheres, model, saleord, saleOrdIds, station, stationIds);

            //result         
            DataResult<List<WOCraftScheduleModel>> result = new DataResult<List<WOCraftScheduleModel>>
            {
                Entity = scheduleResult.Item1,
                StatusCode = 200,
                Pager = new PagerResult
                {
                    TotalRecords = scheduleResult.Item2,
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                }
            };
            return await OK(result);

        }





        /// <summary>
        /// 查询排程任务--用来手工排程
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WOCraftScheduleModel>>> QueryScheduleTaskByStationIdAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            #region 查出工艺id列表
            string craft = string.Empty;
            List<string> craftIds = new List<string>();
            var craftItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FCRAFT");
            if (craftItem != null)
            {
                model.WhereGroup.Items.Remove(craftItem);
                if (!string.IsNullOrWhiteSpace(craftItem.Value))
                {
                    craft = craftItem.Value;
                    var craftResult = await RpcGetCraftIdsAsync(craft);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }
                    craftIds = craftResult.Entity;
                }
            }
            #endregion

            #region 查出工位id列表
            string station = string.Empty;
            List<string> stationIds = new List<string>();
            var stationItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FSTATION_ID");
            if (stationItem != null)
            {
                stationIds.Add(stationItem.Value);
                //model.WhereGroup.Items.Remove(stationItem);
                //if (!string.IsNullOrWhiteSpace(stationItem.Value))
                //{
                //    craft = craftItem.Value;
                //    var stationResult = await RpcGetStationIdsByStationAsync(station);
                //    if (stationResult.StatusCode != 200)
                //    {
                //        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                //    }
                //    stationIds = stationResult.Entity;
                //}
            }
            #endregion


            #region 查出订单id列表
            string saleord = string.Empty;
            List<string> saleOrdIds = new List<string>();
            var saleOrdItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FSALE_ORDER_NO");
            if (saleOrdItem != null)
            {
                model.WhereGroup.Items.Remove(saleOrdItem);
                if (!string.IsNullOrWhiteSpace(saleOrdItem.Value))
                {


                    saleord = saleOrdItem.Value;
                    var saleIdsRPC = await GetService<ICOP001SaleOrderService>("COP001SaleOrder").GetSaleOrdIdsByCodeAsync(saleord);
                    saleOrdIds = saleIdsRPC.Entity;
                }
            }
            #endregion


            #region 查出产品id列表
            string material = string.Empty;
            List<string> materialIds = new List<string>();
            var materialItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FMATERIAL");
            if (materialItem != null)
            {
                model.WhereGroup.Items.Remove(materialItem);
                if (!string.IsNullOrWhiteSpace(materialItem.Value))
                {
                    material = materialItem.Value;
                    var materialResult = await RpcGetMaterialIdsAsync(material);
                    if (materialResult.StatusCode != 200)
                    {
                        ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                    }
                    materialIds = materialResult.Entity;
                }
            }
            #endregion

            #region 根据车间查出工位id列表
            string workShopName = string.Empty;
            List<string> workShopStationIds = new List<string>();
            var workShopQuery = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FWORKSHOP");
            if (workShopQuery != null)
            {
                model.WhereGroup.Items.Remove(workShopQuery);
                if (!string.IsNullOrWhiteSpace(workShopQuery.Value))
                {
                    workShopName = workShopQuery.Value;
                    var stationResult = await RpcGetStationIdsByWorkShopAsync(workShopName);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                    workShopStationIds = stationResult.Entity;
                }
            }
            #endregion

            #region 根据产线查出工位id列表
            string proLineName = string.Empty;
            List<string> proLineStationIds = new List<string>();
            var proLineQuery = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "PROLINE");
            if (proLineQuery != null)
            {
                model.WhereGroup.Items.Remove(proLineQuery);
                if (!string.IsNullOrWhiteSpace(proLineQuery.Value))
                {
                    proLineName = proLineQuery.Value;
                    var stationResult = await RpcGetStationIdsByProLineAsync(proLineName);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                    proLineStationIds = stationResult.Entity;
                }
            }
            #endregion



            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);

            //查询任务
            var scheduleResult = await QueryTaskStationAsync(craft, craftIds, material, materialIds, workShopName, workShopStationIds, proLineName, proLineStationIds, wheres, model, saleord, saleOrdIds, station, stationIds);

            //result         
            DataResult<List<WOCraftScheduleModel>> result = new DataResult<List<WOCraftScheduleModel>>
            {
                Entity = scheduleResult.Item1.OrderBy(p => p.SCHEDULE_FSHOW_SEQNO).ToList(),
                StatusCode = 200,
                Pager = new PagerResult
                {
                    TotalRecords = scheduleResult.Item2,
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                }
            };
            return await OK(result);

        }

        /// <summary>
        /// 查询任务---手工排程使用
        /// </summary>
        /// <returns></returns>
        private async Task<(List<WOCraftScheduleModel>, int)> QueryTaskStationAsync(string craft, List<string> craftIds, string material,
            List<string> materialIds, string workShopName, List<string> workShopStationIds, string proLineName, List<string> proLineStationIds, WhereCollections wheres,
            QueryRequestModel model, string saleOrdNo, List<String> saleOrdIds, string station, List<String> stationIds)
        {

            var db = _isugar.DB;

            //总行数
            RefAsync<int> totalSize = new RefAsync<int>();

            //查询
            var woCraftModels = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                                    T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>(
                                 (sch, schStatus, wo, woStatus, craft, craftStatus) =>
                                 new JoinQueryInfos(
                                 JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID,
                                 JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
             .WhereIF(!string.IsNullOrWhiteSpace(craft), (sch, schStatus, wo, woStatus, craft, craftStatus) => craftIds.Contains(sch.FCRAFT_ID))
                .WhereIF(!string.IsNullOrWhiteSpace(saleOrdNo), (sch, schStatus, wo, woStatus, craft, craftStatus) => saleOrdIds.Contains(sch.FSALE_ORDER_ID))
             .WhereIF(!string.IsNullOrWhiteSpace(material), (sch, schStatus, wo, woStatus, craft, craftStatus) => materialIds.Contains(sch.FMATERIAL_ID))
               .WhereIF(!string.IsNullOrWhiteSpace(workShopName), (sch, schStatus, wo, woStatus, craft, craftStatus) => workShopStationIds.Contains(sch.FSTATION_ID))
                 .WhereIF(!string.IsNullOrWhiteSpace(proLineName), (sch, schStatus, wo, woStatus, craft, craftStatus) => proLineStationIds.Contains(sch.FSTATION_ID))
                 .WhereIF(!string.IsNullOrWhiteSpace(station), (sch, schStatus, wo, woStatus, craft, craftStatus) => stationIds.Contains(sch.FSTATION_ID))
                 .Where((sch, schStatus, wo, woStatus, craft, craftStatus) => (schStatus.FIF_CLOSE == 0 || schStatus.FIF_CLOSE == 2) && schStatus.FRELEASE_STATUS == true)
             //    .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => wo.FWORK_ORDER_NO, OrderByType.Desc)
             //.OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => craft.FSHOW_SEQNO, OrderByType.Asc)
             .Select((sch, schStatus, wo, woStatus, craft, craftStatus) => new WOCraftScheduleModel()
             {
                 FECODE = sch.FECODE,

                 FFINISH_QTY = craftStatus.FFINISH_QTY,
                 FRECV_QTY = craftStatus.FRECV_QTY,

                 FIF_CLOSE = schStatus.FIF_CLOSE,
                 FLEVEL = wo.FLEVEL,

                 FLOT_NO = sch.FLOT_NO,
                 FMATERIAL_ID = sch.FMATERIAL_ID,

                 FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                 FPLAN_EMP_ID = sch.FPLAN_EMP_ID,

                 FPRO_QTY = wo.FPRO_QTY,
                 FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                 FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                 FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                 FWORK_ORDER_ID = sch.FWORK_ORDER_ID,
                 FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                 FCRAFT_ID = sch.FCRAFT_ID,
                 FGEN_RATE = craft.FGEN_RATE,

                 FPROCESS_FEE = craft.FPROCESS_FEE,
                 FSHOW_SEQNO = craft.FSHOW_SEQNO,

                 FSTATION_ID = sch.FSTATION_ID,
                 FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                 FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                 FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY) < 0 ? 0 : (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                 FPLAN_QTY = sch.FPLAN_QTY,
                 FORI_PLAN_QTY = sch.FORI_PLAN_QTY,

                 FPLAN_ED_DATE = sch.FPLAN_ED_DATE,
                 FPLAN_ST_DATE = sch.FPLAN_ST_DATE,

                 FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,

                 FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                 FQRCODE = sch.FQRCODE,

                 FPLAN_USE_HOUR = sch.FPLAN_USE_HOUR,

                 FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                 FSCHEDULE_BATCH_ID = sch.FSCHEDULE_BATCH_ID,

                 FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,

                 FCLOSEDATE = schStatus.FCLOSEDATE,
                 FCLOSER = schStatus.FCLOSER,
                 FCLOSER_ID = schStatus.FCLOSER_ID,

                 FRELEASER = schStatus.FRELEASER,
                 FRELEASER_ID = schStatus.FRELEASER_ID,
                 FRELEASE_DATE = schStatus.FRELEASE_DATE,

                 FRELEASE_QTY = craftStatus.FRELEASE_QTY,

                 FCREATOR = sch.FCREATOR,
                 FCREATOR_ID = sch.FCREATOR_ID,
                 FCDATE = sch.FCDATE,

                 FMODIDATE = sch.FMODIDATE,
                 FMODIFIER = sch.FMODIFIER,
                 FMODIFIER_ID = sch.FMODIFIER_ID,

                 FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,   //排程任务的完工数量
                 FPASS_QTY_SCHEDULE = schStatus.FPASS_QTY,   //排程任务的合格数
                 FNG_QTY_SCHEDULE = schStatus.FNG_QTY,    //排程任务的不良数

                 FACT_ST_DATE = schStatus.FACT_ST_DATE,   //排程任务的实际开工
                 FACT_ED_DATE = schStatus.FACT_ED_DATE,   //排程任务的实际完工
                 FACT_USE_HOUR = schStatus.FACT_USE_HOUR,  //排程任务的实际工时

                 FPLAN_WEIGHT = sch.FPLAN_WEIGHT,   //计划产出重量
                 FWEIGHT_UNIT_ID = sch.FWEIGHT_UNIT_ID,  //重量单位
                 FUNIT_WEIGHT = sch.FUNIT_WEIGHT,  //单位重量

                 FFINISH_WEIGHT = schStatus.FFINISH_WEIGHT,
                 FPASS_WEIGHT = schStatus.FPASS_WEIGHT,
                 FNG_WEIGHT = schStatus.FNG_WEIGHT,

                 FPRO_WEIGHT = wo.FPRO_WEIGHT,

                 FIS_OUT = schStatus.FIS_OUT,
                 FBOOKING_TYPE = wo.FBOOKING_TYPE,
                 SCHEDULE_FSHOW_SEQNO = sch.SCHEDULE_FSHOW_SEQNO,

                 FIUIU_CUST = wo.FIUIU_CUST,
                 FIUIU_ORDER_DATE = wo.FIUIU_ORDER_DATE,
                 FIUIU_ORDER_NO = wo.FIUIU_ORDER_NO,
                 FIUIU_SALE_NAME = wo.FIUIU_SALE_NAME

             })
             .Where(wheres)
             .OrderBy((sch) => sch.SCHEDULE_FSHOW_SEQNO, OrderByType.Desc)
             .OrderBy((sch) => sch.FWORK_ORDER_NO, OrderByType.Desc)
             .OrderBy((sch) => sch.FSHOW_SEQNO, OrderByType.Asc)
             .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            if (woCraftModels.Count > 0)
            {
                List<string> materialIds1 = woCraftModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                //取出物料信息
                DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(materialIds1);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }


                //取计划员姓名
                var empIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                DataResult<List<EmployeeModel>> empResult = null;
                if (empIds.Count > 0)
                {
                    empResult = await RpcGetEmployeesAsync(empIds);
                    if (empResult.StatusCode != 200)
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }

                //取出生产单位
                var unitIds = woCraftModels.SelectMany(p => { var ids = new List<string>() { p.FPRO_UNIT_ID, p.FWEIGHT_UNIT_ID }; return ids; }).Distinct().ToList();
                DataResult<List<SimpleUnitModel>> unitResult = null;
                if (unitIds.Count > 0)
                {
                    unitResult = await RpcGetUnitsAsync(unitIds);
                    if (unitResult.StatusCode != 200)
                    {
                        ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                    }
                }

                //取出工艺信息
                var craftIds1 = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                DataResult<List<SimpleCraftModel>> craftResult = null;
                if (craftIds1.Count > 0)
                {
                    craftResult = await RpcGetCraftsAsync(craftIds1);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }
                }


                //取出工位信息
                var stationIds1 = woCraftModels.Select(p => p.FSTATION_ID).Distinct().ToList();
                DataResult<List<IModuleServices.MES001WorkArea.Models.SimpleStationModel>> stationResult = null;
                if (stationIds1.Count > 0)
                {
                    stationResult = await RpcGetStationsAsync(stationIds1);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                }

                //取出订单编号
                var saleOrderIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID)).Select(p => p.FSALE_ORDER_ID).ToList();
                DataResult<List<SaleOrderSimpleModel>> soResult = null;
                if (saleOrderIds.Count > 0)
                {
                    soResult = await GetSaleOrdersAsync(saleOrderIds);
                    if (soResult.StatusCode != 200)
                    {
                        ERROR(soResult, soResult.StatusCode, soResult.Message);
                    }
                }

                //获取委外申请单已转数量
                List<string> outApplyIds = woCraftModels.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
                var outApplys = await (GetService<IPUR013OutApplyService>("PUR013OutApply")).GetByIdsAsync(outApplyIds);

                //合并到一处循环
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                {
                    //物料
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == woCraft.FMATERIAL_ID);
                    if (material != null)
                    {
                        woCraft.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        woCraft.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        woCraft.FSPEC_DESC = material.FSPEC_DESC;
                        woCraft.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                        woCraft.FGOODS_MODEL = material.FGOODS_MODEL;
                    }

                    //已转申请单数量
                    var outApply = outApplys.Entity.FirstOrDefault(p => p.WoMatId == woCraft.FCRAFT_SCHEDULE_ID);
                    if (outApply != null)
                    {
                        woCraft.OutApplyCount = outApply.count;
                    }

                    //计划员
                    if (empResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FPLAN_EMP_ID))
                        {
                            var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == woCraft.FPLAN_EMP_ID);
                            if (emp != null)
                            {
                                woCraft.FPLAN_EMP_NAME = emp.FEMP_NAME;
                            }
                        }
                    }

                    //单位
                    if (unitResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FPRO_UNIT_ID))
                        {
                            var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == woCraft.FPRO_UNIT_ID);
                            if (unit != null)
                            {
                                woCraft.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                woCraft.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                            }
                        }

                        if (!string.IsNullOrWhiteSpace(woCraft.FWEIGHT_UNIT_ID))
                        {
                            var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == woCraft.FWEIGHT_UNIT_ID);
                            if (unit != null)
                            {
                                woCraft.FWEIGHT_UNIT_CODE = unit.FUNIT_CODE;
                                woCraft.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                            }
                        }
                    }

                    //工艺
                    if (craftResult != null)
                    {
                        var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                        if (craft != null)
                        {
                            woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                            woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                            woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                        }
                    }

                    //工位
                    if (stationResult != null)
                    {
                        var station = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == woCraft.FSTATION_ID);
                        if (station != null)
                        {
                            woCraft.FSTATION_CODE = station.FSTATION_CODE;
                            woCraft.FSTATION_NAME = station.FSTATION_NAME;
                            woCraft.FSTATION_PROPERTY = station.FSTATION_PROPERTY;
                        }
                    }

                    //订单
                    if (soResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FSALE_ORDER_ID))
                        {
                            var so = soResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == woCraft.FSALE_ORDER_ID);
                            if (so != null)
                            {
                                woCraft.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                            }
                        }

                    }

                });

                //取出投入物料
                await GetSubMaterialAsync(woCraftModels);

                //取出执行人
                await GetScheduleEmpsAsync(woCraftModels);



                //获取排程的加工状态
                await GetScheduleStatusAsync(woCraftModels);
            }
            woCraftModels = woCraftModels.Where(p => p.FCRAFT_STATUS != "finished").ToList();
            //返回结果
            return (woCraftModels, totalSize.Value);
        }



        /// <summary>
        /// 查询任务
        /// </summary>
        /// <returns></returns>
        private async Task<(List<WOCraftScheduleModel>, int)> QueryTaskAsync(string craft, List<string> craftIds, string material,
            List<string> materialIds, string workShopName, List<string> workShopStationIds, string proLineName, List<string> proLineStationIds, WhereCollections wheres,
            QueryRequestModel model, string saleOrdNo, List<String> saleOrdIds, string station, List<String> stationIds)
        {

            var db = _isugar.DB;

            //总行数
            RefAsync<int> totalSize = new RefAsync<int>();

            string orderByString = null;
            if (model.Orders != null && model.Orders.Any())
            {
                orderByString = string.Join(",",
                    model.Orders.Select(x => $"{x.Fields[0]} {x.OrderType}"));
            }

            //查询
            var woCraftModels = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                                    T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>(
                                 (sch, schStatus, wo, woStatus, craft, craftStatus) =>
                                 new JoinQueryInfos(
                                 JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID,
                                 JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
             .WhereIF(!string.IsNullOrWhiteSpace(craft), (sch, schStatus, wo, woStatus, craft, craftStatus) => craftIds.Contains(sch.FCRAFT_ID))
                .WhereIF(!string.IsNullOrWhiteSpace(saleOrdNo), (sch, schStatus, wo, woStatus, craft, craftStatus) => saleOrdIds.Contains(sch.FSALE_ORDER_ID))
             .WhereIF(!string.IsNullOrWhiteSpace(material), (sch, schStatus, wo, woStatus, craft, craftStatus) => materialIds.Contains(sch.FMATERIAL_ID))
               .WhereIF(!string.IsNullOrWhiteSpace(workShopName), (sch, schStatus, wo, woStatus, craft, craftStatus) => workShopStationIds.Contains(sch.FSTATION_ID))
                 .WhereIF(!string.IsNullOrWhiteSpace(proLineName), (sch, schStatus, wo, woStatus, craft, craftStatus) => proLineStationIds.Contains(sch.FSTATION_ID))
                 .WhereIF(!string.IsNullOrWhiteSpace(station), (sch, schStatus, wo, woStatus, craft, craftStatus) => stationIds.Contains(sch.FSTATION_ID))
                 .OrderByIF(orderByString != null, orderByString)
                 .OrderByIF(orderByString == null, (sch, schStatus, wo, woStatus, craft, craftStatus) => wo.FWORK_ORDER_NO, OrderByType.Desc)
             .OrderByIF(orderByString == null, (sch, schStatus, wo, woStatus, craft, craftStatus) => craft.FSHOW_SEQNO, OrderByType.Asc)
             .Select((sch, schStatus, wo, woStatus, craft, craftStatus) => new WOCraftScheduleModel()
             {
                 FECODE = sch.FECODE,

                 FFINISH_QTY = craftStatus.FFINISH_QTY,
                 FRECV_QTY = craftStatus.FRECV_QTY,

                 FIF_CLOSE = schStatus.FIF_CLOSE,
                 FLEVEL = wo.FLEVEL,

                 FLOT_NO = sch.FLOT_NO,
                 FMATERIAL_ID = sch.FMATERIAL_ID,

                 FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                 FPLAN_EMP_ID = sch.FPLAN_EMP_ID,

                 FPRO_QTY = wo.FPRO_QTY,
                 FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                 FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                 FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                 FWORK_ORDER_ID = sch.FWORK_ORDER_ID,
                 FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                 FCRAFT_ID = sch.FCRAFT_ID,
                 FGEN_RATE = craft.FGEN_RATE,

                 FPROCESS_FEE = craft.FPROCESS_FEE,
                 FSHOW_SEQNO = craft.FSHOW_SEQNO,

                 FSTATION_ID = sch.FSTATION_ID,
                 FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                 FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                 FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY) < 0 ? 0 : (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                 FPLAN_QTY = sch.FPLAN_QTY,
                 FORI_PLAN_QTY = sch.FORI_PLAN_QTY,

                 FPLAN_ED_DATE = sch.FPLAN_ED_DATE,
                 FPLAN_ST_DATE = sch.FPLAN_ST_DATE,

                 FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,

                 FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                 FQRCODE = sch.FQRCODE,

                 FPLAN_USE_HOUR = sch.FPLAN_USE_HOUR,

                 FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                 FSCHEDULE_BATCH_ID = sch.FSCHEDULE_BATCH_ID,

                 FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,

                 FCLOSEDATE = schStatus.FCLOSEDATE,
                 FCLOSER = schStatus.FCLOSER,
                 FCLOSER_ID = schStatus.FCLOSER_ID,

                 FRELEASER = schStatus.FRELEASER,
                 FRELEASER_ID = schStatus.FRELEASER_ID,
                 FRELEASE_DATE = schStatus.FRELEASE_DATE,

                 FRELEASE_QTY = craftStatus.FRELEASE_QTY,

                 FCREATOR = sch.FCREATOR,
                 FCREATOR_ID = sch.FCREATOR_ID,
                 FCDATE = sch.FCDATE,

                 FMODIDATE = sch.FMODIDATE,
                 FMODIFIER = sch.FMODIFIER,
                 FMODIFIER_ID = sch.FMODIFIER_ID,

                 FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,   //排程任务的完工数量
                 FPASS_QTY_SCHEDULE = schStatus.FPASS_QTY,   //排程任务的合格数
                 FNG_QTY_SCHEDULE = schStatus.FNG_QTY,    //排程任务的不良数

                 FACT_ST_DATE = schStatus.FACT_ST_DATE,   //排程任务的实际开工
                 FACT_ED_DATE = schStatus.FACT_ED_DATE,   //排程任务的实际完工
                 FACT_USE_HOUR = schStatus.FACT_USE_HOUR,  //排程任务的实际工时

                 FPLAN_WEIGHT = sch.FPLAN_WEIGHT,   //计划产出重量
                 FWEIGHT_UNIT_ID = sch.FWEIGHT_UNIT_ID,  //重量单位
                 FUNIT_WEIGHT = sch.FUNIT_WEIGHT,  //单位重量

                 FFINISH_WEIGHT = schStatus.FFINISH_WEIGHT,
                 FPASS_WEIGHT = schStatus.FPASS_WEIGHT,
                 FNG_WEIGHT = schStatus.FNG_WEIGHT,

                 FPRO_WEIGHT = wo.FPRO_WEIGHT,

                 FIS_OUT = schStatus.FIS_OUT,
                 FBOOKING_TYPE = wo.FBOOKING_TYPE,

                 FWORK_NUM = sch.FWORK_NUM,

                 FPARENT_CRAFT_SCHEDULE_ID = sch.FPARENT_CRAFT_SCHEDULE_ID,
                 FSOURCE_JOB_BOOKING_ID = sch.FSOURCE_JOB_BOOKING_ID,
                 FSCHEDULE_TEXT1 = sch.FSCHEDULE_TEXT1,
             })
             .Where(wheres)
             //.OrderBy((sch) => sch.FCRAFT_SCHEDULE_NO, OrderByType.Desc)
             .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            if (woCraftModels.Count > 0)
            {
                List<string> materialIds1 = woCraftModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                //取出物料信息
                DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(materialIds1);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }


                //取计划员姓名
                var empIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                DataResult<List<EmployeeModel>> empResult = null;
                if (empIds.Count > 0)
                {
                    empResult = await RpcGetEmployeesAsync(empIds);
                    if (empResult.StatusCode != 200)
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }

                //取出生产单位
                var unitIds = woCraftModels.SelectMany(p => { var ids = new List<string>() { p.FPRO_UNIT_ID, p.FWEIGHT_UNIT_ID }; return ids; }).Distinct().ToList();
                DataResult<List<SimpleUnitModel>> unitResult = null;
                if (unitIds.Count > 0)
                {
                    unitResult = await RpcGetUnitsAsync(unitIds);
                    if (unitResult.StatusCode != 200)
                    {
                        ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                    }
                }

                //取出工艺信息
                var craftIds1 = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                DataResult<List<SimpleCraftModel>> craftResult = null;
                if (craftIds1.Count > 0)
                {
                    craftResult = await RpcGetCraftsAsync(craftIds1);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }
                }


                //取出工位信息
                var stationIds1 = woCraftModels.Select(p => p.FSTATION_ID).Distinct().ToList();
                DataResult<List<IModuleServices.MES001WorkArea.Models.SimpleStationModel>> stationResult = null;
                if (stationIds1.Count > 0)
                {
                    stationResult = await RpcGetStationsAsync(stationIds1);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                }

                //取出订单编号
                var saleOrderIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID)).Select(p => p.FSALE_ORDER_ID).ToList();
                DataResult<List<SaleOrderSimpleModel>> soResult = null;
                if (saleOrderIds.Count > 0)
                {
                    soResult = await GetSaleOrdersAsync(saleOrderIds);
                    if (soResult.StatusCode != 200)
                    {
                        ERROR(soResult, soResult.StatusCode, soResult.Message);
                    }
                }

                //获取委外申请单已转数量
                List<string> outApplyIds = woCraftModels.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
                var outApplys = await (GetService<IPUR013OutApplyService>("PUR013OutApply")).GetByIdsAsync(outApplyIds);

                //合并到一处循环
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                {
                    //物料
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == woCraft.FMATERIAL_ID);
                    if (material != null)
                    {
                        woCraft.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        woCraft.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        woCraft.FSPEC_DESC = material.FSPEC_DESC;
                        woCraft.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                        woCraft.FGOODS_MODEL = material.FGOODS_MODEL;
                    }

                    //已转申请单数量
                    var outApply = outApplys.Entity.FirstOrDefault(p => p.WoMatId == woCraft.FCRAFT_SCHEDULE_ID);
                    if (outApply != null)
                    {
                        woCraft.OutApplyCount = outApply.count;
                    }

                    //计划员
                    if (empResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FPLAN_EMP_ID))
                        {
                            var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == woCraft.FPLAN_EMP_ID);
                            if (emp != null)
                            {
                                woCraft.FPLAN_EMP_NAME = emp.FEMP_NAME;
                            }
                        }
                    }

                    //单位
                    if (unitResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FPRO_UNIT_ID))
                        {
                            var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == woCraft.FPRO_UNIT_ID);
                            if (unit != null)
                            {
                                woCraft.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                woCraft.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                            }
                        }

                        if (!string.IsNullOrWhiteSpace(woCraft.FWEIGHT_UNIT_ID))
                        {
                            var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == woCraft.FWEIGHT_UNIT_ID);
                            if (unit != null)
                            {
                                woCraft.FWEIGHT_UNIT_CODE = unit.FUNIT_CODE;
                                woCraft.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                            }
                        }
                    }

                    //工艺
                    if (craftResult != null)
                    {
                        var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                        if (craft != null)
                        {
                            woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                            woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                            woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                        }
                    }

                    //工位
                    if (stationResult != null)
                    {
                        var station = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == woCraft.FSTATION_ID);
                        if (station != null)
                        {
                            woCraft.FSTATION_CODE = station.FSTATION_CODE;
                            woCraft.FSTATION_NAME = station.FSTATION_NAME;
                            woCraft.FSTATION_PROPERTY = station.FSTATION_PROPERTY;
                        }
                    }

                    //订单
                    if (soResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FSALE_ORDER_ID))
                        {
                            var so = soResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == woCraft.FSALE_ORDER_ID);
                            if (so != null)
                            {
                                woCraft.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                            }
                        }

                    }

                });

                //取出投入物料
                await GetSubMaterialAsync(woCraftModels);

                //取出执行人
                await GetScheduleEmpsAsync(woCraftModels);



                //获取排程的加工状态
                await GetScheduleStatusAsync(woCraftModels);
            }

            //返回结果
            return (woCraftModels, totalSize.Value);
        }



        /// <summary>
        /// 获取排程任务的加工状态
        /// </summary>
        /// <param name="woCraftModels"></param>
        /// <returns></returns>
        private async Task GetScheduleStatusAsync(List<WOCraftScheduleModel> woCraftModels)
        {

            var schIds = woCraftModels.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
            if (schIds.Count > 0)
            {
                var rpcServer = this.GetService<IModuleServices.MES007_JobBooking.IMES007JobQueryService>("MES007JobQuery");
                QueryRequestModel model = new QueryRequestModel()
                {
                    PageIndex = 0,
                    PageSize = int.MaxValue,
                    WhereGroup = new QueryWhereGroupModel
                    {
                        GroupType = EnumGroupType.AND,
                        Items = new List<QueryWhereItemModel> {
                            new QueryWhereItemModel{
                                FieldName="booking.FCRAFT_SCHEDULE_ID",
                                OperatorType=Core.HCPlatform.Utilities.EnumQuerySymbol.In,
                                Value=string.Join(",",schIds),
                                },
                            new QueryWhereItemModel{
                                FieldName="booking.FWORK_STATUS",
                                OperatorType=Core.HCPlatform.Utilities.EnumQuerySymbol.NotEqual,
                                Value="cancel",
                                }
                        }
                    }
                };
                var bookingResult = await rpcServer.QueryJobBookingAsync(model);
                if (bookingResult.StatusCode != 200)
                {
                    ERROR(bookingResult, bookingResult.StatusCode, bookingResult.Message);
                }

                var bookingData = bookingResult.Entity;
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(p =>
                {

                    var bookings = bookingData.Where(j => j.FCRAFT_SCHEDULE_ID == p.FCRAFT_SCHEDULE_ID).ToList();

                    if (bookings.Count > 0)
                    {
                        //完成  加工中    暂停
                        var sumnum = bookings.Sum(s => (s.FPASS_QTY + s.FNG_QTY));//总数

                        var working = bookings.FirstOrDefault(k => k.FWORK_STATUS == "working");//加工中
                        if (working != null)
                        {
                            p.FCRAFT_STATUS = "working";//加工中
                        }

                        var paused = bookings.FirstOrDefault(k => k.FWORK_STATUS == "paused");//暂停中
                        if (paused != null)
                        {
                            p.FCRAFT_STATUS = "paused";//暂停中
                        }

                        if (working == null && paused == null)
                        {
                            p.FCRAFT_STATUS = "finished";//已完成
                        }
                    }
                    else
                    {
                        p.FCRAFT_STATUS = "unstart";//未开工
                    }

                });


            }

        }

        /// <summary>
        /// 返回订单数据
        /// </summary>
        /// <param name="saleOrderIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SaleOrderSimpleModel>>> GetSaleOrdersAsync(List<string> saleOrderIds)
        {
            var rpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
            return await rpcServer.QuerySalesByIdsAsync(saleOrderIds);
        }

        /// <summary>
        /// 取出任务的执行人
        /// </summary>
        /// <param name="woCraftModels"></param>
        /// <returns></returns>
        private async Task GetScheduleEmpsAsync(List<WOCraftScheduleModel> woCraftModels)
        {
            var db = _isugar.DB;

            var schIds = woCraftModels.Select(p => p.FCRAFT_SCHEDULE_ID).ToList();

            List<WOCraftSchedulePersonModel> persons = await db.Queryable<T_MESD_CRAFT_SCHEDULE_PERSON>()
                                                    .Where(p => schIds.Contains(p.FCRAFT_SCHEDULE_ID))
                                                    .Select(p => new WOCraftSchedulePersonModel
                                                    {
                                                        FCRAFT_SCHEDULE_ID = p.FCRAFT_SCHEDULE_ID,
                                                        FCRAFT_SCHEDULE_PERSON_ID = p.FCRAFT_SCHEDULE_PERSON_ID,
                                                        FECODE = p.FECODE,
                                                        FEMP_ID = p.FEMP_ID,
                                                        FSHOW_SEQNO = p.FSHOW_SEQNO,
                                                    })
                                                    .ToListAsync();

            if (persons.Count > 0)
            {
                var empIds = persons.Select(p => p.FEMP_ID).Distinct().ToList();
                var empsResult = await RpcGetEmployeesAsync(empIds);
                if (empsResult.StatusCode != 200)
                {
                    ERROR(empsResult, empsResult.StatusCode, empsResult.Message);
                }

                persons.AsParallel().WithDegreeOfParallelism(4).ForAll(person =>
                {
                    var emp = empsResult.Entity.FirstOrDefault(p => p.FEMP_ID == person.FEMP_ID);
                    person.FEMP_CODE = emp?.FEMP_CODE;
                    person.FEMP_NAME = emp?.FEMP_NAME;
                });

                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                {
                    craft.Persons = persons.Where(p => p.FCRAFT_SCHEDULE_ID == craft.FCRAFT_SCHEDULE_ID).OrderBy(p => p.FSHOW_SEQNO).ToList();
                    if (craft.Persons.Count > 0)
                    {
                        craft.FEMP = string.Join(",", craft.Persons.Select(p => p.FEMP_NAME).ToList());
                    }
                });
            }
        }

        #endregion

        #region 查询子方法
        /// <summary>
        /// 取出排程任务的投入材料
        /// </summary>
        /// <returns></returns>
        private async Task GetSubMaterialAsync(List<WOCraftScheduleModel> woCraftModels)
        {
            var db = _isugar.DB;

            //取出投入物料
            var woIds = woCraftModels.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();
            if (woIds.Count > 0)
            {
                var subWOMaterials = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => p.FWORK_ORDER_CRAFT_ID != string.Empty && woIds.Contains(p.FWORK_ORDER_ID))
                    .Select(p => new { p.FWORK_ORDER_ID, p.FWORK_ORDER_CRAFT_ID, p.FSUB_MATERIAL_ID })
                    .ToListAsync();
                if (subWOMaterials.Count > 0)
                {
                    var subMaterialIds = subWOMaterials.Select(p => p.FSUB_MATERIAL_ID).Distinct().ToList();
                    var materialsResult = await RpcGetMaterialsAsync(subMaterialIds);
                    if (materialsResult.StatusCode == 200)
                    {
                        var daoMaterials = materialsResult.Entity;
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                        {
                            var subMaterils = (from subWOMaterial in subWOMaterials.Where(p => p.FWORK_ORDER_ID == woCraft.FWORK_ORDER_ID
                                                                                            && p.FWORK_ORDER_CRAFT_ID == woCraft.FWORK_ORDER_CRAFT_ID)
                                               join daoMaterial in daoMaterials
                                               on subWOMaterial.FSUB_MATERIAL_ID equals daoMaterial.FMATERIAL_ID
                                               select string.Concat(daoMaterial.FMATERIAL_CODE, "/", daoMaterial.FMATERIAL_NAME)).ToList();

                            woCraft.FSUB_MATERIAL = string.Join(",", subMaterils);
                        });
                    }
                    else
                    {
                        ERROR(materialsResult, materialsResult.StatusCode, materialsResult.Message);
                    }
                }
            }
        }

        /// <summary>
        /// 返回工艺id列表
        /// </summary>
        /// <param name="craft"></param>
        /// <returns></returns>
        private async Task<DataResult<List<string>>> RpcGetCraftIdsAsync(string craft)
        {
            IMES002CraftService rpcServer = this.GetService<IMES002CraftService>("MES002Craft");

            QueryRequestModel model = new QueryRequestModel();

            model.WhereGroup = new QueryWhereGroupModel();
            model.WhereGroup.GroupType = EnumGroupType.OR;
            model.WhereGroup.Items = new List<QueryWhereItemModel>();

            QueryWhereItemModel whereItem = new QueryWhereItemModel()
            {
                FieldName = "FCRAFT_CODE",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = craft,
            };
            model.WhereGroup.Items.Add(whereItem);

            whereItem = new QueryWhereItemModel()
            {
                FieldName = "FCRAFT_NAME",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = craft,
            };
            model.WhereGroup.Items.Add(whereItem);

            return await rpcServer.QueryCraftIdsAsync(model);
        }

        /// <summary>
        /// 返回订单id列表
        /// </summary>
        /// <param name="material"></param>
        /// <returns></returns>
        private async Task<DataResult<List<string>>> RpcGetSaleOrderIdsAsync(string saleOrderNo)
        {
            ICOP001SaleOrderService rpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");

            QueryRequestModel model = new QueryRequestModel();
            model.WhereGroup = new QueryWhereGroupModel();
            model.WhereGroup.GroupType = EnumGroupType.OR;
            model.WhereGroup.Items = new List<QueryWhereItemModel>();

            QueryWhereItemModel whereItem = new QueryWhereItemModel()
            {
                FieldName = "FSALE_ORDER_NO",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = saleOrderNo,
            };
            model.WhereGroup.Items.Add(whereItem);

            return await rpcServer.QuerySaleOrderIdsAsync(model);
        }

        /// <summary>
        /// 返回物料id列表
        /// </summary>
        /// <param name="material"></param>
        /// <returns></returns>
        private async Task<DataResult<List<string>>> RpcGetMaterialIdsAsync(string material)
        {
            IMSD002MaterialService rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");

            QueryRequestModel model = new QueryRequestModel();
            model.WhereGroup = new QueryWhereGroupModel();
            model.WhereGroup.GroupType = EnumGroupType.OR;
            model.WhereGroup.Items = new List<QueryWhereItemModel>();

            QueryWhereItemModel whereItem = new QueryWhereItemModel()
            {
                FieldName = "FMATERIAL_CODE",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = material,
            };
            model.WhereGroup.Items.Add(whereItem);

            whereItem = new QueryWhereItemModel()
            {
                FieldName = "FMATERIAL_NAME",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = material,
            };
            model.WhereGroup.Items.Add(whereItem);

            return await rpcServer.GetMaterialIDSAsync(model);
        }


        /// <summary>
        /// 根据车间  返回工位IDS
        /// </summary>
        /// <param name="material"></param>
        /// <returns></returns>
        private async Task<DataResult<List<string>>> RpcGetStationIdsByWorkShopAsync(string workshop)
        {

            IMES001WorkShopService rpcServer = this.GetService<IMES001WorkShopService>("MES001WorkShop");
            QueryRequestModel model = new QueryRequestModel();
            model.WhereGroup = new QueryWhereGroupModel();
            model.WhereGroup.GroupType = EnumGroupType.OR;
            model.WhereGroup.Items = new List<QueryWhereItemModel>();

            QueryWhereItemModel whereItem = new QueryWhereItemModel()
            {
                FieldName = "FWORK_SHOP_NAME",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = workshop,
            };
            model.WhereGroup.Items.Add(whereItem);

            whereItem = new QueryWhereItemModel()
            {
                FieldName = "FWORK_SHOP_CODE",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = workshop,
            };
            model.WhereGroup.Items.Add(whereItem);

            return await rpcServer.GetStationIdsByWorkShopAsync(model);
        }


        /// <summary>
        /// 根据工位  返回工位IDS
        /// </summary>
        /// <param name="material"></param>
        /// <returns></returns>
        private async Task<DataResult<List<string>>> RpcGetStationIdsByProLineAsync(string proLine)
        {

            IMES001ProLineService rpcServer = this.GetService<IMES001ProLineService>("MES001ProLine");
            QueryRequestModel model = new QueryRequestModel();
            model.WhereGroup = new QueryWhereGroupModel();
            model.WhereGroup.GroupType = EnumGroupType.OR;
            model.WhereGroup.Items = new List<QueryWhereItemModel>();

            QueryWhereItemModel whereItem = new QueryWhereItemModel()
            {
                FieldName = "FPRO_LINE_CODE",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = proLine,
            };
            model.WhereGroup.Items.Add(whereItem);

            whereItem = new QueryWhereItemModel()
            {
                FieldName = "FPRO_LINE_NAME",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = proLine,
            };
            model.WhereGroup.Items.Add(whereItem);

            return await rpcServer.GetStationIdsByProLineAsync(model);
        }

        /// <summary>
        /// 返回物料信息
        /// </summary>
        /// <param name="materialIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleMaterialModel>>> RpcGetMaterialsAsync(List<string> materialIds)
        {
            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            var rpcrtn = await rpcServer.GetForBusinessByIdsAsync(materialIds);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取物料信息失败");
            }
            return rpcrtn;
        }

        /// <summary>
        /// 返回员工姓名
        /// </summary>
        /// <param name="empIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<EmployeeModel>>> RpcGetEmployeesAsync(List<string> empIds)
        {
            var rpcServer = this.GetService<IEmployeeService>("ADM024Employee");
            return await rpcServer.GetEmpsByIdsAsync(empIds);
        }

        /// <summary>
        /// 返回工艺信息
        /// </summary>
        /// <param name="craftIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleCraftModel>>> RpcGetCraftsAsync(List<string> craftIds)
        {
            var rpcServer = this.GetService<IMES002CraftService>("MES002Craft");
            return await rpcServer.GetForBusinessByIdsAsync(craftIds);
        }

        /// <summary>
        /// 返回工位信息
        /// </summary>
        /// <param name="craftIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<IModuleServices.MES001WorkArea.Models.SimpleStationModel>>> RpcGetStationsAsync(List<string> stationIds)
        {
            var rpcServer = this.GetService<IModuleServices.MES001WorkArea.IMES001StationService>("MES001Station");
            return await rpcServer.GetForBusinessByIdsAsync(stationIds);
        }

        /// <summary>
        /// 根据车间  返回工位IDS
        /// </summary>
        /// <param name="material"></param>
        /// <returns></returns>
        private async Task<DataResult<List<string>>> RpcGetStationIdsByStationAsync(string station)
        {

            IMES001StationService rpcServer = this.GetService<IMES001StationService>("MES001Station");
            QueryRequestModel model = new QueryRequestModel();
            model.WhereGroup = new QueryWhereGroupModel();
            model.WhereGroup.GroupType = EnumGroupType.OR;
            model.WhereGroup.Items = new List<QueryWhereItemModel>();

            QueryWhereItemModel whereItem = new QueryWhereItemModel()
            {
                FieldName = "FSTATION_NAME",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = station,
            };
            model.WhereGroup.Items.Add(whereItem);

            whereItem = new QueryWhereItemModel()
            {
                FieldName = "FSTATION_CODE",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = station,
            };
            model.WhereGroup.Items.Add(whereItem);

            return await rpcServer.GetStationIDSAsync(model);
        }

        /// <summary>
        /// 返回单位信息
        /// </summary>
        /// <param name="unitIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleUnitModel>>> RpcGetUnitsAsync(List<string> unitIds)
        {
            var rpcServer = this.GetService<IMSD001UnitService>("MSD001Unit");
            var rpcrtn = await rpcServer.GetByIdsForBusinessAsync(unitIds);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取单位信息失败");
            }
            return rpcrtn;
        }
        #endregion

        #region 引用检查
        /// <summary>
        /// 是否引用了指定的工单Id
        /// </summary>
        /// <param name="woIds"></param>
        /// <returns>返回已引用的工单Id列表</returns>
        public async Task<DataResult<List<string>>> IsReferenceWorkOrderAsync(List<string> woIds)
        {
            DataResult<List<string>> result = new DataResult<List<string>>();
            var db = _isugar.DB;

            var refers = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER>((sch, wo) => new JoinQueryInfos(JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID))
                .Where((sch, wo) => woIds.Contains(sch.FWORK_ORDER_ID))
                .Select((sch, wo) => new { sch.FWORK_ORDER_ID, wo.FWORK_ORDER_NO })
                .Distinct()
                .ToListAsync();

            if (refers.Count > 0)
            {
                result.StatusCode = 190001;
                result.Entity = refers.Select(p => p.FWORK_ORDER_ID).ToList();
                result.Message = string.Format(_multiLang["执行失败, 工单编号{0}已被工艺排程使用."], refers[0].FWORK_ORDER_NO);
            }
            else
            {
                result.StatusCode = 200;
            }
            return await OK(result);

        }
        #endregion


        /// <summary>
        /// 查询条件获取排程ids列表
        /// </summary>

        public async Task<DataResult<List<string>>> GetScheduleIDSAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;

            //转换查询条件
            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);


            List<string> daomodel = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().Where(wheres)
                .Select(p => p.FCRAFT_SCHEDULE_ID).ToListAsync();


            DataResult<List<string>> result = new DataResult<List<string>>()
            {
                Entity = daomodel,
                StatusCode = 200,
            };
            return await OK(result);
        }


        /// <summary>
        /// APP查询根据排程任务编号查询 查询进度
        /// </summary>
        public async Task<DataResult<ScheduleTaskProgressModel>> GetScheduleTaskProgressAsync(string scheduleNo)
        {
            var db = _isugar.DB;


            var daomodel = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FCRAFT_SCHEDULE_ID == b.FCRAFT_SCHEDULE_ID)).Where((a, b) => a.FCRAFT_SCHEDULE_NO == scheduleNo).Select<ScheduleTaskProgressModel>().FirstAsync();

            if (daomodel == null)
            {
                ERROR(daomodel, 111111, $"未查询到");
            }

            //取出产品信息
            var materialResult = await RpcGetMaterialsAsync(new List<string>() { daomodel.FMATERIAL_ID });

            //取出工单
            var workOrdResult = await RpcGetWorkOrdersAsync(new List<string>() { daomodel.FWORK_ORDER_ID });

            //取出单位信息
            var unitResult = await RpcGetUnitsAsync(workOrdResult.Entity.SelectMany(p =>
            {
                var ids = new List<string>();
                ids.Add(p.FUNIT_ID); ids.Add(p.FWEIGHT_UNIT_ID); return ids;
            }).ToList());

            //取出报工数据
            var jobBooking = await RpcGetJobBookingsAsync(daomodel.FCRAFT_SCHEDULE_ID);





            var mat = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == daomodel.FMATERIAL_ID);

            if (mat != null)
            {

                daomodel.FPIC_ATTACH_ID = mat.FPIC_ATTACH_ID;

                daomodel.FSPEC_DESC = mat.FSPEC_DESC;

                daomodel.FMATERIAL_NAME = mat.FMATERIAL_NAME;

                daomodel.FMATERIAL_CODE = mat.FMATERIAL_CODE;
            }




            var workOrd = workOrdResult.Entity.FirstOrDefault(P => P.FWORK_ORDER_ID == daomodel.FWORK_ORDER_ID);
            if (workOrd != null)
            {
                daomodel.FWORK_ORDER_NO = workOrd.FWORK_ORDER_NO;
            }


            var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrd.FUNIT_ID);

            if (unit != null)
            {
                daomodel.FUNIT_CODE = unit.FUNIT_CODE;
                daomodel.FUNIT_NAME = unit.FUNIT_NAME;
            }

            var weightUnit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrd.FWEIGHT_UNIT_ID);

            if (weightUnit != null)
            {
                daomodel.FWEIGHT_UNIT_CODE = weightUnit.FUNIT_CODE;
                daomodel.FWEIGHT_FUNIT_NAME = weightUnit.FUNIT_NAME;
            }

            var jobStatus = jobBooking.Entity.Select(p => p.FWORK_STATUS).ToList();
            if (jobStatus.Count > 0)
            {
                if (jobStatus.Contains("working"))
                {
                    daomodel.FWORK_STATUS = "working";
                }
                else if (jobStatus.Contains("paused"))
                {
                    daomodel.FWORK_STATUS = "paused";
                }
                else
                {
                    daomodel.FWORK_STATUS = "finished";
                }
            }
            else
            {
                daomodel.FWORK_STATUS = "unstart";
            }

            DataResult<ScheduleTaskProgressModel> result = new DataResult<ScheduleTaskProgressModel>()
            {
                Entity = daomodel,
                StatusCode = 200,
            };
            return await OK(result);
        }



        /// <summary>
        /// 返回生产工单信息
        /// </summary>

        private async Task<DataResult<List<SimpleWorkOrderModel>>> RpcGetWorkOrdersAsync(List<string> ids)
        {
            var rpcServer = this.GetService<IMES003WorkOrderService>("MES003WorkOrder");
            var rpcrtn = await rpcServer.GetWorkOrdersAsync(ids);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取生产工单信息失败");
            }
            return rpcrtn;
        }
        /// <summary>
        /// 返回工艺报工信息
        /// </summary>

        private async Task<DataResult<List<CraftJobBookingModel>>> RpcGetJobBookingsAsync(string id)
        {
            var rpcServer = this.GetService<IMES007JobBookingService>("MES007JobBooking");
            var rpcrtn = await rpcServer.GetJobBookingByAsync(id);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取工艺报工信息失败");
            }
            return rpcrtn;
        }


        /// <summary>
        /// 查询排程任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WOCraftScheduleModel>>> GetScheduleByIdsAsync(List<string> Ids)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();


            //查询任务
            var scheduleResult = await QueryTaskAsync(Ids);

            //result         
            DataResult<List<WOCraftScheduleModel>> result = new DataResult<List<WOCraftScheduleModel>>
            {
                Entity = scheduleResult.Item1,
                StatusCode = 200
            };
            return await OK(result);

        }



        private async Task<(List<WOCraftScheduleModel>, int)> QueryTaskAsync(List<string> Ids)
        {

            var db = _isugar.DB;

            //总行数
            RefAsync<int> totalSize = new RefAsync<int>();

            //查询
            var woCraftModels = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                                    T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>(
                                 (sch, schStatus, wo, woStatus, craft, craftStatus) =>
                                 new JoinQueryInfos(
                                 JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID,
                                 JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
             .Where((sch, schStatus, wo, woStatus, craft, craftStatus) => Ids.Contains(sch.FCRAFT_SCHEDULE_ID))
             .Select((sch, schStatus, wo, woStatus, craft, craftStatus) => new WOCraftScheduleModel()
             {
                 FECODE = sch.FECODE,

                 FFINISH_QTY = craftStatus.FFINISH_QTY,
                 FRECV_QTY = craftStatus.FRECV_QTY,

                 FIF_CLOSE = schStatus.FIF_CLOSE,
                 FLEVEL = wo.FLEVEL,

                 FLOT_NO = sch.FLOT_NO,
                 FMATERIAL_ID = sch.FMATERIAL_ID,

                 FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                 FPLAN_EMP_ID = sch.FPLAN_EMP_ID,

                 FPRO_QTY = wo.FPRO_QTY,
                 FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                 FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                 FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                 FWORK_ORDER_ID = sch.FWORK_ORDER_ID,
                 FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                 FCRAFT_ID = sch.FCRAFT_ID,
                 FGEN_RATE = craft.FGEN_RATE,

                 FPROCESS_FEE = craft.FPROCESS_FEE,
                 FSHOW_SEQNO = craft.FSHOW_SEQNO,

                 FSTATION_ID = sch.FSTATION_ID,
                 FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                 FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                 FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY) < 0 ? 0 : (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                 FPLAN_QTY = sch.FPLAN_QTY,
                 FORI_PLAN_QTY = sch.FORI_PLAN_QTY,

                 FPLAN_ED_DATE = sch.FPLAN_ED_DATE,
                 FPLAN_ST_DATE = sch.FPLAN_ST_DATE,

                 FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,

                 FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                 FQRCODE = sch.FQRCODE,

                 FPLAN_USE_HOUR = sch.FPLAN_USE_HOUR,

                 FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                 FSCHEDULE_BATCH_ID = sch.FSCHEDULE_BATCH_ID,

                 FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,

                 FCLOSEDATE = schStatus.FCLOSEDATE,
                 FCLOSER = schStatus.FCLOSER,
                 FCLOSER_ID = schStatus.FCLOSER_ID,

                 FRELEASER = schStatus.FRELEASER,
                 FRELEASER_ID = schStatus.FRELEASER_ID,
                 FRELEASE_DATE = schStatus.FRELEASE_DATE,

                 FRELEASE_QTY = craftStatus.FRELEASE_QTY,

                 FCREATOR = sch.FCREATOR,
                 FCREATOR_ID = sch.FCREATOR_ID,
                 FCDATE = sch.FCDATE,

                 FMODIDATE = sch.FMODIDATE,
                 FMODIFIER = sch.FMODIFIER,
                 FMODIFIER_ID = sch.FMODIFIER_ID,

                 FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,   //排程任务的完工数量
                 FPASS_QTY_SCHEDULE = schStatus.FPASS_QTY,   //排程任务的合格数
                 FNG_QTY_SCHEDULE = schStatus.FNG_QTY,    //排程任务的不良数

                 FACT_ST_DATE = schStatus.FACT_ST_DATE,   //排程任务的实际开工
                 FACT_ED_DATE = schStatus.FACT_ED_DATE,   //排程任务的实际完工
                 FACT_USE_HOUR = schStatus.FACT_USE_HOUR,  //排程任务的实际工时

                 FPLAN_WEIGHT = sch.FPLAN_WEIGHT,   //计划产出重量
                 FWEIGHT_UNIT_ID = sch.FWEIGHT_UNIT_ID,  //重量单位
                 FUNIT_WEIGHT = sch.FUNIT_WEIGHT,  //单位重量

                 FFINISH_WEIGHT = schStatus.FFINISH_WEIGHT,
                 FPASS_WEIGHT = schStatus.FPASS_WEIGHT,
                 FNG_WEIGHT = schStatus.FNG_WEIGHT,

                 FPRO_WEIGHT = wo.FPRO_WEIGHT,

                 FIS_OUT = schStatus.FIS_OUT,
                 FBOOKING_TYPE = wo.FBOOKING_TYPE,
             })
             .OrderBy((sch) => sch.FCRAFT_SCHEDULE_NO, OrderByType.Desc)
             .ToListAsync();

            if (woCraftModels.Count > 0)
            {
                List<string> materialIds1 = woCraftModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                //取出物料信息
                DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(materialIds1);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }


                //取计划员姓名
                var empIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                DataResult<List<EmployeeModel>> empResult = null;
                if (empIds.Count > 0)
                {
                    empResult = await RpcGetEmployeesAsync(empIds);
                    if (empResult.StatusCode != 200)
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }

                //取出生产单位
                var unitIds = woCraftModels.SelectMany(p => { var ids = new List<string>() { p.FPRO_UNIT_ID, p.FWEIGHT_UNIT_ID }; return ids; }).Distinct().ToList();
                DataResult<List<SimpleUnitModel>> unitResult = null;
                if (unitIds.Count > 0)
                {
                    unitResult = await RpcGetUnitsAsync(unitIds);
                    if (unitResult.StatusCode != 200)
                    {
                        ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                    }
                }

                //取出工艺信息
                var craftIds1 = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                DataResult<List<SimpleCraftModel>> craftResult = null;
                if (craftIds1.Count > 0)
                {
                    craftResult = await RpcGetCraftsAsync(craftIds1);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }
                }


                //取出工位信息
                var stationIds1 = woCraftModels.Select(p => p.FSTATION_ID).Distinct().ToList();
                DataResult<List<IModuleServices.MES001WorkArea.Models.SimpleStationModel>> stationResult = null;
                if (stationIds1.Count > 0)
                {
                    stationResult = await RpcGetStationsAsync(stationIds1);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                }

                //取出订单编号
                var saleOrderIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID)).Select(p => p.FSALE_ORDER_ID).ToList();
                DataResult<List<SaleOrderSimpleModel>> soResult = null;
                if (saleOrderIds.Count > 0)
                {
                    soResult = await GetSaleOrdersAsync(saleOrderIds);
                    if (soResult.StatusCode != 200)
                    {
                        ERROR(soResult, soResult.StatusCode, soResult.Message);
                    }
                }

                //获取委外申请单已转数量
                var outApplys = await (GetService<IPUR013OutApplyService>("PUR013OutApply")).GetByIdsAsync(Ids);


                //合并到一处循环
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                {
                    //物料
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == woCraft.FMATERIAL_ID);
                    if (material != null)
                    {
                        woCraft.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        woCraft.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        woCraft.FSPEC_DESC = material.FSPEC_DESC;
                        woCraft.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                        woCraft.FGOODS_MODEL = material.FGOODS_MODEL;
                    }

                    //已转申请单数量
                    var outApply = outApplys.Entity.FirstOrDefault(p => p.WoMatId == woCraft.FCRAFT_SCHEDULE_ID);
                    if (outApply != null)
                    {
                        woCraft.OutApplyCount = outApply.count;
                    }

                    //计划员
                    if (empResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FPLAN_EMP_ID))
                        {
                            var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == woCraft.FPLAN_EMP_ID);
                            if (emp != null)
                            {
                                woCraft.FPLAN_EMP_NAME = emp.FEMP_NAME;
                            }
                        }
                    }

                    //单位
                    if (unitResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FPRO_UNIT_ID))
                        {
                            var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == woCraft.FPRO_UNIT_ID);
                            if (unit != null)
                            {
                                woCraft.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                woCraft.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                            }
                        }

                        if (!string.IsNullOrWhiteSpace(woCraft.FWEIGHT_UNIT_ID))
                        {
                            var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == woCraft.FWEIGHT_UNIT_ID);
                            if (unit != null)
                            {
                                woCraft.FWEIGHT_UNIT_CODE = unit.FUNIT_CODE;
                                woCraft.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                            }
                        }
                    }

                    //工艺
                    if (craftResult != null)
                    {
                        var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                        if (craft != null)
                        {
                            woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                            woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                            woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                        }
                    }

                    //工位
                    if (stationResult != null)
                    {
                        var station = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == woCraft.FSTATION_ID);
                        if (station != null)
                        {
                            woCraft.FSTATION_CODE = station.FSTATION_CODE;
                            woCraft.FSTATION_NAME = station.FSTATION_NAME;
                            woCraft.FSTATION_PROPERTY = station.FSTATION_PROPERTY;
                        }
                    }

                    //订单
                    if (soResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FSALE_ORDER_ID))
                        {
                            var so = soResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == woCraft.FSALE_ORDER_ID);
                            if (so != null)
                            {
                                woCraft.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                            }
                        }

                    }

                });

                //取出投入物料
                await GetSubMaterialAsync(woCraftModels);

                //取出执行人
                await GetScheduleEmpsAsync(woCraftModels);



                //获取排程的加工状态
                await GetScheduleStatusAsync(woCraftModels);
            }

            //返回结果
            return (woCraftModels, totalSize.Value);
        }


        /// <summary>
        /// 查询任务
        /// </summary>
        /// <returns></returns>
        public async Task<DataResult<List<WOCraftScheduleModel>>> QueryScheduleOutTaskAsync(List<string> Ids)
        {
            var db = _isugar.DB;

            //查询
            var woCraftModels = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                                    T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>(
                                 (sch, schStatus, wo, woStatus, craft, craftStatus) =>
                                 new JoinQueryInfos(
                                 JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID,
                                 JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
             .Where((sch, schStatus, wo, woStatus, craft, craftStatus) => Ids.Contains(sch.FCRAFT_SCHEDULE_ID))
                 .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => wo.FWORK_ORDER_NO, OrderByType.Desc)
             .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => craft.FSHOW_SEQNO, OrderByType.Asc)
             .Select((sch, schStatus, wo, woStatus, craft, craftStatus) => new WOCraftScheduleModel()
             {
                 FECODE = sch.FECODE,

                 FFINISH_QTY = craftStatus.FFINISH_QTY,
                 FRECV_QTY = craftStatus.FRECV_QTY,

                 FIF_CLOSE = schStatus.FIF_CLOSE,
                 FLEVEL = wo.FLEVEL,

                 FLOT_NO = sch.FLOT_NO,
                 FMATERIAL_ID = sch.FMATERIAL_ID,

                 FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                 FPLAN_EMP_ID = sch.FPLAN_EMP_ID,

                 FPRO_QTY = wo.FPRO_QTY,
                 FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                 FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                 FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                 FWORK_ORDER_ID = sch.FWORK_ORDER_ID,
                 FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                 FCRAFT_ID = sch.FCRAFT_ID,
                 FGEN_RATE = craft.FGEN_RATE,

                 FPROCESS_FEE = craft.FPROCESS_FEE,
                 FSHOW_SEQNO = craft.FSHOW_SEQNO,

                 FSTATION_ID = sch.FSTATION_ID,
                 FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                 FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                 FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY) < 0 ? 0 : (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                 FPLAN_QTY = sch.FPLAN_QTY,
                 FORI_PLAN_QTY = sch.FORI_PLAN_QTY,

                 FPLAN_ED_DATE = sch.FPLAN_ED_DATE,
                 FPLAN_ST_DATE = sch.FPLAN_ST_DATE,

                 FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,

                 FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                 FQRCODE = sch.FQRCODE,

                 FPLAN_USE_HOUR = sch.FPLAN_USE_HOUR,

                 FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                 FSCHEDULE_BATCH_ID = sch.FSCHEDULE_BATCH_ID,

                 FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,

                 FCLOSEDATE = schStatus.FCLOSEDATE,
                 FCLOSER = schStatus.FCLOSER,
                 FCLOSER_ID = schStatus.FCLOSER_ID,

                 FRELEASER = schStatus.FRELEASER,
                 FRELEASER_ID = schStatus.FRELEASER_ID,
                 FRELEASE_DATE = schStatus.FRELEASE_DATE,

                 FRELEASE_QTY = craftStatus.FRELEASE_QTY,

                 FCREATOR = sch.FCREATOR,
                 FCREATOR_ID = sch.FCREATOR_ID,
                 FCDATE = sch.FCDATE,

                 FMODIDATE = sch.FMODIDATE,
                 FMODIFIER = sch.FMODIFIER,
                 FMODIFIER_ID = sch.FMODIFIER_ID,

                 FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,   //排程任务的完工数量
                 FPASS_QTY_SCHEDULE = schStatus.FPASS_QTY,   //排程任务的合格数
                 FNG_QTY_SCHEDULE = schStatus.FNG_QTY,    //排程任务的不良数

                 FACT_ST_DATE = schStatus.FACT_ST_DATE,   //排程任务的实际开工
                 FACT_ED_DATE = schStatus.FACT_ED_DATE,   //排程任务的实际完工
                 FACT_USE_HOUR = schStatus.FACT_USE_HOUR,  //排程任务的实际工时

                 FPLAN_WEIGHT = sch.FPLAN_WEIGHT,   //计划产出重量
                 FWEIGHT_UNIT_ID = sch.FWEIGHT_UNIT_ID,  //重量单位
                 FUNIT_WEIGHT = sch.FUNIT_WEIGHT,  //单位重量

                 FFINISH_WEIGHT = schStatus.FFINISH_WEIGHT,
                 FPASS_WEIGHT = schStatus.FPASS_WEIGHT,
                 FNG_WEIGHT = schStatus.FNG_WEIGHT,

                 FPRO_WEIGHT = wo.FPRO_WEIGHT,

                 FIS_OUT = schStatus.FIS_OUT,
                 FBOOKING_TYPE = wo.FBOOKING_TYPE,
             })
             //.OrderBy((sch) => sch.FCRAFT_SCHEDULE_NO, OrderByType.Desc)
             .ToListAsync();

            if (woCraftModels.Count > 0)
            {
                List<string> materialIds1 = woCraftModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                //取出物料信息
                var materialResult = await RpcGetMaterialsAsync(materialIds1);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }
                //取计划员姓名
                var empIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                DataResult<List<EmployeeModel>> empResult = null;
                if (empIds.Count > 0)
                {
                    empResult = await RpcGetEmployeesAsync(empIds);
                    if (empResult.StatusCode != 200)
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }
                //取出生产单位
                var unitIds = woCraftModels.SelectMany(p => { var ids = new List<string>() { p.FPRO_UNIT_ID, p.FWEIGHT_UNIT_ID }; return ids; }).Distinct().ToList();
                DataResult<List<SimpleUnitModel>> unitResult = null;
                if (unitIds.Count > 0)
                {
                    unitResult = await RpcGetUnitsAsync(unitIds);
                    if (unitResult.StatusCode != 200)
                    {
                        ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                    }
                }

                //取出工艺信息
                var craftIds1 = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                DataResult<List<SimpleCraftModel>> craftResult = null;
                if (craftIds1.Count > 0)
                {
                    craftResult = await RpcGetCraftsAsync(craftIds1);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }
                }
                //取出工位信息
                var stationIds1 = woCraftModels.Select(p => p.FSTATION_ID).Distinct().ToList();
                DataResult<List<IModuleServices.MES001WorkArea.Models.SimpleStationModel>> stationResult = null;
                if (stationIds1.Count > 0)
                {
                    stationResult = await RpcGetStationsAsync(stationIds1);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                }
                //取出订单编号
                var saleOrderIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID)).Select(p => p.FSALE_ORDER_ID).ToList();
                DataResult<List<SaleOrderSimpleModel>> soResult = null;
                if (saleOrderIds.Count > 0)
                {
                    soResult = await GetSaleOrdersAsync(saleOrderIds);
                    if (soResult.StatusCode != 200)
                    {
                        ERROR(soResult, soResult.StatusCode, soResult.Message);
                    }
                }
                //获取委外申请单已转数量
                List<string> outApplyIds = woCraftModels.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
                var outApplys = await (GetService<IPUR013OutApplyService>("PUR013OutApply")).GetByIdsAsync(outApplyIds);

                //合并到一处循环
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                {
                    //物料
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == woCraft.FMATERIAL_ID);
                    if (material != null)
                    {
                        woCraft.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        woCraft.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        woCraft.FSPEC_DESC = material.FSPEC_DESC;
                        woCraft.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                        woCraft.FGOODS_MODEL = material.FGOODS_MODEL;
                    }

                    //已转申请单数量
                    var outApply = outApplys.Entity.FirstOrDefault(p => p.WoMatId == woCraft.FCRAFT_SCHEDULE_ID);
                    if (outApply != null)
                    {
                        woCraft.OutApplyCount = outApply.count;
                    }

                    //计划员
                    if (empResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FPLAN_EMP_ID))
                        {
                            var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == woCraft.FPLAN_EMP_ID);
                            if (emp != null)
                            {
                                woCraft.FPLAN_EMP_NAME = emp.FEMP_NAME;
                            }
                        }
                    }

                    //单位
                    if (unitResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FPRO_UNIT_ID))
                        {
                            var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == woCraft.FPRO_UNIT_ID);
                            if (unit != null)
                            {
                                woCraft.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                woCraft.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                            }
                        }

                        if (!string.IsNullOrWhiteSpace(woCraft.FWEIGHT_UNIT_ID))
                        {
                            var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == woCraft.FWEIGHT_UNIT_ID);
                            if (unit != null)
                            {
                                woCraft.FWEIGHT_UNIT_CODE = unit.FUNIT_CODE;
                                woCraft.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                            }
                        }
                    }

                    //工艺
                    if (craftResult != null)
                    {
                        var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                        if (craft != null)
                        {
                            woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                            woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                            woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                        }
                    }

                    //工位
                    if (stationResult != null)
                    {
                        var station = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == woCraft.FSTATION_ID);
                        if (station != null)
                        {
                            woCraft.FSTATION_CODE = station.FSTATION_CODE;
                            woCraft.FSTATION_NAME = station.FSTATION_NAME;
                            woCraft.FSTATION_PROPERTY = station.FSTATION_PROPERTY;
                        }
                    }

                    //订单
                    if (soResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FSALE_ORDER_ID))
                        {
                            var so = soResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == woCraft.FSALE_ORDER_ID);
                            if (so != null)
                            {
                                woCraft.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                            }
                        }

                    }

                });
            }

            //返回结果
            DataResult<List<WOCraftScheduleModel>> dataResult = new DataResult<List<WOCraftScheduleModel>>
            {
                StatusCode = 200,
                Entity = woCraftModels,
            };

            return await OK(dataResult);
        }



        #region 领料获取排程, 排除自动结案或手动结案排程



        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WORecCraftScheduleModel>>> QueryScheduleTaskByRecAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            string orderByString = null;
            if (model.Orders != null && model.Orders.Any())
            {
                orderByString = string.Join(",",
                    model.Orders.Select(x => $"{x.Fields[0]} {x.OrderType}"));
            }

            #region 查出工艺id列表
            string craft = string.Empty;
            List<string> craftIds = new List<string>();
            var craftItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FCRAFT");
            if (craftItem != null)
            {
                model.WhereGroup.Items.Remove(craftItem);
                if (!string.IsNullOrWhiteSpace(craftItem.Value))
                {
                    craft = craftItem.Value;
                    var craftResult = await RpcGetCraftIdsAsync(craft);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }
                    craftIds = craftResult.Entity;
                }
            }
            #endregion

            #region 查出工位id列表
            string station = string.Empty;
            List<string> stationIds = new List<string>();
            var stationItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FSTATION_ID");
            if (stationItem != null)
            {
                stationIds.Add(stationItem.Value);
            }
            #endregion

            #region 查出订单id列表
            string saleOrdNo = string.Empty;
            List<string> saleOrdIds = new List<string>();
            var saleOrdItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FSALE_ORDER_NO");
            if (saleOrdItem != null)
            {
                model.WhereGroup.Items.Remove(saleOrdItem);
                if (!string.IsNullOrWhiteSpace(saleOrdItem.Value))
                {


                    saleOrdNo = saleOrdItem.Value;
                    var saleIdsRPC = await GetService<ICOP001SaleOrderService>("COP001SaleOrder").GetSaleOrdIdsByCodeAsync(saleOrdNo);
                    saleOrdIds = saleIdsRPC.Entity;
                }
            }
            #endregion

            #region 查出产品id列表
            string material = string.Empty;
            List<string> materialIds = new List<string>();
            var materialItem = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FMATERIAL");
            if (materialItem != null)
            {
                model.WhereGroup.Items.Remove(materialItem);
                if (!string.IsNullOrWhiteSpace(materialItem.Value))
                {
                    material = materialItem.Value;
                    var materialResult = await RpcGetMaterialIdsAsync(material);
                    if (materialResult.StatusCode != 200)
                    {
                        ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                    }
                    materialIds = materialResult.Entity;
                }
            }
            #endregion

            #region 根据车间查出工位id列表
            string workShopName = string.Empty;
            List<string> workShopStationIds = new List<string>();
            var workShopQuery = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FWORKSHOP");
            if (workShopQuery != null)
            {
                model.WhereGroup.Items.Remove(workShopQuery);
                if (!string.IsNullOrWhiteSpace(workShopQuery.Value))
                {
                    workShopName = workShopQuery.Value;
                    var stationResult = await RpcGetStationIdsByWorkShopAsync(workShopName);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                    workShopStationIds = stationResult.Entity;
                }
            }
            #endregion

            #region 根据产线查出工位id列表
            string proLineName = string.Empty;
            List<string> proLineStationIds = new List<string>();
            var proLineQuery = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "PROLINE");
            if (proLineQuery != null)
            {
                model.WhereGroup.Items.Remove(proLineQuery);
                if (!string.IsNullOrWhiteSpace(proLineQuery.Value))
                {
                    proLineName = proLineQuery.Value;
                    var stationResult = await RpcGetStationIdsByProLineAsync(proLineName);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                    proLineStationIds = stationResult.Entity;
                }
            }
            #endregion

            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);

            //总行数
            RefAsync<int> totalSize = new RefAsync<int>();

            //查询
            var woCraftModels = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                                    T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>(
                                 (sch, schStatus, wo, woStatus, craft, craftStatus) =>
                                 new JoinQueryInfos(
                                 JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID,
                                 JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
                .WhereIF(!string.IsNullOrWhiteSpace(craft), (sch, schStatus, wo, woStatus, craft, craftStatus) => craftIds.Contains(sch.FCRAFT_ID))
                .WhereIF(!string.IsNullOrWhiteSpace(saleOrdNo), (sch, schStatus, wo, woStatus, craft, craftStatus) => saleOrdIds.Contains(sch.FSALE_ORDER_ID))
                .WhereIF(!string.IsNullOrWhiteSpace(material), (sch, schStatus, wo, woStatus, craft, craftStatus) => materialIds.Contains(sch.FMATERIAL_ID))
                .WhereIF(!string.IsNullOrWhiteSpace(workShopName), (sch, schStatus, wo, woStatus, craft, craftStatus) => workShopStationIds.Contains(sch.FSTATION_ID))
                .WhereIF(!string.IsNullOrWhiteSpace(proLineName), (sch, schStatus, wo, woStatus, craft, craftStatus) => proLineStationIds.Contains(sch.FSTATION_ID))
                .WhereIF(!string.IsNullOrWhiteSpace(station), (sch, schStatus, wo, woStatus, craft, craftStatus) => stationIds.Contains(sch.FSTATION_ID))
                .Where((sch, schStatus, wo, woStatus, craft, craftStatus) => (schStatus.FIF_CLOSE == 0 || schStatus.FIF_CLOSE == 2) && schStatus.FRELEASE_STATUS == true)
                 .OrderByIF(orderByString != null, orderByString)
                 .OrderByIF(orderByString == null, (sch, schStatus, wo, woStatus, craft, craftStatus) => sch.SCHEDULE_FSHOW_SEQNO, OrderByType.Desc)
                 .OrderByIF(orderByString == null, (sch, schStatus, wo, woStatus, craft, craftStatus) => wo.FWORK_ORDER_NO, OrderByType.Desc)
                 .OrderByIF(orderByString == null, (sch, schStatus, wo, woStatus, craft, craftStatus) => craft.FSHOW_SEQNO, OrderByType.Asc)
                .Select((sch, schStatus, wo, woStatus, craft, craftStatus) => new WORecCraftScheduleModel()
                {
                    FECODE = sch.FECODE,

                    FFINISH_QTY = craftStatus.FFINISH_QTY,
                    FRECV_QTY = craftStatus.FRECV_QTY,

                    FIF_CLOSE = schStatus.FIF_CLOSE,
                    FLEVEL = wo.FLEVEL,

                    FLOT_NO = sch.FLOT_NO,
                    FMATERIAL_ID = sch.FMATERIAL_ID,

                    FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                    FPLAN_EMP_ID = sch.FPLAN_EMP_ID,

                    FPRO_QTY = wo.FPRO_QTY,
                    FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                    FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                    FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                    FWORK_ORDER_ID = sch.FWORK_ORDER_ID,
                    FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                    FCRAFT_ID = sch.FCRAFT_ID,
                    FGEN_RATE = craft.FGEN_RATE,

                    FPROCESS_FEE = craft.FPROCESS_FEE,
                    FSHOW_SEQNO = craft.FSHOW_SEQNO,

                    FSTATION_ID = sch.FSTATION_ID,
                    FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                    FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                    FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY) < 0 ? 0 : (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                    FPLAN_QTY = sch.FPLAN_QTY,
                    FORI_PLAN_QTY = sch.FORI_PLAN_QTY,

                    FPLAN_ED_DATE = sch.FPLAN_ED_DATE,
                    FPLAN_ST_DATE = sch.FPLAN_ST_DATE,

                    FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,

                    FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                    FQRCODE = sch.FQRCODE,

                    FPLAN_USE_HOUR = sch.FPLAN_USE_HOUR,

                    FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                    FSCHEDULE_BATCH_ID = sch.FSCHEDULE_BATCH_ID,

                    FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,

                    FCLOSEDATE = schStatus.FCLOSEDATE,
                    FCLOSER = schStatus.FCLOSER,
                    FCLOSER_ID = schStatus.FCLOSER_ID,

                    FRELEASER = schStatus.FRELEASER,
                    FRELEASER_ID = schStatus.FRELEASER_ID,
                    FRELEASE_DATE = schStatus.FRELEASE_DATE,

                    FRELEASE_QTY = craftStatus.FRELEASE_QTY,

                    FCREATOR = sch.FCREATOR,
                    FCREATOR_ID = sch.FCREATOR_ID,
                    FCDATE = sch.FCDATE,

                    FMODIDATE = sch.FMODIDATE,
                    FMODIFIER = sch.FMODIFIER,
                    FMODIFIER_ID = sch.FMODIFIER_ID,

                    FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,   //排程任务的完工数量
                    FPASS_QTY_SCHEDULE = schStatus.FPASS_QTY,   //排程任务的合格数
                    FNG_QTY_SCHEDULE = schStatus.FNG_QTY,    //排程任务的不良数

                    FACT_ST_DATE = schStatus.FACT_ST_DATE,   //排程任务的实际开工
                    FACT_ED_DATE = schStatus.FACT_ED_DATE,   //排程任务的实际完工
                    FACT_USE_HOUR = schStatus.FACT_USE_HOUR,  //排程任务的实际工时

                    FPLAN_WEIGHT = sch.FPLAN_WEIGHT,   //计划产出重量
                    FWEIGHT_UNIT_ID = sch.FWEIGHT_UNIT_ID,  //重量单位
                    FUNIT_WEIGHT = sch.FUNIT_WEIGHT,  //单位重量

                    FFINISH_WEIGHT = schStatus.FFINISH_WEIGHT,
                    FPASS_WEIGHT = schStatus.FPASS_WEIGHT,
                    FNG_WEIGHT = schStatus.FNG_WEIGHT,

                    FPRO_WEIGHT = wo.FPRO_WEIGHT,

                    FIS_OUT = schStatus.FIS_OUT,
                    FBOOKING_TYPE = wo.FBOOKING_TYPE,
                    SCHEDULE_FSHOW_SEQNO = sch.SCHEDULE_FSHOW_SEQNO,

                    FIUIU_CUST = wo.FIUIU_CUST,
                    FIUIU_ORDER_DATE = wo.FIUIU_ORDER_DATE,
                    FIUIU_ORDER_NO = wo.FIUIU_ORDER_NO,
                    FIUIU_SALE_NAME = wo.FIUIU_SALE_NAME

                })

                 .Where(wheres)
                 .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            var woIds = woCraftModels.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();

            // 排程按工艺取出, 对应的工单物料表工艺投料信息

            var woRecMatModels = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL, T_MESD_WORK_ORDER_MATERIAL_STATUS>
                ((im, ims) => new JoinQueryInfos(JoinType.Left, im.FWORK_ORDER_MATERIAL_ID == ims.FWORK_ORDER_MATERIAL_ID))
                .Where((im, ims) => woIds.Contains(im.FWORK_ORDER_ID))
                .Select<WORecMatModel>()
                .ToListAsync();

            var rawPickedMaterials = await db.Queryable<T_MESD_WORK_REC_MAT, T_MESD_WORK_REC_STATUS>(
                (recMat, recStatus) => recMat.FWORK_REC_ID == recStatus.FWORK_REC_ID)
                .Where((recMat, recStatus) => woIds.Contains(recMat.FSOURCE_ID) && recStatus.FIF_CANCEL == false)
                .Select(recMat => new
                {
                    recMat.FSOURCE_IM_ID,
                    recMat.FQTY
                })
                .ToListAsync();

            var allPickedMaterials = rawPickedMaterials
                .GroupBy(item => item.FSOURCE_IM_ID)
                .Select(g => new
                {
                    WorkOrderMaterialId = g.Key,
                    TotalPickedQty = g.Sum(item => item.FQTY)
                })
                .ToList();

            if (woCraftModels.Count > 0)
            {
                List<string> materialIds1 = woCraftModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                List<string> materialIds2 = woRecMatModels.Select(p => p.FSUB_MATERIAL_ID).Distinct().ToList();
                List<string> allMaterialIds = materialIds1.Union(materialIds2).Distinct().ToList();

                //取出物料信息
                DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(allMaterialIds);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }


                //取计划员姓名
                var empIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID))
                    .Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                DataResult<List<EmployeeModel>> empResult = null;
                if (empIds.Count > 0)
                {
                    empResult = await RpcGetEmployeesAsync(empIds);
                    if (empResult.StatusCode != 200)
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }

                var allUnitIds = woCraftModels.SelectMany(p => new[] { p.FPRO_UNIT_ID, p.FWEIGHT_UNIT_ID })
                    .Union(woRecMatModels.SelectMany(p => new[] { p.FSUB_UNIT_ID, p.FSUB_STK_UNIT_ID }))
                    .Where(id => !string.IsNullOrEmpty(id))
                    .Distinct()
                    .ToList();

                DataResult<List<SimpleUnitModel>> unitResult = null;
                if (allUnitIds.Count > 0)
                {
                    unitResult = await RpcGetUnitsAsync(allUnitIds);
                    if (unitResult.StatusCode != 200)
                    {
                        ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                    }
                }

                //取出工艺信息
                var craftIds1 = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                DataResult<List<SimpleCraftModel>> craftResult = null;
                if (craftIds1.Count > 0)
                {
                    craftResult = await RpcGetCraftsAsync(craftIds1);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }
                }


                //取出工位信息
                var stationIds1 = woCraftModels.Select(p => p.FSTATION_ID).Distinct().ToList();
                DataResult<List<IModuleServices.MES001WorkArea.Models.SimpleStationModel>> stationResult = null;
                if (stationIds1.Count > 0)
                {
                    stationResult = await RpcGetStationsAsync(stationIds1);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                }

                //取出订单编号
                var saleOrderIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID)).Select(p => p.FSALE_ORDER_ID).ToList();
                DataResult<List<SaleOrderSimpleModel>> soResult = null;
                if (saleOrderIds.Count > 0)
                {
                    soResult = await GetSaleOrdersAsync(saleOrderIds);
                    if (soResult.StatusCode != 200)
                    {
                        ERROR(soResult, soResult.StatusCode, soResult.Message);
                    }
                }

                //获取委外申请单已转数量
                List<string> outApplyIds = woCraftModels.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
                var outApplys = await (GetService<IPUR013OutApplyService>("PUR013OutApply")).GetByIdsAsync(outApplyIds);

                woRecMatModels.AsParallel().WithDegreeOfParallelism(4).ForAll(mat =>
                {
                    //物料
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == mat.FSUB_MATERIAL_ID);
                    if (material != null)
                    {
                        mat.FSUB_MATERIAL_CODE = material.FMATERIAL_CODE;
                        mat.FSUB_MATERIAL_NAME = material.FMATERIAL_NAME;
                        mat.FSUB_SPEC_DESC = material.FSPEC_DESC;
                        mat.FSTORE_ID = material.FSTORE_ID; // 添加物料的库存ID    
                        mat.FSTORE_PLACE_ID = material.FSTORE_PLACE_ID; // 添加物料的库位ID
                    }

                    // 单位
                    if (unitResult != null)
                    {
                        // 计量单位
                        var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == mat.FSUB_UNIT_ID);
                        if (unit != null)
                        {
                            mat.FSUB_UNIT_CODE = unit.FUNIT_CODE;
                            mat.FSUB_UNIT_NAME = unit.FUNIT_NAME;
                        }
                        // 库存单位
                        unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == mat.FSUB_STK_UNIT_ID);
                        if (unit != null)
                        {
                            mat.FSUB_STK_UNIT_CODE = unit.FUNIT_CODE;
                            mat.FSUB_STK_UNIT_NAME = unit.FUNIT_NAME;
                        }
                    }
                });

                //合并到一处循环
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                {
                    //物料
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == woCraft.FMATERIAL_ID);
                    if (material != null)
                    {
                        woCraft.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        woCraft.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        woCraft.FSPEC_DESC = material.FSPEC_DESC;
                        woCraft.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                        woCraft.FGOODS_MODEL = material.FGOODS_MODEL;
                    }

                    //已转申请单数量
                    var outApply = outApplys.Entity.FirstOrDefault(p => p.WoMatId == woCraft.FCRAFT_SCHEDULE_ID);
                    if (outApply != null)
                    {
                        woCraft.OutApplyCount = outApply.count;
                    }

                    //计划员
                    if (empResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FPLAN_EMP_ID))
                        {
                            var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == woCraft.FPLAN_EMP_ID);
                            if (emp != null)
                            {
                                woCraft.FPLAN_EMP_NAME = emp.FEMP_NAME;
                            }
                        }
                    }

                    //单位
                    if (unitResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FPRO_UNIT_ID))
                        {
                            var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == woCraft.FPRO_UNIT_ID);
                            if (unit != null)
                            {
                                woCraft.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                woCraft.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                            }
                        }

                        if (!string.IsNullOrWhiteSpace(woCraft.FWEIGHT_UNIT_ID))
                        {
                            var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == woCraft.FWEIGHT_UNIT_ID);
                            if (unit != null)
                            {
                                woCraft.FWEIGHT_UNIT_CODE = unit.FUNIT_CODE;
                                woCraft.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                            }
                        }
                    }

                    //工艺
                    if (craftResult != null)
                    {
                        var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                        if (craft != null)
                        {
                            woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                            woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                            woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                        }
                    }

                    //工位
                    if (stationResult != null)
                    {
                        var station = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == woCraft.FSTATION_ID);
                        if (station != null)
                        {
                            woCraft.FSTATION_CODE = station.FSTATION_CODE;
                            woCraft.FSTATION_NAME = station.FSTATION_NAME;
                            woCraft.FSTATION_PROPERTY = station.FSTATION_PROPERTY;
                        }
                    }

                    //订单
                    if (soResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(woCraft.FSALE_ORDER_ID))
                        {
                            var so = soResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == woCraft.FSALE_ORDER_ID);
                            if (so != null)
                            {
                                woCraft.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                            }
                        }

                    }

                    // 给入当前排程的投料物料
                    var submats = woRecMatModels.Where(p => p.FWORK_ORDER_CRAFT_ID == woCraft.FWORK_ORDER_CRAFT_ID && p.FWORK_ORDER_ID == woCraft.FWORK_ORDER_ID).ToList();
                    if (submats != null && submats.Count > 0)
                    {
                        woCraft.woRecMats = submats;
                    }
                    if (woCraft.woRecMats != null && woCraft.woRecMats.Any())
                    {
                        woCraft.woRecMats.ForEach(mat =>
                        {
                            decimal lossRate = mat.FLOSS_RATE;
                            decimal totalRequiredForSchedule = (mat.FUNIT_QTY * woCraft.FPLAN_QTY) * (1 + lossRate / 100);
                            mat.FUSE_QTY = totalRequiredForSchedule; // 理论需求量

                            var pickedInfo = allPickedMaterials.FirstOrDefault(p => p.WorkOrderMaterialId == mat.FWORK_ORDER_MATERIAL_ID);
                            decimal totalPickedQty = pickedInfo?.TotalPickedQty ?? 0;
                            mat.FFETCH_QTY = totalPickedQty; // 已领用数量

                            mat.FCAN_FETCH_QTY = totalRequiredForSchedule - totalPickedQty;
                            if (mat.FCAN_FETCH_QTY < 0)
                            {
                                mat.FCAN_FETCH_QTY = 0;
                            }
                        });
                    }
                });

                var schIds = woCraftModels.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
                if (schIds.Count > 0)
                {
                    var rpcServer = this.GetService<IModuleServices.MES007_JobBooking.IMES007JobQueryService>("MES007JobQuery");
                    var bookingResult = await rpcServer.QueryJobBookingAsync(new QueryRequestModel()
                    {
                        PageIndex = 0,
                        PageSize = int.MaxValue,
                        WhereGroup = new QueryWhereGroupModel
                        {
                            GroupType = EnumGroupType.AND,
                            Items = new List<QueryWhereItemModel> {
                                new QueryWhereItemModel{
                                    FieldName="booking.FCRAFT_SCHEDULE_ID",
                                    OperatorType=Core.HCPlatform.Utilities.EnumQuerySymbol.In,
                                    Value=string.Join(",",schIds),
                                    },
                                new QueryWhereItemModel{
                                    FieldName="booking.FWORK_STATUS",
                                    OperatorType=Core.HCPlatform.Utilities.EnumQuerySymbol.NotEqual,
                                    Value="cancel",
                                    }
                            }
                        }
                    });
                    if (bookingResult.StatusCode != 200)
                    {
                        ERROR(bookingResult, bookingResult.StatusCode, bookingResult.Message);
                    }

                    var bookingData = bookingResult.Entity;
                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(p =>
                    {
                        var bookings = bookingData.Where(j => j.FCRAFT_SCHEDULE_ID == p.FCRAFT_SCHEDULE_ID).ToList();
                        if (bookings.Count > 0)
                        {
                            //完成  加工中    暂停
                            var sumnum = bookings.Sum(s => (s.FPASS_QTY + s.FNG_QTY));//总数
                            var working = bookings.FirstOrDefault(k => k.FWORK_STATUS == "working");//加工中
                            if (working != null)
                            {
                                p.FCRAFT_STATUS = "working";//加工中
                            }
                            var paused = bookings.FirstOrDefault(k => k.FWORK_STATUS == "paused");//暂停中
                            if (paused != null)
                            {
                                p.FCRAFT_STATUS = "paused";//暂停中
                            }
                            if (working == null && paused == null)
                            {
                                p.FCRAFT_STATUS = "finished";//已完成
                            }
                        }
                        else
                        {
                            p.FCRAFT_STATUS = "unstart";//未开工
                        }
                    });
                }

                ////取出投入物料
                //await GetSubMaterialAsync(woCraftModels);

                ////获取排程的加工状态
                //await GetScheduleStatusAsync(woCraftModels);
            }

            DataResult<List<WORecCraftScheduleModel>> result = new DataResult<List<WORecCraftScheduleModel>>
            {
                Entity = woCraftModels.OrderBy(p => p.SCHEDULE_FSHOW_SEQNO).ToList(),
                StatusCode = 200,
                Pager = new PagerResult
                {
                    TotalRecords = totalSize,
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                }
            };
            return await OK(result);
        }

        #endregion



        #region 公共子方法

        /// <summary>
        /// 根据 "yy" + "当天秒数(5位)" 规则生成批号基础部分
        /// </summary>
        /// <returns>生成的批号字符串</returns>
        private string GenerateLotNumberBase()
        {
            // 使用 _iauth 服务获取当前时间，确保与应用服务器时间一致
            DateTime now = _iauth.GetCurDateTime();

            // 1. 获取 'yy' (年份后两位)
            string year = now.ToString("yy"); // 例如: "25"

            // 2. 计算今天从凌晨0点到现在的总秒数
            int secondsOfDay = (int)now.TimeOfDay.TotalSeconds;

            // 3. 将秒数补全为5位数 (sssss)
            string paddedSeconds = secondsOfDay.ToString("D5"); // 例如: "00123"

            // 4. 拼接成最终的基础批号并返回
            return $"L{year}{paddedSeconds}"; // 例如: "2500123"
        }


        /// <summary>
        /// 为需要生成批号的排程任务列表，生成并分配唯一的批号。
        /// </summary>
        /// <param name="db">数据库上下文</param>
        /// <param name="workOrderId">关联的工单ID</param>
        /// <param name="craftId">关联的工单工艺ID</param>
        /// <param name="schedules">需要分配批号的排程任务对象数组</param>
        /// <remarks>
        /// 然后查询数据库中具有相同基础部分的现有批号，以确定下一个可用的序列号，从而保证唯一性。
        /// </remarks>
        private async Task GenerateNewLotNumbersAsync(
            SqlSugarClient db,
            string workOrderId,
            string craftId,
            params T_MESD_CRAFT_SCHEDULE[] schedules)
        {
            // 如果没有需要处理的任务，则直接返回
            if (schedules == null || !schedules.Any())
            {
                return;
            }

            // 1. 标准方法生成批号的基础部分 (例如: "2540451")
            string lotBase = GenerateLotNumberBase();

            // 2. 查询数据库，找出当前工单工艺下，所有使用相同基础批号的现有记录
            var existingLots = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .Where(p => p.FWORK_ORDER_ID == workOrderId &&
                            p.FWORK_ORDER_CRAFT_ID == craftId &&
                            p.FLOT_NO.StartsWith(lotBase))
                .Select(p => p.FLOT_NO)
                .ToListAsync();

            // 3. 从现有批号中解析出最大的序列号
            int maxSeq = 0;
            if (existingLots.Any())
            {
                maxSeq = existingLots
                    // 安全地截取并转换序列号部分为整数
                    .Select(lot => int.TryParse(lot.Substring(lotBase.Length), out int seq) ? seq : 0)
                    .DefaultIfEmpty(0)
                    .Max();
            }

            // 4. 计算下一个可用的序列号
            int nextSeq = maxSeq + 1;

            // 5. 遍历需要分配批号的任务列表，并为它们赋上新的、唯一的批号
            foreach (var schedule in schedules)
            {
                // 使用3位补零的序列号 (如: 001, 002)，以支持在同一秒内创建多个任务
                schedule.FLOT_NO = $"{lotBase}{nextSeq++:D3}";
            }
        }
        #endregion
    }
}
