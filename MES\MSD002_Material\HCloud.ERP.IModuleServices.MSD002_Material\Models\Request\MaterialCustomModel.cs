﻿using MessagePack;
using System;

namespace HCloud.ERP.IModuleServices.MSD002_Material.Models
{
    /// <summary>
    /// 物料对象
    /// </summary>
    [MessagePackObject]
    public class MaterialCustomModel
    {
        public string FMATERIAL_CUSTOM_ID
        {
            get; set;
        }
        //物料Id
        public string FMATERIAL_ID
        {
            get; set;
        }
        //文本字段2
        public string FTEXT_02
        {
            get; set;
        }
        //文本字段1
        public string FTEXT_01
        {
            get; set;
        }
        //文本字段3
        public string FTEXT_03
        {
            get; set;
        }
        //文本字段4
        public string FTEXT_04
        {
            get; set;
        }
        //文本字段5
        public string FTEXT_05
        {
            get; set;
        }
        //文本字段6
        public string FTEXT_06
        {
            get; set;
        }
        //文本字段7
        public string FTEXT_07
        {
            get; set;
        }
        //文本字段8
        public string FTEXT_08
        {
            get; set;
        }
        //文本字段9
        public string FTEXT_09
        {
            get; set;
        }
        //文本字段10
        public string FTEXT_10
        {
            get; set;
        }
        //文本字段11
        public string FTEXT_11
        {
            get; set;
        }
        //文本字段12
        public string FTEXT_12
        {
            get; set;
        }
        //文本字段13
        public string FTEXT_13
        {
            get; set;
        }
        //文本字段14
        public string FTEXT_14
        {
            get; set;
        }
        //文本字段15
        public string FTEXT_15
        {
            get; set;
        }
        //文本字段16
        public string FTEXT_16
        {
            get; set;
        }
        //文本字段17
        public string FTEXT_17
        {
            get; set;
        }
        //文本字段18
        public string FTEXT_18
        {
            get; set;
        }
        //文本字段19
        public string FTEXT_19
        {
            get; set;
        }
        //文本字段20
        public string FTEXT_20
        {
            get; set;
        }
        //数字字段01
        public decimal ? FNUMBER_01
        {
            get; set;
        }
        //数字字段02
        public decimal ? FNUMBER_02
        {
            get; set;
        }
        //数字字段03
        public decimal ? FNUMBER_03
        {
            get; set;
        }
        //数字字段04
        public decimal ? FNUMBER_04
        {
            get; set;
        }
        //数字字段05
        public decimal ? FNUMBER_05
        {
            get; set;
        }
        //数字字段06
        public decimal ? FNUMBER_06
        {
            get; set;
        }
        //数字字段07
        public decimal ? FNUMBER_07
        {
            get; set;
        }
        //数字字段08
        public decimal ? FNUMBER_08
        {
            get; set;
        }
        //数字字段09
        public decimal ? FNUMBER_09
        {
            get; set;
        }
        //数字字段10
        public decimal ? FNUMBER_10
        {
            get; set;
        }
        //数字字段11
        public decimal ? FNUMBER_11
        {
            get; set;
        }
        //数字字段12
        public decimal ? FNUMBER_12
        {
            get; set;
        }
        //数字字段13
        public decimal ? FNUMBER_13
        {
            get; set;
        }
        //数字字段14
        public decimal ? FNUMBER_14
        {
            get; set;
        }
        //数字字段15
        public decimal ? FNUMBER_15
        {
            get; set;
        }
        //数字字段16
        public decimal ? FNUMBER_16
        {
            get; set;
        }
        //数字字段17
        public decimal ? FNUMBER_17
        {
            get; set;
        }
        //数字字段18
        public decimal ? FNUMBER_18
        {
            get; set;
        }
        //数字字段19
        public decimal ? FNUMBER_19
        {
            get; set;
        }
        //数字字段20
        public decimal ? FNUMBER_20
        {
            get; set;
        }
        //日期字段01
        public DateTime ? FDATETIME_01
        {
            get; set;
        }
        //日期字段02
        public DateTime ? FDATETIME_02
        {
            get; set;
        }
        //日期字段03
        public DateTime ? FDATETIME_03
        {
            get; set;
        }
        //日期字段04
        public DateTime ? FDATETIME_04
        {
            get; set;
        }
        //日期字段05
        public DateTime ? FDATETIME_05
        {
            get; set;
        }
        //日期字段06
        public DateTime ? FDATETIME_06
        {
            get; set;
        }
        //日期字段07
        public DateTime ? FDATETIME_07
        {
            get; set;
        }
        //日期字段08
        public DateTime ? FDATETIME_08
        {
            get; set;
        }
        //日期字段09
        public DateTime ? FDATETIME_09
        {
            get; set;
        }
        //日期字段10
        public DateTime ? FDATETIME_10
        {
            get; set;
        }
    }
}
