﻿using HCloud.Core.HCPlatform.Auth.Internal;
using HCloud.Core.HCPlatform.Ioc;
using HCloud.Core.HCPlatform.Multilingual;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.ProxyGenerator.Models;
using HCloud.Core.Sugar;
using HCloud.ERP.IModuleServices.MES001WorkArea;
using HCloud.ERP.IModuleServices.MES001WorkArea.Models;
using HCloud.ERP.IModuleServices.MES002_Craft;
using HCloud.ERP.IModuleServices.MES002_Craft.Models;
using HCloud.ERP.IModuleServices.MES003_WorkOrder.Models;
using HCloud.ERP.IModuleServices.MES007_JobBooking;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HCloud.ERP.IModuleServices.MSD002_Material.Models;
using HCloud.ERP.IModuleServices.MSD002_Material;
using HCloud.Core.HCPlatform.Serialization;
using HCloud.Core.HCPlatform.CommonData;
using HCloud.ERP.IModuleServices.MSD001_Unit.Models;
using HCloud.ERP.IModuleServices.MSD001_Unit;
using HCloud.ERP.IModuleServices.ADM024_Employee;
using HCloud.ERP.IModuleServices.ADM024_Employee.Models;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models.Request;
using HCloud.ERP.IModuleServices.MES003_WorkOrder;
using HCloud.ERP.IModuleServices.COP001_SaleOrder;
using HCloud.ERP.IModuleServices.COP001_SaleOrder.Models;
using HCloud.Core.HCPlatform.Utilities;
using HCloud.ERP.IModuleServices.QCS001_BadReason;
using HCloud.ERP.IModuleServices.QCS001_BadReason.Models;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models.Response;
using Newtonsoft.Json;
using System;

namespace HCloud.ERP.ModuleServices.MES007_JobBooking
{
    [ModuleName("MES007JobQuery")]
    public partial class MES007JobQueryService : ProxyServiceBase, IMES007JobQueryService
    {
        private readonly ILogger _Logger = null;
        private readonly IAuth _iauth = null;
        private readonly ISugar<MES007JobQueryService> _isugar = null;
        private readonly IMultilingualResource _multiLang = null;
        private readonly ISerializer<string> _serialize = null;
        private readonly ICommonDataProvider _commonDataProvider = null;

        public MES007JobQueryService(ILogger<MES007JobQueryService> logger,
             ISugar<MES007JobQueryService> sugar, IAuth auth, IMultilingualResource multiLang,
             ISerializer<string> serialize, ICommonDataProvider commonDataProvider)
        {
            _Logger = logger;
            _iauth = auth;
            _isugar = sugar;
            _multiLang = multiLang;
            _serialize = serialize;
            _commonDataProvider = commonDataProvider;
        }

        /// <summary>
        ///生产报工明细
        /// </summary>
        public async Task<DataResult<List<CraftJobBookingDetailModel>>> JobBookingDetailAsync(JobQueryRequstModel model)
        {
            var db = _isugar.DB;


            //取工位
            if (model.FstationQRM != null)
            {
                var rpcServer = this.GetService<IMES001StationService>("MES001Station");
                var rpcrtn = await rpcServer.GetStationIDSAsync(model.FstationQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取工位信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FSTATION_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });
            }

            //取订单
            if (model.SaleOrdQRM != null)
            {
                var rpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
                var rpcrtn = await rpcServer.QuerySaleOrderIdsAsync(model.SaleOrdQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取订单信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FSALE_ORDER_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });
            }

            //取车间
            if (model.WorkShopQRM != null)
            {
                var rpcServer = this.GetService<IMES001WorkShopService>("MES001WorkShop");
                var rpcrtn = await rpcServer.GetStationIdsByWorkShopAsync(model.WorkShopQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取车间信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FSTATION_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });
            }


            //取工艺
            if (model.CraftQRM != null)
            {
                var rpcServer = this.GetService<IMES002CraftService>("MES002Craft");
                var rpcrtn = await rpcServer.GetCraftIDSAsync(model.CraftQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取工艺信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FCRAFT_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });
            }



            //取产品
            if (model.MatQRM != null)
            {

                var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
                var rpcrtn = await rpcServer.GetMaterialIDSAsync(model.MatQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取物料信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FMATERIAL_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });

            }

            //取人员
            if (model.EmpQRM != null)
            {

                var rpcServer = this.GetService<IEmployeeService>("ADM024Employee");
                var rpcrtn = await rpcServer.GetEmployIDSAsync(model.EmpQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取人员信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FEMP_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });


            }



            //取工单
            if (model.WordOrdQRM != null)
            {
                var rpcServer = this.GetService<IMES003WorkOrderService>("MES003WorkOrder");
                var rpcrtn = await rpcServer.GetWorkOrderIDSAsync(model.WordOrdQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取生产工单信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FWORK_ORDER_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });
            }



            //取排程
            if (model.ScheduleQRM != null)
            {
                var rpcServer = this.GetService<IMES005CraftScheduleService>("MES005CraftSchedule");
                var rpcrtn = await rpcServer.GetScheduleIDSAsync(model.ScheduleQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取排程信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FCRAFT_SCHEDULE_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });

            }


            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model.JobQRM);


            var orderBy = $" FACT_ST_DATE DESC";
            if (model.JobQRM.Orders != null && model.JobQRM.Orders.Count > 0)
            {
                if (model.JobQRM.Orders[0].Fields != null && model.JobQRM.Orders[0].Fields.Count > 0)
                {
                    orderBy = $" { model.JobQRM.Orders[0].Fields[0]}  {model.JobQRM.Orders[0].OrderType.ToString()}";
                }
            }


            RefAsync<int> totalCount = new RefAsync<int>();

            //查询结果
            var jobBooks =
             await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(wheres).OrderBy(orderBy).Select<CraftJobBookingDetailModel>()
                .ToPageListAsync(model.JobQRM.PageIndex, model.JobQRM.PageSize, totalCount);


            //取出工位
            var stationResult = await RpcGetStationsAsync(jobBooks.Select(p => p.FSTATION_ID).ToList());

            //取出工艺
            var craftResult = await RpcGetCraftsAsync(jobBooks.Select(p => p.FCRAFT_ID).ToList());


            //取出产品信息
            var materialResult = await RpcGetMaterialsAsync(jobBooks.Select(p => p.FMATERIAL_ID).ToList());

            //取出报工人员
            var lstEmpId = jobBooks.Select(p => p.FEMP_ID).ToList();
            var lstCheckerId = jobBooks.Where(p => !string.IsNullOrWhiteSpace(p.FCHECK_PERSON_ID)).Select(p => p.FCHECK_PERSON_ID).ToList();
            List<string> lstId = new();
            lstId.AddRange(lstEmpId);
            lstId.AddRange(lstCheckerId);
            var empResult = await RpcGetEmployeesAsync(lstId);

            //取出工单
            var workOrdResult = await RpcGetWorkOrdersAsync(jobBooks.Select(p => p.FWORK_ORDER_ID).ToList());

            var unitIds = workOrdResult.Entity.SelectMany(p => { var ids = new List<string>(); ids.Add(p.FPRO_UNIT_ID); ids.Add(p.FWEIGHT_UNIT_ID); return ids; });
            //取出单位信息
            var unitResult = await RpcGetUnitsAsync(unitIds.ToList());


            //取出订单
            var saleOrdResult = await RpcGetSalesAsync(jobBooks.Select(p => p.FSALE_ORDER_ID).ToList());

            //取出排程任务
            var scheduleResult = await RpcGetSchedulesAsync(jobBooks.Select(p => p.FCRAFT_SCHEDULE_ID).ToList());

            // 取出细表不良
            var ngItem = await GetByBookingBadIdsAsync(jobBooks.Select(p => p.FCRAFT_JOB_BOOKING_ID).ToList());
            List<string> badIds = null;
            if (ngItem != null)
            {
                badIds = ngItem.Entity.Select(p => p.FBAD_REASON_ID).Distinct().ToList();
            }

            // 取出不良原因
            var lstBadReasonId = jobBooks.Where(a => !string.IsNullOrWhiteSpace(a.FBAD_REASON_ID)).Select(a => a.FBAD_REASON_ID).Distinct().ToList();
            lstBadReasonId = lstBadReasonId.Concat(badIds).Distinct().ToList();
            Dictionary<string, BadReasonModel> dicBadReason = new Dictionary<string, BadReasonModel>();
            if (lstBadReasonId != null && lstBadReasonId.Count > 0)
                dicBadReason = await RpcGetBadReasonAsync(lstBadReasonId);


            jobBooks.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var station = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == item.FSTATION_ID);
                if (station != null)
                {
                    item.FSTATION_NAME = station.FSTATION_NAME;
                    item.FSTATION_CODE = station.FSTATION_CODE;
                    item.FWORK_SHOP_NAME = station.FWORK_SHOP_NAME;
                    item.FWORK_SHOP_CODE = station.FWORK_SHOP_CODE;
                }

                var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == item.FCRAFT_ID);
                if (craft != null)
                {
                    item.FCRAFT_CODE = craft.FCRAFT_CODE;
                    item.FCRAFT_NAME = craft.FCRAFT_NAME;
                }


                var mat = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                if (mat != null)
                {
                    item.FMATERIAL_CODE = mat.FMATERIAL_CODE;
                    item.FMATERIAL_NAME = mat.FMATERIAL_NAME;
                    item.FSPEC_DESC = mat.FSPEC_DESC;
                    item.FGOODS_MODEL = mat.FGOODS_MODEL;
                }


                var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == item.FEMP_ID);
                if (emp != null)
                {
                    item.FEMP_CODE = emp.FEMP_CODE;
                    item.FEMP_NAME = emp.FEMP_NAME;
                }

                // 检验人
                if (!string.IsNullOrWhiteSpace(item.FCHECK_PERSON_ID))
                {
                    emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == item.FCHECK_PERSON_ID);
                    if (emp != null)
                    {
                        item.FCHECK_PERSON_NAME = emp.FEMP_NAME;
                    }
                }

                // 不良原因
                var badReasons = ngItem.Entity.Where(p => p.FCRAFT_JOB_BOOKING_ID == item.FCRAFT_JOB_BOOKING_ID).ToList();
                if (badReasons != null && badReasons.Count > 0)
                {
                    List<string> badNames = new List<string>();
                    badReasons.ForEach(bad =>
                    {
                        if (!string.IsNullOrWhiteSpace(bad.FBAD_REASON_ID) && dicBadReason.ContainsKey(bad.FBAD_REASON_ID))
                        {
                            badNames.Add(dicBadReason[bad.FBAD_REASON_ID].FBAD_REASON_NAME);
                        }
                    });
                    item.FBAD_REASON_NAME = String.Join(",", badNames);
                }
                else
                {
                    var badReasonId = item.FBAD_REASON_ID;
                    if (!string.IsNullOrWhiteSpace(badReasonId) && dicBadReason.ContainsKey(badReasonId))
                    {
                        item.FBAD_REASON_NAME = dicBadReason[badReasonId].FBAD_REASON_NAME;
                    }

                }

                var work = workOrdResult.Entity.FirstOrDefault(p => p.FWORK_ORDER_ID == item.FWORK_ORDER_ID);
                if (work != null)
                {

                    item.FWORK_ORDER_NO = work.FWORK_ORDER_NO;
                    //item.FPRO_QTY = work.FPRO_QTY;

                    item.FIUIU_CUST = work.FIUIU_CUST;
                    item.FIUIU_ORDER_NO = work.FIUIU_ORDER_NO;
                    item.FIUIU_ORDER_DATE = work.FIUIU_ORDER_DATE;
                    item.FIUIU_SALE_NAME = work.FIUIU_SALE_NAME;

                    item.FLAYOUT_NUM = work.FLAYOUT_NUM;
                    item.FSHEET_NUM = work.FSHEET_NUM;

                    item.FPASS_SHEET_NUM = string.IsNullOrWhiteSpace(work.FLAYOUT_NUM.ToString()) && work.FLAYOUT_NUM != 0 ?
                            int.Parse((item.FPASS_QTY / work.FLAYOUT_NUM).ToString()) : 0;
                    item.FNG_SHEET_NUM = string.IsNullOrWhiteSpace(work.FLAYOUT_NUM.ToString()) && work.FLAYOUT_NUM != 0 ?
                            int.Parse((item.FNG_QTY / work.FLAYOUT_NUM).ToString()) : 0;


                    var proUnit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == work.FPRO_UNIT_ID);
                    if (proUnit != null)
                    {
                        item.FPRO_UNIT_ID = proUnit.FUNIT_ID;
                        item.FPRO_UNIT_NAME = proUnit.FUNIT_NAME;
                    }

                    var weightUnit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == work.FWEIGHT_UNIT_ID);
                    if (weightUnit != null)
                    {
                        item.FWEIGHT_UNIT_ID = weightUnit.FUNIT_ID;
                        item.FWEIGHT_UNIT_NAME = weightUnit.FUNIT_NAME;
                    }
                }

                var saleOrd = saleOrdResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == item.FSALE_ORDER_ID);
                if (saleOrd != null)
                {
                    item.FSALE_ORDER_NO = saleOrd.FSALE_ORDER_NO;
                }

                var schedule = scheduleResult.Entity.FirstOrDefault(p => p.FCRAFT_SCHEDULE_ID == item.FCRAFT_SCHEDULE_ID);
                if (schedule != null)
                {
                    item.FCRAFT_SCHEDULE_NO = schedule.FCRAFT_SCHEDULE_NO;
                    item.FIS_OUT = schedule.FIS_OUT;
                    item.FLOT_NO = schedule.FLOT_NO;
                    item.FLOT_NO_COLOR = schedule.FLOT_NO_COLOR;
                }

            });


            DataResult<List<CraftJobBookingDetailModel>> dataResult = new DataResult<List<CraftJobBookingDetailModel>>()
            {
                Entity = jobBooks,
                StatusCode = 200,
                Pager = new PagerResult()
                {
                    PageNum = model.JobQRM.PageIndex,
                    PageSize = model.JobQRM.PageSize,
                    TotalRecords = totalCount.Value,
                },
            };

            return await OK(dataResult);
        }



        /// <summary>
        ///生产报工明细
        /// </summary>
        public async Task<DataResult<List<CraftJobBookingDetailModel>>> JobBookingQcDetailAsync(JobQueryRequstModel model)
        {
            var db = _isugar.DB;


            //取工位
            if (model.FstationQRM != null)
            {
                var rpcServer = this.GetService<IMES001StationService>("MES001Station");
                var rpcrtn = await rpcServer.GetStationIDSAsync(model.FstationQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取工位信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FSTATION_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });
            }

            //取订单
            if (model.SaleOrdQRM != null)
            {
                var rpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
                var rpcrtn = await rpcServer.QuerySaleOrderIdsAsync(model.SaleOrdQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取订单信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FSALE_ORDER_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });
            }

            //取车间
            if (model.WorkShopQRM != null)
            {
                var rpcServer = this.GetService<IMES001WorkShopService>("MES001WorkShop");
                var rpcrtn = await rpcServer.GetStationIdsByWorkShopAsync(model.WorkShopQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取车间信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FSTATION_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });
            }


            //取工艺
            if (model.CraftQRM != null)
            {
                var rpcServer = this.GetService<IMES002CraftService>("MES002Craft");
                var rpcrtn = await rpcServer.GetCraftIDSAsync(model.CraftQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取工艺信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FCRAFT_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });
            }



            //取产品
            if (model.MatQRM != null)
            {

                var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
                var rpcrtn = await rpcServer.GetMaterialIDSAsync(model.MatQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取物料信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FMATERIAL_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });

            }

            //取人员
            if (model.EmpQRM != null)
            {

                var rpcServer = this.GetService<IEmployeeService>("ADM024Employee");
                var rpcrtn = await rpcServer.GetEmployIDSAsync(model.EmpQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取人员信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FEMP_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });


            }



            //取工单
            if (model.WordOrdQRM != null)
            {
                var rpcServer = this.GetService<IMES003WorkOrderService>("MES003WorkOrder");
                var rpcrtn = await rpcServer.GetWorkOrderIDSAsync(model.WordOrdQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取生产工单信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FWORK_ORDER_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });
            }



            //取排程
            if (model.ScheduleQRM != null)
            {
                var rpcServer = this.GetService<IMES005CraftScheduleService>("MES005CraftSchedule");
                var rpcrtn = await rpcServer.GetScheduleIDSAsync(model.ScheduleQRM);
                if (rpcrtn.StatusCode != 200)
                {
                    ERROR(rpcrtn, 111111, $"获取排程信息失败");
                }
                model.JobQRM.WhereGroup.Items.Add(new QueryWhereItemModel() { FieldName = "FCRAFT_SCHEDULE_ID", OperatorType = EnumQuerySymbol.In, Value = string.Join(',', rpcrtn.Entity) });

            }


            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model.JobQRM);


            var orderBy = $" FACT_ST_DATE DESC";
            if (model.JobQRM.Orders != null && model.JobQRM.Orders.Count > 0)
            {
                if (model.JobQRM.Orders[0].Fields != null && model.JobQRM.Orders[0].Fields.Count > 0)
                {
                    orderBy = $" {model.JobQRM.Orders[0].Fields[0]}  {model.JobQRM.Orders[0].OrderType.ToString()}";
                }
            }


            RefAsync<int> totalCount = new RefAsync<int>();

            //查询结果
            var jobBooks =
             await db.Queryable<T_MESD_CRAFT_JOB_BOOKING_QC>()
                .Where(wheres).OrderBy(orderBy).Select<CraftJobBookingDetailModel>()
                .ToPageListAsync(model.JobQRM.PageIndex, model.JobQRM.PageSize, totalCount);


            //取出工位
            var stationResult = await RpcGetStationsAsync(jobBooks.Select(p => p.FSTATION_ID).ToList());

            //取出工艺
            var craftResult = await RpcGetCraftsAsync(jobBooks.Select(p => p.FCRAFT_ID).ToList());


            //取出产品信息
            var materialResult = await RpcGetMaterialsAsync(jobBooks.Select(p => p.FMATERIAL_ID).ToList());

            //取出报工人员
            var lstEmpId = jobBooks.Select(p => p.FEMP_ID).ToList();
            var lstCheckerId = jobBooks.Where(p => !string.IsNullOrWhiteSpace(p.FCHECK_PERSON_ID)).Select(p => p.FCHECK_PERSON_ID).ToList();
            List<string> lstId = new();
            lstId.AddRange(lstEmpId);
            lstId.AddRange(lstCheckerId);
            var empResult = await RpcGetEmployeesAsync(lstId);

            //取出工单
            var workOrdResult = await RpcGetWorkOrdersAsync(jobBooks.Select(p => p.FWORK_ORDER_ID).ToList());

            var unitIds = workOrdResult.Entity.SelectMany(p => { var ids = new List<string>(); ids.Add(p.FPRO_UNIT_ID); ids.Add(p.FWEIGHT_UNIT_ID); return ids; });
            //取出单位信息
            var unitResult = await RpcGetUnitsAsync(unitIds.ToList());


            //取出订单
            var saleOrdResult = await RpcGetSalesAsync(jobBooks.Select(p => p.FSALE_ORDER_ID).ToList());

            //取出排程任务
            var scheduleResult = await RpcGetSchedulesAsync(jobBooks.Select(p => p.FCRAFT_SCHEDULE_ID).ToList());

            // 取出不良原因
            var lstBadReasonId = jobBooks.Where(a => !string.IsNullOrWhiteSpace(a.FBAD_REASON_ID)).Select(a => a.FBAD_REASON_ID).ToList();
            Dictionary<string, BadReasonModel> dicBadReason = new();
            if (lstBadReasonId != null && lstBadReasonId.Count > 0)
                dicBadReason = await RpcGetBadReasonAsync(lstBadReasonId);



            jobBooks.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var station = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == item.FSTATION_ID);
                if (station != null)
                {
                    item.FSTATION_NAME = station.FSTATION_NAME;
                    item.FSTATION_CODE = station.FSTATION_CODE;
                    item.FWORK_SHOP_NAME = station.FWORK_SHOP_NAME;
                    item.FWORK_SHOP_CODE = station.FWORK_SHOP_CODE;
                }

                var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == item.FCRAFT_ID);
                if (craft != null)
                {
                    item.FCRAFT_CODE = craft.FCRAFT_CODE;
                    item.FCRAFT_NAME = craft.FCRAFT_NAME;
                }


                var mat = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                if (mat != null)
                {
                    item.FMATERIAL_CODE = mat.FMATERIAL_CODE;
                    item.FMATERIAL_NAME = mat.FMATERIAL_NAME;
                    item.FSPEC_DESC = mat.FSPEC_DESC;
                    item.FGOODS_MODEL = mat.FGOODS_MODEL;
                }


                var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == item.FEMP_ID);
                if (emp != null)
                {
                    item.FEMP_CODE = emp.FEMP_CODE;
                    item.FEMP_NAME = emp.FEMP_NAME;
                }

                // 检验人
                if (!string.IsNullOrWhiteSpace(item.FCHECK_PERSON_ID))
                {
                    emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == item.FCHECK_PERSON_ID);
                    if (emp != null)
                    {
                        item.FCHECK_PERSON_NAME = emp.FEMP_NAME;
                    }
                }

                // 不良原因
                var badReasonId = item.FBAD_REASON_ID;
                if (!string.IsNullOrWhiteSpace(badReasonId) && dicBadReason.ContainsKey(badReasonId))
                {
                    item.FBAD_REASON_NAME = dicBadReason[badReasonId].FBAD_REASON_NAME;
                }

                var work = workOrdResult.Entity.FirstOrDefault(p => p.FWORK_ORDER_ID == item.FWORK_ORDER_ID);
                if (work != null)
                {
                    item.FWORK_ORDER_NO = work.FWORK_ORDER_NO;

                    var proUnit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == work.FPRO_UNIT_ID);
                    if (proUnit != null)
                    {
                        item.FPRO_UNIT_ID = proUnit.FUNIT_ID;
                        item.FPRO_UNIT_NAME = proUnit.FUNIT_NAME;
                    }

                    var weightUnit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == work.FWEIGHT_UNIT_ID);
                    if (weightUnit != null)
                    {
                        item.FWEIGHT_UNIT_ID = weightUnit.FUNIT_ID;
                        item.FWEIGHT_UNIT_NAME = weightUnit.FUNIT_NAME;
                    }
                }

                var saleOrd = saleOrdResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == item.FSALE_ORDER_ID);
                if (saleOrd != null)
                {
                    item.FSALE_ORDER_NO = saleOrd.FSALE_ORDER_NO;
                }

                var schedule = scheduleResult.Entity.FirstOrDefault(p => p.FCRAFT_SCHEDULE_ID == item.FCRAFT_SCHEDULE_ID);
                if (schedule != null)
                {
                    item.FCRAFT_SCHEDULE_NO = schedule.FCRAFT_SCHEDULE_NO;
                    item.FIS_OUT = schedule.FIS_OUT;
                }

            });


            DataResult<List<CraftJobBookingDetailModel>> dataResult = new DataResult<List<CraftJobBookingDetailModel>>()
            {
                Entity = jobBooks,
                StatusCode = 200,
                Pager = new PagerResult()
                {
                    PageNum = model.JobQRM.PageIndex,
                    PageSize = model.JobQRM.PageSize,
                    TotalRecords = totalCount.Value,
                },
            };

            return await OK(dataResult);
        }

        /// <summary>
        /// 查询报工数据
        /// </summary>

        public async Task<DataResult<List<CraftJobBookingModel>>> QueryJobBookingAsync(QueryRequestModel model)
        {
            //确保性能, 目前用于工艺排程列表, 尽量少关联,少rpc调用. 

            var db = _isugar.DB;

            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);

            RefAsync<int> refAsync = new RefAsync<int>();
            var bookingData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>("booking").Where(wheres).Select<CraftJobBookingModel>().ToPageListAsync(model.PageIndex, model.PageSize, refAsync);

            DataResult<List<CraftJobBookingModel>> result = new DataResult<List<CraftJobBookingModel>>
            {
                Entity = bookingData,
                StatusCode = 200,
                Pager = new PagerResult
                {
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                    TotalRecords = refAsync.Value,
                }
            };
            return await OK(result);

        }
        /// <summary>
        /// 根据加工任务编号 获取报工信息  进行打印
        /// </summary>
        /// <param name="jobNo"></param>
        /// <returns></returns>
        public async Task<DataResult<PrintInfo>> QueryJobBookingByNoAsync(string jobNo)
        {
            var db = _isugar.DB;


            var bookingData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => p.FCRAFT_JOB_BOOKING_NO == jobNo).Select<PrintInfo>().FirstAsync();

            if (bookingData == null)
            {
                ERROR(null, 111111, $"未找到编号信息{jobNo}");
            }

            if (bookingData.FWORK_STATUS != "finished")
            {
                ERROR(null, 111111, $"加工任务 {jobNo} 未完工");
            }

            var jobBooks = new List<PrintInfo>() { bookingData };
            //取出工位
            var stationResult = await RpcGetStationsAsync(jobBooks.Select(p => p.FSTATION_ID).ToList());

            //取出工艺
            var craftResult = await RpcGetCraftsAsync(jobBooks.Select(p => p.FCRAFT_ID).ToList());


            //取出产品信息
            var materialResult = await RpcGetMaterialsAsync(jobBooks.Select(p => p.FMATERIAL_ID).ToList());

            ////取出报工人员
            //var lstEmpId = jobBooks.Select(p => p.FEMP_ID).ToList();
            //var lstCheckerId = jobBooks.Where(p => !string.IsNullOrWhiteSpace(p.FCHECK_PERSON_ID)).Select(p => p.FCHECK_PERSON_ID).ToList();
            //List<string> lstId = new();
            //lstId.AddRange(lstEmpId);
            //lstId.AddRange(lstCheckerId);
            //var empResult = await RpcGetEmployeesAsync(lstId);

            //取出工单
            var workOrdResult = await RpcGetWorkOrdersAsync(jobBooks.Select(p => p.FWORK_ORDER_ID).ToList());

            //var unitIds = workOrdResult.Entity.SelectMany(p => { var ids = new List<string>(); ids.Add(p.FPRO_UNIT_ID); ids.Add(p.FWEIGHT_UNIT_ID); return ids; });
            ////取出单位信息
            //var unitResult = await RpcGetUnitsAsync(unitIds.ToList());


            //取出订单
            var saleOrdResult = await RpcGetSalesAsync(jobBooks.Select(p => p.FSALE_ORDER_ID).ToList());


            jobBooks.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var station = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == item.FSTATION_ID);
                if (station != null)
                {
                    item.FSTATION_NAME = station.FSTATION_NAME;
                    item.FSTATION_CODE = station.FSTATION_CODE;
                    item.FWORK_SHOP_NAME = station.FWORK_SHOP_NAME;
                    item.FWORK_SHOP_CODE = station.FWORK_SHOP_CODE;
                }

                var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == item.FCRAFT_ID);
                if (craft != null)
                {
                    item.FCRAFT_CODE = craft.FCRAFT_CODE;
                    item.FCRAFT_NAME = craft.FCRAFT_NAME;
                }


                var mat = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                if (mat != null)
                {
                    item.FMATERIAL_CODE = mat.FMATERIAL_CODE;
                    item.FMATERIAL_NAME = mat.FMATERIAL_NAME;
                    item.FSPEC_DESC = mat.FSPEC_DESC;
                    item.FGOODS_MODEL = mat.FGOODS_MODEL;
                }


                //var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == item.FEMP_ID);
                //if (emp != null)
                //{
                //    item.FEMP_CODE = emp.FEMP_CODE;
                //    item.FEMP_NAME = emp.FEMP_NAME;
                //}

                //// 检验人
                //if (!string.IsNullOrWhiteSpace(item.FCHECK_PERSON_ID))
                //{
                //    emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == item.FCHECK_PERSON_ID);
                //    if (emp != null)
                //    {
                //        item.FCHECK_PERSON_NAME = emp.FEMP_NAME;
                //    }
                //}

                //// 不良原因
                //var badReasonId = item.FBAD_REASON_ID;
                //if (!string.IsNullOrWhiteSpace(badReasonId) && dicBadReason.ContainsKey(badReasonId))
                //{
                //    item.FBAD_REASON_NAME = dicBadReason[badReasonId].FBAD_REASON_NAME;
                //}

                var work = workOrdResult.Entity.FirstOrDefault(p => p.FWORK_ORDER_ID == item.FWORK_ORDER_ID);
                if (work != null)
                {
                    item.FWORK_ORDER_NO = work.FWORK_ORDER_NO;

                    //var proUnit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == work.FPRO_UNIT_ID);
                    //if (proUnit != null)
                    //{
                    //    item.FPRO_UNIT_ID = proUnit.FUNIT_ID;
                    //    item.FPRO_UNIT_NAME = proUnit.FUNIT_NAME;
                    //}

                    //var weightUnit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == work.FWEIGHT_UNIT_ID);
                    //if (weightUnit != null)
                    //{
                    //    item.FWEIGHT_UNIT_ID = weightUnit.FUNIT_ID;
                    //    item.FWEIGHT_UNIT_NAME = weightUnit.FUNIT_NAME;
                    //}
                }

                var saleOrd = saleOrdResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == item.FSALE_ORDER_ID);
                if (saleOrd != null)
                {
                    item.FSALE_ORDER_NO = saleOrd.FSALE_ORDER_NO;
                }

                //var schedule = scheduleResult.Entity.FirstOrDefault(p => p.FCRAFT_SCHEDULE_ID == item.FCRAFT_SCHEDULE_ID);
                //if (schedule != null)
                //{
                //    item.FCRAFT_SCHEDULE_NO = schedule.FCRAFT_SCHEDULE_NO;
                //}

            });



            DataResult<PrintInfo> result = new DataResult<PrintInfo>
            {
                Entity = bookingData,
                StatusCode = 200,
            };
            return await OK(result);
        }


        /// <summary>
        /// 返回物料信息
        /// </summary>
        /// <param name="materialIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleMaterialModel>>> RpcGetMaterialsAsync(List<string> ids)
        {
            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            var rpcrtn = await rpcServer.GetForBusinessByIdsAsync(ids);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取物料信息失败");
            }
            return rpcrtn;
        }

        /// <summary>
        /// 返回排程任务信息
        /// </summary>
        private async Task<DataResult<List<SimpleScheduleModel>>> RpcGetSchedulesAsync(List<string> ids)
        {
            var rpcServer = this.GetService<IMES005CraftScheduleService>("MES005CraftSchedule");
            var rpcrtn = await rpcServer.SimpleScheduleAsync(ids);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取排程信息失败");
            }
            return rpcrtn;
        }

        /// <summary>
        /// 返回排程任务信息
        /// </summary>
        private async Task<Dictionary<string, BadReasonModel>> RpcGetBadReasonAsync(List<string> ids)
        {
            var rpcServer = this.GetService<IQCS001BadReasonService>("QCS001BadReason");
            QueryRequestModel model = new()
            {
                WhereGroup = new QueryWhereGroupModel(),
                PageIndex = 1,
                PageSize = 100,
            };
            model.WhereGroup.GroupType = EnumGroupType.AND;
            model.WhereGroup.Items = new List<QueryWhereItemModel>
            {
                new QueryWhereItemModel()
                {
                    FieldName = "a.FBAD_REASON_ID",
                    OperatorType = EnumQuerySymbol.In,
                    Value = string.Join(',', ids),
                }
            };
            var rpcRtn = await rpcServer.GetAllAsync(model);
            if (rpcRtn.StatusCode != 200)
            {
                ERROR(rpcRtn, 111111, "获取不良原因信息失败");
            }
            return rpcRtn.Entity.ToDictionary(a => a.FBAD_REASON_ID);
        }

        /// <summary>
        /// 返回生产工单信息
        /// </summary>

        private async Task<DataResult<List<SimpleWorkOrderModel>>> RpcGetWorkOrdersAsync(List<string> ids)
        {
            var rpcServer = this.GetService<IMES003WorkOrderService>("MES003WorkOrder");
            var rpcrtn = await rpcServer.GetWorkOrdersAsync(ids);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取生产工单信息失败");
            }
            return rpcrtn;
        }


        /// <summary>
        /// 返回销售订单信息
        /// </summary>

        private async Task<DataResult<List<SimpleSaleOrderModel>>> RpcGetSalesAsync(List<string> ids)
        {
            var rpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
            var rpcrtn = await rpcServer.QuerySalesInfoByIdsAsync(ids);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取销售订单信息失败");
            }
            return rpcrtn;
        }



        /// <summary>
        /// 返回员工姓名
        /// </summary>
        /// <param name="empIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<EmployeeModel>>> RpcGetEmployeesAsync(List<string> ids)
        {
            var rpcServer = this.GetService<IEmployeeService>("ADM024Employee");
            var rpcrtn = await rpcServer.GetEmpsByIdsAsync(ids);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取人员信息失败");
            }
            return rpcrtn;
        }

        /// <summary>
        /// 返回工艺信息
        /// </summary>
        /// <param name="craftIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleCraftModel>>> RpcGetCraftsAsync(List<string> ids)
        {
            var rpcServer = this.GetService<IMES002CraftService>("MES002Craft");
            var rpcrtn = await rpcServer.GetForBusinessByIdsAsync(ids);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取工艺信息失败");
            }
            return rpcrtn;
        }


        /// <summary>
        /// 返回工位信息
        /// </summary>
        /// <param name="craftIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleStationModel>>> RpcGetStationsAsync(List<string> ids)
        {
            var rpcServer = this.GetService<IMES001StationService>("MES001Station");
            var rpcrtn = await rpcServer.GetForBusinessByIdsAsync(ids);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取工位信息失败");
            }
            return rpcrtn;
        }


        /// <summary>
        /// 返回单位信息
        /// </summary>
        /// <param name="unitIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleUnitModel>>> RpcGetUnitsAsync(List<string> ids)
        {
            var rpcServer = this.GetService<IMSD001UnitService>("MSD001Unit");
            var rpcrtn = await rpcServer.GetByIdsForBusinessAsync(ids);
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, $"获取单位信息失败");
            }
            return rpcrtn;
        }

        /// <summary>
        /// 根据工位IDS查询出对应工位最新状态
        /// </summary>
        /// <param name="unitIds"></param>
        /// <returns></returns>
        public async Task<DataResult<List<CraftJobBookingDetailModel>>> QueryStationStatusByIdsAsync(List<string> ids)
        {
            var db = _isugar.DB;

            var jobBooks =
            await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
               .Where(p => ids.Contains(p.FSTATION_ID))
               //.OrderBy(p=> p.FACT_ST_DATE,OrderByType.Desc)
               .PartitionBy(p => p.FSTATION_ID)
               .OrderBy(p => p.FACT_ST_DATE, OrderByType.Desc)
               .Take(1)
               .Select<CraftJobBookingDetailModel>()
               .ToListAsync();

            DataResult<List<CraftJobBookingDetailModel>> dataResult = new DataResult<List<CraftJobBookingDetailModel>>()
            {
                Entity = jobBooks,
                StatusCode = 200
            };
            return dataResult;
        }

        /// <summary>
        /// 使用加工任务ID 获取报工不良明细
        /// </summary>
        /// <param name="craftJobBookId"></param>
        /// <returns></returns>
        public async Task<DataResult<List<CraftJobBookingBadModel>>> GetByBookingBadIdAsync(string id)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var data = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING_BAD>()
                .Where(p => p.FCRAFT_JOB_BOOKING_ID == id).Select<CraftJobBookingBadModel>().ToListAsync();

            var ids = data.Select(p => p.FBAD_REASON_ID).Distinct().ToList();
            var badResult = await RpcGetBadReasonAsync(ids);
            data.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var badId = item.FBAD_REASON_ID;
                if (!string.IsNullOrWhiteSpace(badId) && badResult.ContainsKey(badId))
                {
                    item.FBAD_REASON_NAME = badResult[badId].FBAD_REASON_NAME;
                }
            });

            DataResult<List<CraftJobBookingBadModel>> dataResult = new DataResult<List<CraftJobBookingBadModel>>
            {
                StatusCode = 200,
                Entity = data,
            };

            return await OK(dataResult);
        }


        /// <summary>
        /// 使用加工任务ids 获取报工不良明细
        /// </summary>
        /// <param name="craftJobBookIds"></param>
        /// <returns></returns>
        public async Task<DataResult<List<CraftJobBookingBadModel>>> GetByBookingBadIdsAsync(List<string> ids)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var data = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING_BAD>()
                .Where(p => ids.Contains(p.FCRAFT_JOB_BOOKING_ID)).Select<CraftJobBookingBadModel>().ToListAsync();

            var badIds = data.Select(p => p.FBAD_REASON_ID).Distinct().ToList();
            var badResult = await RpcGetBadReasonAsync(badIds);
            data.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var badId = item.FBAD_REASON_ID;
                if (!string.IsNullOrWhiteSpace(badId) && badResult.ContainsKey(badId))
                {
                    item.FBAD_REASON_NAME = badResult[badId].FBAD_REASON_NAME;
                }
            });

            DataResult<List<CraftJobBookingBadModel>> dataResult = new DataResult<List<CraftJobBookingBadModel>>
            {
                StatusCode = 200,
                Entity = data,
            };

            return await OK(dataResult);
        }

    }
}
