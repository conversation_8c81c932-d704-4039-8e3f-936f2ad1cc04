﻿using HCloud.Core.Common;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.ProxyGenerator.Models;
using HCloud.Core.Sugar;
using HCloud.ERP.IModuleServices.MES003_WorkOrder.Models;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HCloud.Core.HCPlatform.CommonData;
using HCloud.ERP.IModuleServices.MES003_WorkOrder;
using HCloud.ERP.IModuleServices.MES007_JobBooking;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models.Request;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models;
using HCloud.ERP.IModuleServices.MES010_WorkOrderChange.Models;
using HCloud.ERP.IModuleServices.ADM024_Employee.Models;
using HCloud.ERP.IModuleServices.MSD001_Unit.Models;
using HCloud.ERP.IModuleServices.MES002_Craft.Models;
using HCloud.ERP.IModuleServices.COP001_SaleOrder.Models;
using HCloud.ERP.IModuleServices.PUR013_OutApply;
using Newtonsoft.Json.Linq;
using Castle.Core.Internal;

namespace HCloud.ERP.ModuleServices.MES005_CraftSchedule
{
    public partial class MES005CraftScheduleService : ProxyServiceBase, IMES005CraftScheduleService
    {

        #region 结案
        /// <summary>
        /// 结案
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> EndCloseAsync(List<string> ids)
        {
            await cancelSchJobKing(ids);//排程结案 先取消加工任务


            //结案前检查
            if (!await ValidateEndCloseAsync(ids))
            {
                ERROR(null, 107000, _multiLang["结案检查失败."]);
            }

            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            //构造数据
            List<string> schStatusIds = (await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS>()
                     .Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID))
                     .Select(p => p.FCRAFT_SCHEDULE_STATUS_ID)
                     .ToListAsync()).Distinct().ToList();

            var daos = schStatusIds.Select(statusId =>

                new T_MESD_CRAFT_SCHEDULE_STATUS
                {
                    FIF_CLOSE = 3,
                    FCLOSEDATE = _iauth.GetCurDateTime(),
                    FCLOSER = user.UserPsnName,
                    FCLOSER_ID = user.UserPsnId,
                    FCRAFT_SCHEDULE_STATUS_ID = statusId,
                }
            ).ToList();

            //更新结案状态
            await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daos)
            .UpdateColumns(t => new { t.FIF_CLOSE, t.FCLOSEDATE, t.FCLOSER, t.FCLOSER_ID })
            .ExecuteCommandAsync();

            //检查工单是否需要结案
            var wordIds = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID)).Select(p => p.FWORK_ORDER_ID).Distinct().ToListAsync();

            var workinfos = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID)).Where((a, b) => wordIds.Contains(a.FWORK_ORDER_ID) && a.FPRO_QTY <= b.FFINISH_QTY && (b.FIF_CLOSE == 0 || b.FIF_CLOSE == 2)).Select(a => a.FWORK_ORDER_ID).ToListAsync();
            if (workinfos.Count > 0)
            {
                //更新结案状态
                await db.Updateable<T_MESD_WORK_ORDER_STATUS>()
                .SetColumns(p =>
                new T_MESD_WORK_ORDER_STATUS
                {
                    FIF_CLOSE = 3,
                    FCLOSEDATE = _iauth.GetCurDateTime(),
                    FCLOSER = user.UserPsnName,
                    FCLOSER_ID = user.UserPsnId,
                })
                .Where(p => workinfos.Contains(p.FWORK_ORDER_ID))
                .ExecuteCommandAsync();
            }

            //返回结果
            var dynamics = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER_CRAFT_STATUS>
                ((sch, schStatus, craftStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                        JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
                .Where((sch, schStatus, craftStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID))
                .Select((sch, schStatus, craftStatus) => new
                {
                    FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,
                    FIF_CLOSE = schStatus.FIF_CLOSE,
                    FCLOSEDATE = schStatus.FCLOSEDATE,
                    FCLOSER = schStatus.FCLOSER,
                    FCLOSER_ID = schStatus.FCLOSER_ID,
                })
                .ToListAsync();

            DataResult<object> result = new DataResult<object>
            {
                StatusCode = 200,
                Entity = dynamics,
            };

            try
            {
                //结案自动调用产生投料信息
                var rpcServer = this.GetService<IMES003WorkOrderService>("MES003WorkOrder");
                rpcServer.GenrateWorkOrderFetchAsync(wordIds);

            }
            catch (Exception)
            {


            }


            return await OK(result);
        }

        /// <summary>
        /// 结案排程 取消正在加工中得任务
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>

        public async Task cancelSchJobKing(List<string> ids)
        {
            //结案自动取消开工中的加工任务 如果有的话
            var db = _isugar.DB;
            var jobbookings = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID) && (p.FWORK_STATUS == "paused" || p.FWORK_STATUS == "working")).ToListAsync();

            if (jobbookings.Count > 0)
            {
                foreach (var item in jobbookings)
                {
                    item.FWORK_STATUS = "cancel";
                }

                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(jobbookings).UpdateColumns(p => new { p.FWORK_STATUS }).ExecuteCommandAsync();
            }


            SendMsg();//发送消息通知
        }

        /// <summary>
        /// 发送通知调用
        /// </summary>
        /// <returns></returns>
        public async Task SendMsg()
        {
            var wss = await _ThirdPartySetting.GetSettingParmByCodeAsync<JObject>("wss");
            if (wss["open"].ToString() == "true")
            {
                _httpClient.CreateClient("a").GetAsync($"http://{wss["ipport"].ToString()}/send?msg=1");
            }
        }

        /// <summary>
        /// 结案检查
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        private async Task<bool> ValidateEndCloseAsync(List<string> ids)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var schNo = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
               ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
               .Where((sch, schStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID) && (schStatus.FIF_CLOSE == 1 || schStatus.FIF_CLOSE == 3))
               .Select((sch, schStatus) => sch.FCRAFT_SCHEDULE_NO)
               .FirstAsync();
            if (!string.IsNullOrWhiteSpace(schNo))
            {
                ERROR(null, 100701, string.Format(_multiLang["任务编号{0}已结案,不能重复操作."], schNo));
            }

            //存在加工中的任务不能结案
            var jobResult = await ValidateScheduleJobBookingAsync(ids);
            if (jobResult.StatusCode != 200)
            {
                ERROR(jobResult, jobResult.StatusCode, jobResult.Message);
            }
            //

            return true;
        }
        /// <summary>
        /// 验证排程任务是否在加工中
        /// </summary>
        /// <param name="woIds"></param>
        /// <returns></returns>
        private async Task<DataResult<bool>> ValidateScheduleJobBookingAsync(List<string> ids)
        {
            var rpcServer = this.GetService<IMES007JobBookingService>("MES007JobBooking");
            return await rpcServer.ValidateScheduleJobBookingAsync(ids);
        }

        #endregion

        #region  反结案
        /// <summary>
        /// 反结案
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> UnEndCloseAsync(List<string> ids)
        {
            //反结案前检查
            if (!await ValidateUnEndCloseAsync(ids))
            {
                ERROR(null, 108100, _multiLang["反结案检查失败."]);
            }

            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;


            //构造数据
            List<string> schStatusIds = (await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS>()
                      .Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID))
                      .Select(p => p.FCRAFT_SCHEDULE_STATUS_ID)
                      .ToListAsync()).Distinct().ToList();

            var daos = schStatusIds.Select(statusId =>

                new T_MESD_CRAFT_SCHEDULE_STATUS
                {
                    FIF_CLOSE = 2,
                    FCLOSEDATE = _iauth.GetCurDateTime(),
                    FCLOSER = user.UserPsnName,
                    FCLOSER_ID = user.UserPsnId,
                    FCRAFT_SCHEDULE_STATUS_ID = statusId,
                }
            ).ToList();

            //更新为反结案
            await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daos)
            .UpdateColumns(t => new { t.FIF_CLOSE, t.FCLOSEDATE, t.FCLOSER, t.FCLOSER_ID })
            .ExecuteCommandAsync();

            //返回结果
            var dynamics = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER_CRAFT_STATUS>
                ((sch, schStatus, craftStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                        JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
                .Where((sch, schStatus, craftStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID))
                .Select((sch, schStatus, craftStatus) => new
                {
                    FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,
                    FIF_CLOSE = schStatus.FIF_CLOSE,
                    FCLOSEDATE = schStatus.FCLOSEDATE,
                    FCLOSER = schStatus.FCLOSER,
                    FCLOSER_ID = schStatus.FCLOSER_ID,
                })
                .ToListAsync();

            DataResult<object> result = new DataResult<object>
            {
                StatusCode = 200,
                Entity = dynamics,
            };
            return await OK(result);
        }

        /// <summary>
        /// 反结案检查
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        private async Task<bool> ValidateUnEndCloseAsync(List<string> ids)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var schNo = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
               ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
               .Where((sch, schStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID) && (schStatus.FIF_CLOSE == 0 || schStatus.FIF_CLOSE == 2))
               .Select((sch, schStatus) => sch.FCRAFT_SCHEDULE_NO)
               .FirstAsync();
            if (!string.IsNullOrWhiteSpace(schNo))
            {
                ERROR(null, 100701, string.Format(_multiLang["任务编号{0}已反结案,不能重复操作."], schNo));
            }


            //验证工单是否有效
            var woIds = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
               .Where(sch => ids.Contains(sch.FCRAFT_SCHEDULE_ID))
               .Select(sch => sch.FWORK_ORDER_ID)
               .ToListAsync();
            if (!await ValidateWorderOrderIsValidAsync(woIds))
            {
                ERROR(null, 106011, _multiLang["验证工单状态失败."]);
            }

            return true;
        }
        #endregion


        #region 下发任务
        /// <summary>
        /// 下发任务
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> ReleaseTaskByIdsAsync(List<string> ids)
        {
            var user = await _iauth.GetUserAccountAsync();
            if (!await ValidateReleaseTaskAsync(ids))
            {
                ERROR(null, 100202, _multiLang["下发任务检查失败"]);
            }

            var db = _isugar.DB;

            //取出工单工艺id
            var woCraftIds = (await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID))
                .Select(p => p.FWORK_ORDER_CRAFT_ID)
                .ToListAsync()).Distinct().ToList();



            List<string> schStatusIds = (await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS>()
                .Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID))
                .Select(p => p.FCRAFT_SCHEDULE_STATUS_ID)
                .ToListAsync()).Distinct().ToList();

            var daos = schStatusIds.Select(statusId =>

                new T_MESD_CRAFT_SCHEDULE_STATUS
                {
                    FRELEASE_STATUS = true,
                    FRELEASER = user.UserPsnName,
                    FRELEASER_ID = user.UserPsnId,
                    FRELEASE_DATE = _iauth.GetCurDateTime(),
                    FCRAFT_SCHEDULE_STATUS_ID = statusId,
                }
            ).ToList();


            db = _isugar.DB;
            try
            {
                db.BeginTran();

                //更新下发状态           
                await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daos)
                    .UpdateColumns(t => new { t.FRELEASE_STATUS, t.FRELEASER, t.FRELEASER_ID, t.FRELEASE_DATE })
                    .ExecuteCommandAsync();

                //更新已下发数量
                await UpdateWorkOrderCraftReleaseQtyAsync(woCraftIds, db);

                db.CommitTran();
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }

            //返回结果
            var dynamics = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER_CRAFT_STATUS>
                ((sch, schStatus, craftStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                        JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
                .Where((sch, schStatus, craftStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID))
                .Select((sch, schStatus, craftStatus) => new
                {
                    FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,
                    FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                    FRELEASER = schStatus.FRELEASER,
                    FRELEASER_ID = schStatus.FRELEASER_ID,
                    FRELEASE_DATE = schStatus.FRELEASE_DATE,
                    FRELEASE_QTY = craftStatus.FRELEASE_QTY
                })
                .ToListAsync();

            DataResult<object> result = new DataResult<object>()
            {
                Entity = dynamics,
                StatusCode = 200,
            };


            UpdateNewScheduleAsync(ids);

            return await OK(result);
        }

        /// <summary>
        /// 验证下发任务
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        private async Task<bool> ValidateReleaseTaskAsync(List<string> ids)
        {
            var db = _isugar.DB;
            var schNo = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((sch, schStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID) && schStatus.FRELEASE_STATUS == true)
                .Select((sch, schStatus) => sch.FCRAFT_SCHEDULE_NO)
                .FirstAsync();
            if (!string.IsNullOrWhiteSpace(schNo))
            {
                ERROR(null, 100201, string.Format(_multiLang["执行失败, 任务编号{0}已下发."], schNo));
            }

            schNo = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((sch, schStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID) && (schStatus.FIF_CLOSE == 1 || schStatus.FIF_CLOSE == 3))
                .Select((sch, schStatus) => sch.FCRAFT_SCHEDULE_NO)
                .FirstAsync();
            if (!string.IsNullOrWhiteSpace(schNo))
            {
                ERROR(null, 100202, string.Format(_multiLang["执行失败, 任务编号{0}已结案."], schNo));
            }

            //验证工单是否有效
            var woIds = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
               .Where(sch => ids.Contains(sch.FCRAFT_SCHEDULE_ID))
               .Select(sch => sch.FWORK_ORDER_ID)
               .ToListAsync();
            if (!await ValidateWorderOrderIsValidAsync(woIds))
            {
                ERROR(null, 106011, _multiLang["验证工单状态失败."]);
            }

            return true;
        }

        /// <summary>
        /// 产生任务编号
        /// </summary>
        /// <param name="count"></param>
        /// <returns></returns>
        private async Task<List<string>> GetBillCodesAsync(int count)
        {
            GenBillCodeModel model = new GenBillCodeModel()
            {
                FBILL_COUNT = count,
                FBILL_RULE_CODE = "BR-CPF-0029",
                FENTITY_DESC = "工艺排程任务",
                FENTITY_NAME = "T_MESD_CRAFT_SCHEDULE",
                FFIELD_NAME = "FCRAFT_SCHEDULE_NO",
                FFIELD_DESC = "任务编号",
                Parms = new List<GenBillCodeParmModel> { }
            };
            model.Parms.Add(new GenBillCodeParmModel
            {
                FPARM_CODE = "Date",
                FPARM_VALUE = _iauth.GetCurDateTime().ToString("yyyyMMdd"),
            });
            return await _commonDataProvider.GenEntBillCodeAsync(model);
        }
        #endregion

        #region 撤回下发任务
        /// <summary>
        /// 撤回下发任务
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> UnReleaseTaskByIdsAsync(List<string> ids)
        {
            var user = await _iauth.GetUserAccountAsync();
            if (!await ValidateUnReleaseTaskAsync(ids))
            {
                ERROR(null, 100302, _multiLang["撤回下发任务检查失败"]);
            }

            var db = _isugar.DB;

            //取出工单工艺id
            List<string> woCraftIds = (await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID))
                .Select(p => p.FWORK_ORDER_CRAFT_ID)
                .ToListAsync()).Distinct().ToList();


            List<string> schStatusIds = (await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS>()
                .Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID))
                .Select(p => p.FCRAFT_SCHEDULE_STATUS_ID)
                .ToListAsync()).Distinct().ToList();

            var daos = schStatusIds.Select(statusId =>

                new T_MESD_CRAFT_SCHEDULE_STATUS
                {
                    FRELEASE_STATUS = false,
                    FRELEASER = user.UserPsnName,
                    FRELEASER_ID = user.UserPsnId,
                    FRELEASE_DATE = _iauth.GetCurDateTime(),
                    FCRAFT_SCHEDULE_STATUS_ID = statusId,
                }
            ).ToList();


            db = _isugar.DB;
            try
            {
                db.BeginTran();

                //更新下发状态           
                await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daos)
                    .UpdateColumns(t => new { t.FRELEASE_STATUS, t.FRELEASER, t.FRELEASER_ID, t.FRELEASE_DATE })
                    .ExecuteCommandAsync();

                //更新已下发数量
                await UpdateWorkOrderCraftReleaseQtyAsync(woCraftIds, db);

                db.CommitTran();
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }

            //返回结果
            var dynamics = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER_CRAFT_STATUS>
                ((sch, schStatus, craftStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                        JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
                .Where((sch, schStatus, craftStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID))
                .Select((sch, schStatus, craftStatus) => new
                {
                    FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,
                    FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                    FRELEASER = schStatus.FRELEASER,
                    FRELEASER_ID = schStatus.FRELEASER_ID,
                    FRELEASE_DATE = schStatus.FRELEASE_DATE,
                    FRELEASE_QTY = craftStatus.FRELEASE_QTY
                })
                .ToListAsync();

            DataResult<object> result = new DataResult<object>()
            {
                Entity = dynamics,
                StatusCode = 200,
            };
            return await OK(result);
        }

        /// <summary>
        /// 验证撤回下发任务
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        private async Task<bool> ValidateUnReleaseTaskAsync(List<string> ids)
        {
            var db = _isugar.DB;

            //
            var schNo = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((sch, schStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID) && schStatus.FRELEASE_STATUS == false)
                .Select((sch, schStatus) => sch.FCRAFT_SCHEDULE_NO)
                .FirstAsync();
            if (!string.IsNullOrWhiteSpace(schNo))
            {
                ERROR(null, 100201, string.Format(_multiLang["任务编号{0}已撤回,不能重复操作."], schNo));
            }

            //
            schNo = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((sch, schStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID) && (schStatus.FIF_CLOSE == 1 || schStatus.FIF_CLOSE == 3))
                .Select((sch, schStatus) => sch.FCRAFT_SCHEDULE_NO)
                .FirstAsync();
            if (!string.IsNullOrWhiteSpace(schNo))
            {
                ERROR(null, 100202, string.Format(_multiLang["执行失败, 任务编号{0}已结案."], schNo));
            }

            //存在加工中的任务不能撤回
            var jobResult = await ValidateScheduleJobBookingAsync(ids);
            if (jobResult.StatusCode != 200)
            {
                ERROR(jobResult, jobResult.StatusCode, jobResult.Message);
            }
            //

            //验证工单是否有效
            var woIds = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
               .Where(sch => ids.Contains(sch.FCRAFT_SCHEDULE_ID))
               .Select(sch => sch.FWORK_ORDER_ID)
               .ToListAsync();
            if (!await ValidateWorderOrderIsValidAsync(woIds))
            {
                ERROR(null, 106011, _multiLang["验证工单状态失败."]);
            }

            return true;
        }
        #endregion

        #region 返写工单工艺
        /// <summary>
        /// 更新工单工艺已计划数量
        /// </summary>
        /// <param name="scheduleIds"></param>
        /// <returns></returns>
        private async Task UpdateWorkOrderCraftPlanQtyAsync(List<string> woCraftIds, bool synRelease, SqlSugarClient db)
        {
            //更新计划数
            var craftPlanQtys = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER_CRAFT_STATUS>
                                    ((sch, woCraftStatus) => new JoinQueryInfos(JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == woCraftStatus.FWORK_ORDER_CRAFT_ID))
                                    .Where((sch, woCraftStatus) => woCraftIds.Contains(woCraftStatus.FWORK_ORDER_CRAFT_ID))
                                    .Select((sch, woCraftStatus) => new { sch.FWORK_ORDER_CRAFT_ID, woCraftStatus.FWORK_ORDER_CRAFT_STATUS_ID, sch.FPLAN_QTY }).ToListAsync();


            var sumPlans = (from item in craftPlanQtys
                            group item by new { item.FWORK_ORDER_CRAFT_STATUS_ID } into g
                            select new T_MESD_WORK_ORDER_CRAFT_STATUS
                            {
                                FWORK_ORDER_CRAFT_STATUS_ID = g.Key.FWORK_ORDER_CRAFT_STATUS_ID,
                                FPLAN_QTY = g.Sum(x => x.FPLAN_QTY),
                            }).ToList();

            var woCraftItems = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_CRAFT_STATUS>
                                ((wo, woCraftStatus) => new JoinQueryInfos(JoinType.Left, wo.FWORK_ORDER_ID == woCraftStatus.FWORK_ORDER_ID))
                                .Where((wo, woCraftStatus) => woCraftIds.Contains(woCraftStatus.FWORK_ORDER_CRAFT_ID))
                                .Select((wo, woCraftStatus) => new
                                {
                                    wo.FWORK_ORDER_ID,
                                    woCraftStatus.FWORK_ORDER_CRAFT_ID,
                                    woCraftStatus.FWORK_ORDER_CRAFT_STATUS_ID,
                                })
                                .ToListAsync();

            //加入不存在于合计列表的数据
            var excludes = woCraftItems.Where(craft => !sumPlans.Any(plan => craft.FWORK_ORDER_CRAFT_STATUS_ID == plan.FWORK_ORDER_CRAFT_STATUS_ID)).ToList();
            if (excludes.Count > 0)
            {
                excludes.ForEach(item =>
                {
                    sumPlans.Add(new T_MESD_WORK_ORDER_CRAFT_STATUS
                    {
                        FWORK_ORDER_CRAFT_STATUS_ID = item.FWORK_ORDER_CRAFT_ID,
                        FPLAN_QTY = 0,
                    });
                    ;
                });
            }
            //更新已排数量
            await db.Updateable<T_MESD_WORK_ORDER_CRAFT_STATUS>(sumPlans).UpdateColumns(p => new { p.FPLAN_QTY }).ExecuteCommandAsync();

            //取出排程数量
            var woIds = woCraftItems.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();
            if (woIds.Count > 0)
            {
                //更新排程状态
                await UpdateWorkOrderScheduleStatusAsync(woIds, db);
            }

            //更新已下发数
            if (synRelease)
            {
                await UpdateWorkOrderCraftReleaseQtyAsync(woCraftIds, db);
            }

        }

        /// <summary>
        /// 更新工单排程状态
        /// </summary>
        /// <param name="scheduleIds"></param>
        /// <returns></returns>
        private async Task UpdateWorkOrderScheduleStatusAsync(List<string> woIds, SqlSugarClient db)
        {

            var woCraftStatus = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT_STATUS>
                ((wo, woStatus, woCraftStatus) => new JoinQueryInfos(
                        JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                        JoinType.Left, wo.FWORK_ORDER_ID == woCraftStatus.FWORK_ORDER_ID))
                .Where((wo, woStatus, woCraftStatus) => woIds.Contains(wo.FWORK_ORDER_ID))
                .Select((wo, woStatus, woCraftStatus) => new
                {
                    wo.FPRO_QTY,
                    woCraftStatus.FPLAN_QTY,
                    woCraftStatus.FWORK_ORDER_CRAFT_ID,
                    wo.FWORK_ORDER_ID,
                    woStatus.FWORK_ORDER_STATUS_ID
                })
                .ToListAsync();

            System.Collections.Concurrent.ConcurrentBag<T_MESD_WORK_ORDER_STATUS> daoWoStatus = new System.Collections.Concurrent.ConcurrentBag<T_MESD_WORK_ORDER_STATUS>();

            var woCraftDic = woCraftStatus.GroupBy(x => x.FWORK_ORDER_STATUS_ID).ToDictionary(group => group.Key, group => group.ToList());
            woCraftDic.AsParallel().WithDegreeOfParallelism(4).ForAll(pv =>
            {
                T_MESD_WORK_ORDER_STATUS dao = new T_MESD_WORK_ORDER_STATUS() { FWORK_ORDER_STATUS_ID = pv.Key };
                if (!pv.Value.Any(p => p.FPRO_QTY > p.FPLAN_QTY))
                {
                    //已排程
                    dao.FSCHEDULE_STATUS = 1;
                }
                else if (pv.Value.Any(p => p.FPRO_QTY > p.FPLAN_QTY) && pv.Value.Any(p => p.FPRO_QTY == p.FPLAN_QTY))
                {
                    //已部份排程
                    dao.FSCHEDULE_STATUS = 2;
                }
                else
                {
                    //未排程
                    dao.FSCHEDULE_STATUS = 0;
                }
                daoWoStatus.Add(dao);
            });
            //更新排程状态
            await db.Updateable<T_MESD_WORK_ORDER_STATUS>(daoWoStatus.ToList()).UpdateColumns(p => new { p.FSCHEDULE_STATUS }).ExecuteCommandAsync();
        }

        /// <summary>
        /// 更新工单工艺已下发数量
        /// </summary>
        /// <param name="scheduleIds"></param>
        /// <returns></returns>
        private async Task UpdateWorkOrderCraftReleaseQtyAsync(List<string> woCraftIds, SqlSugarClient db)
        {
            //更新已下发数
            var craftReleaseQtys = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER_CRAFT_STATUS>
                                             ((sch, schStatus, woCraftStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                                             JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == woCraftStatus.FWORK_ORDER_CRAFT_ID))
                                     .Where((sch, schStatus, woCraftStatus) => woCraftIds.Contains(sch.FWORK_ORDER_CRAFT_ID) && schStatus.FRELEASE_STATUS == true)
                                     .Select((sch, schStatus, woCraftStatus) => new { woCraftStatus.FWORK_ORDER_CRAFT_STATUS_ID, sch.FPLAN_QTY })
                                     .ToListAsync();

            var sumReleases = (from item in craftReleaseQtys
                               group item by new { item.FWORK_ORDER_CRAFT_STATUS_ID } into g
                               select new T_MESD_WORK_ORDER_CRAFT_STATUS
                               {
                                   FWORK_ORDER_CRAFT_STATUS_ID = g.Key.FWORK_ORDER_CRAFT_STATUS_ID,
                                   FRELEASE_QTY = g.Sum(x => x.FPLAN_QTY),
                               }).ToList();

            List<T_MESD_WORK_ORDER_CRAFT_STATUS> daoCraftStatus = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>().Where(p => woCraftIds.Contains(p.FWORK_ORDER_CRAFT_ID))
                .Select(p => new T_MESD_WORK_ORDER_CRAFT_STATUS
                {
                    FWORK_ORDER_CRAFT_ID = p.FWORK_ORDER_CRAFT_ID,
                    FWORK_ORDER_CRAFT_STATUS_ID = p.FWORK_ORDER_CRAFT_STATUS_ID,
                    FRELEASE_QTY = 0
                }).ToListAsync();

            if (sumReleases != null && sumReleases.Count > 0)
            {
                daoCraftStatus.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                {
                    var sumRelease = sumReleases.FirstOrDefault(item => item.FWORK_ORDER_CRAFT_STATUS_ID == craft.FWORK_ORDER_CRAFT_STATUS_ID);
                    if (sumRelease != null)
                    {
                        craft.FRELEASE_QTY = sumRelease.FRELEASE_QTY;
                    }
                });
            }

            if ((daoCraftStatus == null ? 0 : daoCraftStatus.Count) != woCraftIds.Count)
            {
                ERROR(null, 109001, _multiLang["更新工单工艺记录数与传入参数记录数不一致"]);
            }
            await db.Updateable<T_MESD_WORK_ORDER_CRAFT_STATUS>(daoCraftStatus).UpdateColumns(p => new { p.FRELEASE_QTY }).ExecuteCommandAsync();

        }
        #endregion


        #region 产生任务
        /// <summary>
        /// 根据工单id产生排程任务
        /// </summary>
        /// <param name="woIds"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> GenScheduleTaskByWoIdsAsync(List<string> woIds)
        {
            if (woIds == null || woIds.Count == 0)
            {
                ERROR(null, 100010, String.Format(_multiLang["传入参数 {0} 为空"], "woIds"));
            }

            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var woData = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((wo, wostatus) => new JoinQueryInfos(JoinType.Left, wo.FWORK_ORDER_ID == wostatus.FWORK_ORDER_ID))
                .Where((wo, wostatus) => woIds.Contains(wo.FWORK_ORDER_ID))
                .Select((wo, wostatus) => new { wo.FWORK_ORDER_ID, wostatus.FCFLAG, wostatus.FIF_CANCEL, wostatus.FIF_CLOSE, wostatus.FSCHEDULE_STATUS, wo.FWORK_ORDER_NO })
                .ToListAsync();

            var errorData = woData.Where(p => p.FIF_CANCEL).FirstOrDefault();
            if (errorData != null)
            {
                ERROR(null, 100020, String.Format(_multiLang["工单 {0} 已作废."], errorData.FWORK_ORDER_NO));
            }

            errorData = woData.Where(p => p.FIF_CLOSE == 1 || p.FIF_CLOSE == 3).FirstOrDefault();
            if (errorData != null)
            {
                ERROR(null, 100030, String.Format(_multiLang["工单 {0} 已结案."], errorData.FWORK_ORDER_NO));
            }

            errorData = woData.Where(p => p.FCFLAG != 1).FirstOrDefault();
            if (errorData != null)
            {
                ERROR(null, 100040, String.Format(_multiLang["工单 {0} 未审核通过."], errorData.FWORK_ORDER_NO));
            }


            errorData = woData.Where(p => p.FSCHEDULE_STATUS == 1).FirstOrDefault();
            if (errorData != null)
            {
                ERROR(null, 100050, String.Format(_multiLang["工单 {0} 已排程."], errorData.FWORK_ORDER_NO));
            }

            var errWoId = woIds.Where(p => !woData.Any(wo => wo.FWORK_ORDER_ID == p)).FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(errWoId))
            {
                ERROR(null, 100060, String.Format(_multiLang["工单Id {0} 数据已不存在, 请重新查询再试."], errWoId));
            }

            //查询工单工艺
            var query = new QueryRequestModel
            {
                WhereGroup = new QueryWhereGroupModel
                {
                    Items = new List<QueryWhereItemModel> {
                      new QueryWhereItemModel {
                        FieldName="wo.FWORK_ORDER_ID",
                        OperatorType=Core.HCPlatform.Utilities.EnumQuerySymbol.In,
                        Value=string.Join(",",woIds),
                      }
                    },
                    GroupType = EnumGroupType.AND,
                    Groups = new List<QueryWhereGroupModel> { }
                },
                PageIndex = 1,
                PageSize = int.MaxValue,
            };
            var woCraftResult = await QueryWaitScheduleAsync(query);

            if (woCraftResult.StatusCode != 200)
            {
                ERROR(woCraftResult, woCraftResult.StatusCode, woCraftResult.Message);
            }

            //检查工单是否有工艺数据
            errorData = woData.Where(p => !woCraftResult.Entity.WOCraftModels.Any(craft => craft.FWORK_ORDER_ID == p.FWORK_ORDER_ID)).FirstOrDefault();
            if (errorData != null)
            {
                ERROR(null, 100070, String.Format(_multiLang["工单 {0} 不存在工艺明细数据."], errorData.FWORK_ORDER_NO));
            }

            //检查工单工艺是否有工位数据
            var craftErrorData = woCraftResult.Entity.WOCraftModels.Where(p => string.IsNullOrWhiteSpace(p.FSTATION_ID)).FirstOrDefault();
            if (craftErrorData != null)
            {

                ERROR(null, 100080, String.Format(_multiLang["工单 {0} , 工艺 {1} 加工工位为空."], craftErrorData.FWORK_ORDER_NO, craftErrorData.FCRAFT_NAME));
            }

            //生成工艺排程数据
            woCraftResult.Entity.IsReleaseTask = true;
            var genResult = await GenScheduleTaskAsync(woCraftResult.Entity);
            if (genResult.StatusCode != 200)
            {
                ERROR(genResult, genResult.StatusCode, genResult.Message);
            }

            //返回工单数据
            var rpcServer = this.GetService<IModuleServices.MES003_WorkOrder.IMES003WorkOrderService>("MES003WorkOrder");
            QueryRequestModel requestModel = new QueryRequestModel
            {
                PageIndex = 1,
                PageSize = int.MaxValue,
                WhereGroup = new QueryWhereGroupModel
                {
                    Items = new List<QueryWhereItemModel> {
                    new QueryWhereItemModel{
                        FieldName="a.FWORK_ORDER_ID",
                        OperatorType=Core.HCPlatform.Utilities.EnumQuerySymbol.In,
                        Value=string.Join(",",woIds),
                    }
                    }
                }
            };
            var woResult = await rpcServer.GetAllAsync(requestModel);

            if (woResult.StatusCode != 200)
            {
                ERROR(woResult, woResult.StatusCode, woResult.Message);
            }

            var result = new
            {
                FSCHEDULE_BATCH_ID = genResult.Entity,
                WOData = woResult.Entity,
            };

            DataResult<dynamic> dataResult = new DataResult<dynamic>
            {
                StatusCode = 200,
                Entity = result,
            };

            return await OK(dataResult);
        }

        /// <summary>
        /// 产生排程任务,返回排程批次Id
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<string>> GenScheduleTaskAsync(ScheduleModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            if (model == null)
            {
                ERROR(null, 100100, string.Format(_multiLang["执行失败,传入参数 {0} 为null."], "model"));
            }

            if (model.WOCraftModels == null || model.WOCraftModels.Count == 0)
            {
                ERROR(null, 100101, string.Format(_multiLang["执行失败,传入参数 {0} 为null."], "model.WOCraftModels"));
            }

            //更新下发状态
            if (model.IsReleaseTask)
            {
                model.WOCraftModels.AsParallel().ForAll(craftModel =>
                {
                    craftModel.FRELEASE_STATUS = true;
                    craftModel.FRELEASER = user.UserPsnName;
                    craftModel.FRELEASER_ID = user.UserPsnId;
                    craftModel.FRELEASE_DATE = _iauth.GetCurDateTime();
                });
            }

            //验证排程数量
            if (!await ValidateAndPatchTaskOnGenAsync(model))
            {
                ERROR(null, 100104, _multiLang["验证排程数量发生错误."]);
            }

            var craftModels = model.WOCraftModels;
            // 若存在变更中(已建变更单未审核）的生产工单，不能产生。
            var db = _isugar.DB;
            var lstWorkOrderId = craftModels.Where(a => !string.IsNullOrWhiteSpace(a.FWORK_ORDER_ID))
                .Select(a => a.FWORK_ORDER_ID).Distinct().ToList();
            if (lstWorkOrderId != null && lstWorkOrderId.Count > 0)
            {
                var lstChange = await db.Queryable<T_MESD_WORK_ORDER_CHANGE, T_MESD_WORK_ORDER_CHANGE_STATUS>(
                    (a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_CHANGE_ID == b.FWORK_ORDER_CHANGE_ID))
                    .Where((a, b) => !b.FIF_CANCEL && (b.FCFLAG == 0 || b.FCFLAG == 2) && lstWorkOrderId.Contains(a.FWORK_ORDER_ID))
                    .Select<WorkOrderChangeModel>().ToListAsync();
                if (lstChange != null && lstChange.Count > 0)
                {
                    var change = lstChange[0];
                    var order = await db.Queryable<T_MESD_WORK_ORDER>().InSingleAsync(change.FWORK_ORDER_ID);
                    ERROR(null, 111111,
                        _multiLang[$"生产工单(编号:{order.FWORK_ORDER_NO})已被生产变更单(编号:{change.FWORK_ORDER_CHANGE_NO})引用,不能产生排程任务."]);
                }
            }
            //取出任务单号
            var billCodes = await GetBillCodesAsync(craftModels.Count);
            if (billCodes.Count != craftModels.Count)
            {
                ERROR(null, 100102, string.Format(_multiLang["执行失败,产生任务编号个数不正确,应为 {0} 个,只产生了{1}个."], craftModels.Count, billCodes.Count));
            }

            //排程批次Id
            string scheduleBatchId = GuidHelper.NewGuid();
            for (int i = 0; i < craftModels.Count; i++)
            {
                craftModels[i].FCRAFT_SCHEDULE_NO = billCodes[i];
                craftModels[i].FSCHEDULE_BATCH_ID = scheduleBatchId;
                craftModels[i].FORI_PLAN_QTY = craftModels[i].FPLAN_QTY;  //原下发数量

                craftModels[i].FQRCODE = craftModels[i].FCRAFT_SCHEDULE_NO;   //二维码
            }

            //负责人
            System.Collections.Concurrent.ConcurrentBag<WOCraftSchedulePersonModel> persons = new System.Collections.Concurrent.ConcurrentBag<WOCraftSchedulePersonModel>();
            craftModels.Where(p => p.Persons != null && p.Persons.Count > 0).AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
            {
                craft.Persons.ForEach(person =>
                {
                    person.FCRAFT_SCHEDULE_ID = craft.FCRAFT_SCHEDULE_ID;
                    person.FCRAFT_SCHEDULE_PERSON_ID = GuidHelper.NewGuid();
                    persons.Add(person);
                });
            });

            //排程任务dao
            List<T_MESD_CRAFT_SCHEDULE> daoSchedule = craftModels.MapToDestObj<WOCraftScheduleModel, T_MESD_CRAFT_SCHEDULE>();

            //排程任务状态dao
            List<T_MESD_CRAFT_SCHEDULE_STATUS> daoStatus = craftModels.MapToDestObj<WOCraftScheduleModel, T_MESD_CRAFT_SCHEDULE_STATUS>();
            daoStatus.AsParallel().WithDegreeOfParallelism(4).ForAll(dao =>
            {
                dao.FCRAFT_SCHEDULE_STATUS_ID = GuidHelper.NewGuid();
            });
            //负责人dao
            List<T_MESD_CRAFT_SCHEDULE_PERSON> daoPersons = persons.ToList().MapToDestObj<WOCraftSchedulePersonModel, T_MESD_CRAFT_SCHEDULE_PERSON>();


            try
            {
                db.BeginTran();

                //写入数据表
                await db.Insertable<T_MESD_CRAFT_SCHEDULE>(daoSchedule).ExecuteCommandAsync();
                await db.Insertable<T_MESD_CRAFT_SCHEDULE_STATUS>(daoStatus).ExecuteCommandAsync();
                await db.Insertable<T_MESD_CRAFT_SCHEDULE_PERSON>(daoPersons).ExecuteCommandAsync();

                //反写工单工艺已排数
                await UpdateWorkOrderCraftPlanQtyAsync(craftModels.Select(p => p.FWORK_ORDER_CRAFT_ID).Distinct().ToList(), model.IsReleaseTask, db);

                db.CommitTran();
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }
            DataResult<string> dataResult = new DataResult<string>()
            {
                Entity = scheduleBatchId,
                StatusCode = 200,
            };

            if (model.IsReleaseTask)
            {
                UpdateNewScheduleAsync(craftModels.Select(p => p.FCRAFT_SCHEDULE_ID).ToList());
            }


            return await OK(dataResult);

        }

        /// <summary>
        /// 手工排程  --排序工位下 得排程顺序
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public async Task<DataResult<bool>> ManScheduleTaskAsync(List<ManualScheduling> model)
        {
            if (model.Count == 0)
            {
                ERROR(model, 111111, $"未找到排程信息");
            }
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var schTask = model.MapToDestObj<ManualScheduling, T_MESD_CRAFT_SCHEDULE>();

            await db.Updateable<T_MESD_CRAFT_SCHEDULE>(schTask).UpdateColumns(p => new { p.SCHEDULE_FSHOW_SEQNO }).ExecuteCommandAsync();

            var datasch = model.FirstOrDefault();
            var schid = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().Where(p => p.FCRAFT_SCHEDULE_ID == datasch.FCRAFT_SCHEDULE_ID).FirstAsync();


            //下发到排程工位机
            if (JudgeNewSchedule.ContainsKey(schid.FSTATION_ID))
            {
                JudgeNewSchedule[schid.FSTATION_ID] = true;
            }
            else
            {
                JudgeNewSchedule.Add(schid.FSTATION_ID, true);
            }



            return new DataResult<bool>() { Entity = true, StatusCode = 200 };

        }

        /// <summary>
        /// 验证排程任务,并加入差异值(在产生任务时调用)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<bool> ValidateAndPatchTaskOnGenAsync(ScheduleModel model)
        {

            //验证工单是否有效
            if (!await ValidateWorderOrderIsValidAsync(model.WOCraftModels.Select(p => p.FWORK_ORDER_ID).ToList()))
            {
                ERROR(null, 106011, _multiLang["验证工单状态失败."]);
            }

            //
            var craftPlanItems = (from item in model.WOCraftModels
                                  group item by new { item.FWORK_ORDER_CRAFT_ID, item.FCRAFT_NAME, item.FWORK_ORDER_NO } into g
                                  select new
                                  {
                                      FWORK_ORDER_CRAFT_ID = g.Key.FWORK_ORDER_CRAFT_ID,
                                      FCRAFT_NAME = g.Key.FCRAFT_NAME,
                                      FWORK_ORDER_NO = g.Key.FWORK_ORDER_NO,
                                      FPLAN_QTY = g.Sum(x => x.FPLAN_QTY),
                                  }).ToList();

            var woCraftIds = craftPlanItems.Select(p => p.FWORK_ORDER_CRAFT_ID).ToList();

            var db = _isugar.DB;
            var craftAllowItems = await db.Queryable<T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS, T_MESD_WORK_ORDER>
                ((woCraft, woCraftStatus, wo) => new JoinQueryInfos(JoinType.Left, woCraft.FWORK_ORDER_CRAFT_ID == woCraftStatus.FWORK_ORDER_CRAFT_ID,
                                        JoinType.Left, woCraft.FWORK_ORDER_ID == wo.FWORK_ORDER_ID))
                .Where((woCraft, woCraftStatus, wo) => woCraftIds.Contains(woCraft.FWORK_ORDER_CRAFT_ID))
                .Select((woCraft, woCraftStatus, wo) => new { woCraft.FWORK_ORDER_CRAFT_ID, FPLAN_QTY = wo.FPRO_QTY - woCraftStatus.FPLAN_QTY })
                .ToListAsync();


            /*
            //检查是否超出数量
            var moreItems = (from planItem in craftPlanItems
                             join allowItem in craftAllowItems on planItem.FWORK_ORDER_CRAFT_ID equals allowItem.FWORK_ORDER_CRAFT_ID
                             where planItem.FPLAN_QTY > allowItem.FPLAN_QTY
                             select new { planItem.FWORK_ORDER_NO, planItem.FCRAFT_NAME, FDIFF_QTY = planItem.FPLAN_QTY - allowItem.FPLAN_QTY }
                           ).ToList();

            if (moreItems.Count > 0)
            {
                ERROR(null, 100103, string.Format(_multiLang["工单{0},工艺{1},计划产出数量超出{2}"],
                                        moreItems[0].FWORK_ORDER_NO, moreItems[0].FCRAFT_NAME, moreItems[0].FDIFF_QTY.ToString("#,##0.######")));
            }
            */

            //把未排满的数量加入计划，状态为待下发
            var lessItems = (from planItem in craftPlanItems
                             join allowItem in craftAllowItems on planItem.FWORK_ORDER_CRAFT_ID equals allowItem.FWORK_ORDER_CRAFT_ID
                             where planItem.FPLAN_QTY < allowItem.FPLAN_QTY
                             select new { planItem.FWORK_ORDER_CRAFT_ID, FDIFF_QTY = allowItem.FPLAN_QTY - planItem.FPLAN_QTY }
                        ).ToList();
            
            if (model.IsAutoCreateRemainder && lessItems.Count > 0)
            {
                lessItems.ForEach(item =>
                {
                    var firstCraft = model.WOCraftModels.FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == item.FWORK_ORDER_CRAFT_ID);

                    var cloneCraft = (WOCraftScheduleModel)_serializer.Deserialize(_serializer.Serialize(firstCraft), typeof(WOCraftScheduleModel));

                    cloneCraft.FRELEASE_STATUS = false;   //待下发状态
                    cloneCraft.FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid();
                    cloneCraft.FPLAN_QTY = item.FDIFF_QTY;
                    model.WOCraftModels.Add(cloneCraft);

                });
            }
            return true;
        }
        #endregion


        #region （mes）兆科修改保存排程任务
        /// <summary>
        /// 修改保存排程任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WOCraftScheduleModel>>> SaveTaskZKAsync(ScheduleModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            if (model == null)
            {
                ERROR(null, 105100, string.Format(_multiLang["执行失败,传入参数 {0} 为null."], "model"));
            }

            if (model.EditModel == null)
            {
                ERROR(null, 105101, string.Format(_multiLang["执行失败,传入参数 {0} 为null."], "model.EditModel"));
            }

            if (model.EditType != ScheduleEditType.EditPlan && model.EditType != ScheduleEditType.EditRelease)
            {
                ERROR(null, 105102, string.Format(_multiLang["执行失败,传入参数编辑类型{0}不支持, 只接受EditPlan/EditRelease."], "model.EditType"));
            }

            //更新下发状态
            if (model.EditType == ScheduleEditType.EditPlan && model.IsReleaseTask)
            {
                model.EditModel.FRELEASE_STATUS = true;
                model.EditModel.FRELEASER = user.UserPsnName;
                model.EditModel.FRELEASER_ID = user.UserPsnId;
                model.EditModel.FRELEASE_DATE = _iauth.GetCurDateTime();
            }

            //验证排程数量
            var validateResult = await ZKValidateAndPatchTaskOnEditSaveAsync(model);
            if (!validateResult.Item1)
            {
                ERROR(null, 106104, _multiLang["验证排程数量发生错误."]);
            }

            //验证是否更换了工位
            if (model.EditType == ScheduleEditType.EditPlan && model.IsReleaseTask)
            {
                var db1 = _isugar.DB;
                var oldStationId = await db1.Queryable<T_MESD_CRAFT_SCHEDULE>().Where(p => p.FCRAFT_SCHEDULE_ID == model.EditModel.FCRAFT_SCHEDULE_ID)
                    .Select(p => p.FSTATION_ID)
                    .FirstAsync();
                if (oldStationId != model.EditModel.FSTATION_ID)
                {

                    //排程任务是否有加工中的任务
                    var workingData = await db1.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE>
                       ((job, sch) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID))
                       .Where((job, sch) => job.FCRAFT_SCHEDULE_ID == model.EditModel.FCRAFT_SCHEDULE_ID &&
                               (job.FWORK_STATUS == WorkStatus.working.ToString() ||
                               job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                               job.FWORK_STATUS == WorkStatus.paused.ToString()))
                       .Select((job, sch) => new { job.FCRAFT_JOB_BOOKING_NO })
                       .FirstAsync();
                    if (workingData != null)
                    {
                        ERROR(null, 107103, string.Format(_multiLang["执行失败, 存在加工中任务 {0} ,不能更改工位."], workingData.FCRAFT_JOB_BOOKING_NO));
                    }
                }
            }


            //排程任务dao更新
            T_MESD_CRAFT_SCHEDULE daoSchedule_Upd = model.EditModel.MapToDestObj<WOCraftScheduleModel, T_MESD_CRAFT_SCHEDULE>();

            //排程任务状态dao更新
            T_MESD_CRAFT_SCHEDULE_STATUS daoStatus_Upd = model.EditModel.MapToDestObj<WOCraftScheduleModel, T_MESD_CRAFT_SCHEDULE_STATUS>();

            //负责人dao
            List<T_MESD_CRAFT_SCHEDULE_PERSON> daoPersons_Ins = model.EditModel.Persons.MapToDestObj<WOCraftSchedulePersonModel, T_MESD_CRAFT_SCHEDULE_PERSON>();
            daoPersons_Ins.AsParallel().WithDegreeOfParallelism(4).ForAll(person =>
            {
                if (string.IsNullOrWhiteSpace(person.FCRAFT_SCHEDULE_ID))
                {
                    person.FCRAFT_SCHEDULE_ID = model.EditModel.FCRAFT_SCHEDULE_ID;
                }
                if (string.IsNullOrWhiteSpace(person.FCRAFT_SCHEDULE_PERSON_ID))
                {
                    person.FCRAFT_SCHEDULE_PERSON_ID = GuidHelper.NewGuid();
                }
            });


            //排程任务dao插入
            T_MESD_CRAFT_SCHEDULE daoSchedule_Ins = null;

            //排程任务状态dao插入
            T_MESD_CRAFT_SCHEDULE_STATUS daoStatus_Ins = null;

            WOCraftScheduleModel newItem = null;
            if (validateResult.Item2 != null && validateResult.Item2 != null)
            {
                newItem = validateResult.Item2;
                //产生任务单号
                var billCodes = await GetBillCodesAsync(1);
                if (billCodes.Count != 1)
                {
                    ERROR(null, 100102, string.Format(_multiLang["执行失败,产生任务编号个数不正确,应为 {0} 个,只产生了{1}个."], 1, billCodes.Count));
                }

                newItem.FCRAFT_SCHEDULE_NO = billCodes[0];
                newItem.FFINISH_QTY = 0;
                newItem.FACT_ST_DATE = null;
                newItem.FACT_ED_DATE = null;
                newItem.FACT_USE_HOUR = 0;
                //二维码
                newItem.FQRCODE = newItem.FCRAFT_SCHEDULE_NO;

                newItem.FSCHEDULE_BATCH_ID = model.EditModel.FSCHEDULE_BATCH_ID;
                newItem.FORI_PLAN_QTY = newItem.FPLAN_QTY;  //原下发数量

                daoSchedule_Ins = newItem.MapToDestObj<WOCraftScheduleModel, T_MESD_CRAFT_SCHEDULE>();
                daoStatus_Ins = newItem.MapToDestObj<WOCraftScheduleModel, T_MESD_CRAFT_SCHEDULE_STATUS>();

                //负责人
                System.Collections.Concurrent.ConcurrentBag<WOCraftSchedulePersonModel> persons =
                    new System.Collections.Concurrent.ConcurrentBag<WOCraftSchedulePersonModel>();

                if (newItem.Persons != null && newItem.Persons.Count > 0)
                {
                    newItem.Persons.ForEach(person =>
                    {
                        person.FCRAFT_SCHEDULE_ID = newItem.FCRAFT_SCHEDULE_ID;
                        person.FCRAFT_SCHEDULE_PERSON_ID = GuidHelper.NewGuid();
                        persons.Add(person);
                    });
                    daoPersons_Ins.AddRange(newItem.Persons.MapToDestObj<WOCraftSchedulePersonModel, T_MESD_CRAFT_SCHEDULE_PERSON>());
                }
            }
            var db = _isugar.DB;

            // 收集需要分配新批号的排程任务
            var schedulesToAssignLotNo = new List<T_MESD_CRAFT_SCHEDULE>();

            // 如果被修改的排程任务没有批号，则将其加入列表等待分配
            if (string.IsNullOrWhiteSpace(daoSchedule_Upd.FLOT_NO))
            {
                schedulesToAssignLotNo.Add(daoSchedule_Upd);
            }

            // 如果存在拆分出的新排程任务，它永远需要一个新的批号
            if (daoSchedule_Ins != null)
            {
                schedulesToAssignLotNo.Add(daoSchedule_Ins);
            }

            // 如果列表不为空，则调用辅助方法批量生成并分配唯一批号
            if (schedulesToAssignLotNo.Any())
            {
                await GenerateNewLotNumbersAsync(db,
                    daoSchedule_Upd.FWORK_ORDER_ID,
                    daoSchedule_Upd.FWORK_ORDER_CRAFT_ID,
                    schedulesToAssignLotNo.ToArray());
            }


            try
            {
                db.BeginTran();

                //更新数据表                
                await db.Updateable<T_MESD_CRAFT_SCHEDULE>(daoSchedule_Upd).IgnoreColumns(t => new
                {
                    t.FCDATE,
                    t.FCREATOR_ID,
                    t.FCREATOR
                }).ExecuteCommandAsync();

                await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daoStatus_Upd).UpdateColumns(t => new
                {
                    t.FRELEASER,
                    t.FRELEASER_ID,
                    t.FRELEASE_DATE,
                    t.FRELEASE_STATUS
                }).ExecuteCommandAsync();

                await db.Deleteable<T_MESD_CRAFT_SCHEDULE_PERSON>()
                    .Where(t => t.FCRAFT_SCHEDULE_ID == model.EditModel.FCRAFT_SCHEDULE_ID)
                    .ExecuteCommandAsync();

                if (daoSchedule_Ins != null)
                {
                    await db.Insertable<T_MESD_CRAFT_SCHEDULE>(daoSchedule_Ins).ExecuteCommandAsync();
                }

                if (daoStatus_Ins != null)
                {
                    await db.Insertable<T_MESD_CRAFT_SCHEDULE_STATUS>(daoStatus_Ins).ExecuteCommandAsync();
                }

                if (daoPersons_Ins != null && daoPersons_Ins.Count > 0)
                {
                    await db.Insertable<T_MESD_CRAFT_SCHEDULE_PERSON>(daoPersons_Ins).ExecuteCommandAsync();
                }

                //反写工单工艺已排数
                await UpdateWorkOrderCraftPlanQtyAsync(
                    new List<string> { model.EditModel.FWORK_ORDER_CRAFT_ID },
                    model.IsReleaseTask && model.EditType == ScheduleEditType.EditPlan,
                    db
                    );

                db.CommitTran();
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }

            //
            List<string> schIds = new List<string>() { model.EditModel.FCRAFT_SCHEDULE_ID };
            if (newItem != null)
            {
                schIds.Add(newItem.FCRAFT_SCHEDULE_ID);
            }
            //

            //返回排程任务数据
            QueryRequestModel request = new QueryRequestModel()
            {
                PageIndex = 1,
                PageSize = 10000,
                WhereGroup = new QueryWhereGroupModel()
                {
                    Items = new List<QueryWhereItemModel>(),
                    GroupType = EnumGroupType.AND,
                }
            };
            request.WhereGroup.Items.Add(new QueryWhereItemModel
            {
                FieldName = "sch.FCRAFT_SCHEDULE_ID",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.In,
                Value = string.Join(",", schIds)
            });
            //更新下发状态
            if (model.EditType == ScheduleEditType.EditPlan && model.IsReleaseTask)
            {
                UpdateNewScheduleAsync(schIds);
            }
            return await QueryScheduleTaskAsync(request);


        }


        /// <summary>
        /// 验证排程任务,并加入差异值(修改保存任务时调用)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<(bool, WOCraftScheduleModel)> ZKValidateAndPatchTaskOnEditSaveAsync(ScheduleModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            WOCraftScheduleModel newItem = null;
            var db = _isugar.DB;

            List<WOCraftScheduleModel> woCraftModels =
            await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_CRAFT_STATUS>
                ((sch, schStatus, wo, craftStatus) => new JoinQueryInfos(
                    JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                    JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                    JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
            .Where((sch, schStatus, wo, craftStatus) => sch.FWORK_ORDER_CRAFT_ID == model.EditModel.FWORK_ORDER_CRAFT_ID)
            .Select((sch, schStatus, wo, craftStatus) => new WOCraftScheduleModel()
            {
                FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,
                FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                FPLAN_QTY = sch.FPLAN_QTY,
                FRECV_QTY = craftStatus.FRECV_QTY,
                FFINISH_QTY = craftStatus.FFINISH_QTY,
                FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                FPRO_QTY = wo.FPRO_QTY,
                FIF_CLOSE = schStatus.FIF_CLOSE,
                FWORK_ORDER_ID = sch.FWORK_ORDER_ID
            })
            .ToListAsync();

            //
            var oriModel = woCraftModels.FirstOrDefault(p => p.FCRAFT_SCHEDULE_ID == model.EditModel.FCRAFT_SCHEDULE_ID);
            if (oriModel == null)
            {
                ERROR(null, 106001, string.Format(_multiLang["执行失败, 任务编号{0}已失效."], model.EditModel.FCRAFT_SCHEDULE_NO));
            }

            //验证任务是否已结案
            if (oriModel.IsEndClose)
            {
                ERROR(null, 106010, string.Format(_multiLang["执行失败, 任务编号{0}已结案."], model.EditModel.FCRAFT_SCHEDULE_NO));
            }

            if (model.EditType == ScheduleEditType.EditPlan && oriModel.FRELEASE_STATUS)
            {
                ERROR(null, 106014, string.Format(_multiLang["执行失败, 任务编号{0}已下发."], model.EditModel.FCRAFT_SCHEDULE_NO));
            }

            //验证工单是否有效
            if (!await ValidateWorderOrderIsValidAsync(new List<string>() { oriModel.FWORK_ORDER_ID }))
            {
                ERROR(null, 106011, _multiLang["验证工单状态失败."]);
            }

            //
            oriModel.FPLAN_QTY = model.EditModel.FPLAN_QTY;

            //求汇总数
            var totalPlanQty = woCraftModels.Sum(p => p.FPLAN_QTY);

            //验证是否超出工单计划数
            //if (totalPlanQty > oriModel.FPRO_QTY)
            //{
            //    ERROR(null, 106002, string.Format(_multiLang["工单{0},工艺{1},计划产出数量比工单生产数量超出{2}"],
            //                         model.EditModel.FWORK_ORDER_NO,
            //                         model.EditModel.FCRAFT_NAME,
            //                         (totalPlanQty - oriModel.FPRO_QTY).ToString("#,##0.######")));
            //}

            //验证是否小于已接收数
            //if (totalPlanQty < oriModel.FRECV_QTY)
            //{
            //    ERROR(null, 106003, string.Format(_multiLang["工单{0},工艺{1},计划产出数量比接收数超出{2}"],
            //                         model.EditModel.FWORK_ORDER_NO,
            //                         model.EditModel.FCRAFT_NAME,
            //                         (oriModel.FRECV_QTY - totalPlanQty).ToString("#,##0.######")));
            //}

            if (totalPlanQty < oriModel.FFINISH_QTY)
            {
                ERROR(null, 106003, string.Format(_multiLang["工单{0},工艺{1},计划产出数量少于完工数 {2}"],
                                     model.EditModel.FWORK_ORDER_NO,
                                     model.EditModel.FCRAFT_NAME,
                                     (oriModel.FFINISH_QTY - totalPlanQty).ToString("#,##0.######")));
            }


            //把未排满的数量加入计划，状态为待下发
            if (totalPlanQty < oriModel.FPRO_QTY)
            {
                var cloneSchCraft = (WOCraftScheduleModel)_serializer.Deserialize(_serializer.Serialize(model.EditModel), typeof(WOCraftScheduleModel));

                cloneSchCraft.FRELEASE_STATUS = false;   //待下发状态

                cloneSchCraft.FRELEASER = user.UserPsnName;
                cloneSchCraft.FRELEASER_ID = user.UserPsnId;
                cloneSchCraft.FRELEASE_DATE = _iauth.GetCurDateTime();

                cloneSchCraft.FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid();
                cloneSchCraft.FCRAFT_SCHEDULE_STATUS_ID = GuidHelper.NewGuid();

                cloneSchCraft.FPLAN_QTY = oriModel.FPRO_QTY - totalPlanQty;
                newItem = cloneSchCraft;
            }

            //
            return (true, newItem);
        }
        #endregion

        #region 修改保存排程任务
        /// <summary>
        /// 修改保存排程任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WOCraftScheduleModel>>> SaveTaskAsync(ScheduleModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            if (model == null)
            {
                ERROR(null, 105100, string.Format(_multiLang["执行失败,传入参数 {0} 为null."], "model"));
            }

            if (model.EditModel == null)
            {
                ERROR(null, 105101, string.Format(_multiLang["执行失败,传入参数 {0} 为null."], "model.EditModel"));
            }

            if (model.EditType != ScheduleEditType.EditPlan && model.EditType != ScheduleEditType.EditRelease)
            {
                ERROR(null, 105102, string.Format(_multiLang["执行失败,传入参数编辑类型{0}不支持, 只接受EditPlan/EditRelease."], "model.EditType"));
            }

            //更新下发状态
            if (model.EditType == ScheduleEditType.EditPlan && model.IsReleaseTask)
            {
                model.EditModel.FRELEASE_STATUS = true;
                model.EditModel.FRELEASER = user.UserPsnName;
                model.EditModel.FRELEASER_ID = user.UserPsnId;
                model.EditModel.FRELEASE_DATE = _iauth.GetCurDateTime();
            }

            //验证排程数量
            var validateResult = await ValidateAndPatchTaskOnEditSaveAsync(model);
            if (!validateResult.Item1)
            {
                ERROR(null, 106104, _multiLang["验证排程数量发生错误."]);
            }

            //验证是否更换了工位
            if (model.EditType == ScheduleEditType.EditPlan && model.IsReleaseTask)
            {
                var db1 = _isugar.DB;
                var oldStationId = await db1.Queryable<T_MESD_CRAFT_SCHEDULE>().Where(p => p.FCRAFT_SCHEDULE_ID == model.EditModel.FCRAFT_SCHEDULE_ID)
                    .Select(p => p.FSTATION_ID)
                    .FirstAsync();
                if (oldStationId != model.EditModel.FSTATION_ID)
                {

                    //排程任务是否有加工中的任务
                    var workingData = await db1.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE>
                       ((job, sch) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID))
                       .Where((job, sch) => job.FCRAFT_SCHEDULE_ID == model.EditModel.FCRAFT_SCHEDULE_ID &&
                               (job.FWORK_STATUS == WorkStatus.working.ToString() ||
                               job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                               job.FWORK_STATUS == WorkStatus.paused.ToString()))
                       .Select((job, sch) => new { job.FCRAFT_JOB_BOOKING_NO })
                       .FirstAsync();
                    if (workingData != null)
                    {
                        ERROR(null, 107103, string.Format(_multiLang["执行失败, 存在加工中任务 {0} ,不能更改工位."], workingData.FCRAFT_JOB_BOOKING_NO));
                    }
                }
            }


            //排程任务dao更新
            T_MESD_CRAFT_SCHEDULE daoSchedule_Upd = model.EditModel.MapToDestObj<WOCraftScheduleModel, T_MESD_CRAFT_SCHEDULE>();

            //排程任务状态dao更新
            T_MESD_CRAFT_SCHEDULE_STATUS daoStatus_Upd = model.EditModel.MapToDestObj<WOCraftScheduleModel, T_MESD_CRAFT_SCHEDULE_STATUS>();

            //负责人dao
            List<T_MESD_CRAFT_SCHEDULE_PERSON> daoPersons_Ins = model.EditModel.Persons.MapToDestObj<WOCraftSchedulePersonModel, T_MESD_CRAFT_SCHEDULE_PERSON>();
            daoPersons_Ins.AsParallel().WithDegreeOfParallelism(4).ForAll(person =>
            {
                if (string.IsNullOrWhiteSpace(person.FCRAFT_SCHEDULE_ID))
                {
                    person.FCRAFT_SCHEDULE_ID = model.EditModel.FCRAFT_SCHEDULE_ID;
                }
                if (string.IsNullOrWhiteSpace(person.FCRAFT_SCHEDULE_PERSON_ID))
                {
                    person.FCRAFT_SCHEDULE_PERSON_ID = GuidHelper.NewGuid();
                }
            });


            //排程任务dao插入
            List<T_MESD_CRAFT_SCHEDULE> daoSchedule_Ins = new List<T_MESD_CRAFT_SCHEDULE>();

            //排程任务状态dao插入
            List<T_MESD_CRAFT_SCHEDULE_STATUS> daoStatus_Ins = new List<T_MESD_CRAFT_SCHEDULE_STATUS>();

            List<WOCraftScheduleModel> newItems = null;
            if (validateResult.Item2 != null && validateResult.Item2.Count != 0)
            {
                newItems = validateResult.Item2;
                //产生任务单号
                var billCodes = await GetBillCodesAsync(newItems.Count);
                if (billCodes.Count != newItems.Count)
                {
                    ERROR(null, 100102, string.Format(_multiLang["执行失败,产生任务编号个数不正确,应为 {0} 个,只产生了{1}个."], 1, billCodes.Count));
                }
                int billCodeIndex = 0;//任务单号索引
                foreach (var newItem in newItems)
                {
                    newItem.FCRAFT_SCHEDULE_NO = billCodes[billCodeIndex];
                    newItem.FFINISH_QTY = 0;
                    newItem.FACT_ST_DATE = null;
                    newItem.FACT_ED_DATE = null;
                    newItem.FACT_USE_HOUR = 0;
                    //二维码
                    newItem.FQRCODE = newItem.FCRAFT_SCHEDULE_NO;

                    newItem.FSCHEDULE_BATCH_ID = model.EditModel.FSCHEDULE_BATCH_ID;
                    newItem.FORI_PLAN_QTY = newItem.FPLAN_QTY;  //原下发数量

                    daoSchedule_Ins.Add(newItem.MapToDestObj<WOCraftScheduleModel, T_MESD_CRAFT_SCHEDULE>());
                    daoStatus_Ins.Add(newItem.MapToDestObj<WOCraftScheduleModel, T_MESD_CRAFT_SCHEDULE_STATUS>());

                    //负责人
                    System.Collections.Concurrent.ConcurrentBag<WOCraftSchedulePersonModel> persons =
                        new System.Collections.Concurrent.ConcurrentBag<WOCraftSchedulePersonModel>();

                    if (newItem.Persons != null && newItem.Persons.Count > 0)
                    {
                        newItem.Persons.ForEach(person =>
                        {
                            person.FCRAFT_SCHEDULE_ID = newItem.FCRAFT_SCHEDULE_ID;
                            person.FCRAFT_SCHEDULE_PERSON_ID = GuidHelper.NewGuid();
                            persons.Add(person);
                        });
                        daoPersons_Ins.AddRange(newItem.Persons.MapToDestObj<WOCraftSchedulePersonModel, T_MESD_CRAFT_SCHEDULE_PERSON>());
                    }
                    billCodeIndex++;
                }

            }

            var db = _isugar.DB;

            // 收集所有需要分配新批号的排程任务
            var schedulesToAssignLotNo = new List<T_MESD_CRAFT_SCHEDULE>();

            // 1. 如果被修改的排程任务没有批号，则将其加入列表
            if (string.IsNullOrWhiteSpace(daoSchedule_Upd.FLOT_NO))
            {
                schedulesToAssignLotNo.Add(daoSchedule_Upd);
            }

            // 2. 所有新拆分出的排程任务，都需要一个新批号
            if (daoSchedule_Ins != null && daoSchedule_Ins.Any())
            {
                schedulesToAssignLotNo.AddRange(daoSchedule_Ins);
            }

            // 3. 如果列表不为空，则调用辅助方法批量生成并分配唯一批号
            if (schedulesToAssignLotNo.Any())
            {
                await GenerateNewLotNumbersAsync(db,
                    daoSchedule_Upd.FWORK_ORDER_ID,
                    daoSchedule_Upd.FWORK_ORDER_CRAFT_ID,
                    schedulesToAssignLotNo.ToArray());
            }



            try
            {
                db.BeginTran();

                //更新数据表                
                await db.Updateable<T_MESD_CRAFT_SCHEDULE>(daoSchedule_Upd).IgnoreColumns(t => new
                {
                    t.FCDATE,
                    t.FCREATOR_ID,
                    t.FCREATOR
                }).ExecuteCommandAsync();

                await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daoStatus_Upd).UpdateColumns(t => new
                {
                    t.FRELEASER,
                    t.FRELEASER_ID,
                    t.FRELEASE_DATE,
                    t.FRELEASE_STATUS
                }).ExecuteCommandAsync();

                await db.Deleteable<T_MESD_CRAFT_SCHEDULE_PERSON>()
                    .Where(t => t.FCRAFT_SCHEDULE_ID == model.EditModel.FCRAFT_SCHEDULE_ID)
                    .ExecuteCommandAsync();

                if (daoSchedule_Ins != null && daoSchedule_Ins.Count != 0)
                {
                    await db.Insertable<T_MESD_CRAFT_SCHEDULE>(daoSchedule_Ins).ExecuteCommandAsync();
                }

                if (daoStatus_Ins != null && daoStatus_Ins.Count != 0)
                {
                    await db.Insertable<T_MESD_CRAFT_SCHEDULE_STATUS>(daoStatus_Ins).ExecuteCommandAsync();
                }

                if (daoPersons_Ins != null && daoPersons_Ins.Count > 0)
                {
                    await db.Insertable<T_MESD_CRAFT_SCHEDULE_PERSON>(daoPersons_Ins).ExecuteCommandAsync();
                }

                //反写工单工艺已排数
                await UpdateWorkOrderCraftPlanQtyAsync(
                    new List<string> { model.EditModel.FWORK_ORDER_CRAFT_ID },
                    model.IsReleaseTask && model.EditType == ScheduleEditType.EditPlan,
                    db
                    );

                db.CommitTran();
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }

            //
            List<string> schIds = new List<string>() { model.EditModel.FCRAFT_SCHEDULE_ID };
            if (newItems != null)
            {
                schIds.AddRange(newItems.Select(p => p.FCRAFT_SCHEDULE_ID).ToList());
            }
            //

            //返回排程任务数据
            QueryRequestModel request = new QueryRequestModel()
            {
                PageIndex = 1,
                PageSize = 10000,
                WhereGroup = new QueryWhereGroupModel()
                {
                    Items = new List<QueryWhereItemModel>(),
                    GroupType = EnumGroupType.AND,
                }
            };
            request.WhereGroup.Items.Add(new QueryWhereItemModel
            {
                FieldName = "sch.FCRAFT_SCHEDULE_ID",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.In,
                Value = string.Join(",", schIds)
            });
            //更新下发状态
            if (model.EditType == ScheduleEditType.EditPlan && model.IsReleaseTask)
            {
                await UpdateNewScheduleAsync(schIds);
            }
            return await QueryScheduleTaskAsync(request);


        }


        /// <summary>
        /// 验证排程任务,并加入差异值(修改保存任务时调用)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<(bool, List<WOCraftScheduleModel>)> ValidateAndPatchTaskOnEditSaveAsync(ScheduleModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            List<WOCraftScheduleModel> newItems = new List<WOCraftScheduleModel>();
            var db = _isugar.DB;

            List<WOCraftScheduleModel> woCraftModels =
            await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_CRAFT_STATUS>
                ((sch, schStatus, wo, craftStatus) => new JoinQueryInfos(
                    JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                    JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                    JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
            .Where((sch, schStatus, wo, craftStatus) => sch.FWORK_ORDER_CRAFT_ID == model.EditModel.FWORK_ORDER_CRAFT_ID)
            .Select((sch, schStatus, wo, craftStatus) => new WOCraftScheduleModel()
            {
                FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,
                FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                FPLAN_QTY = sch.FPLAN_QTY,
                FRECV_QTY = craftStatus.FRECV_QTY,
                FFINISH_QTY = craftStatus.FFINISH_QTY,
                FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                FPRO_QTY = wo.FPRO_QTY,
                FIF_CLOSE = schStatus.FIF_CLOSE,
                FWORK_ORDER_ID = sch.FWORK_ORDER_ID
            })
            .ToListAsync();

            //
            var oriModel = woCraftModels.FirstOrDefault(p => p.FCRAFT_SCHEDULE_ID == model.EditModel.FCRAFT_SCHEDULE_ID);
            if (oriModel == null)
            {
                ERROR(null, 106001, string.Format(_multiLang["执行失败, 任务编号{0}已失效."], model.EditModel.FCRAFT_SCHEDULE_NO));
            }

            //验证任务是否已结案
            if (oriModel.IsEndClose)
            {
                ERROR(null, 106010, string.Format(_multiLang["执行失败, 任务编号{0}已结案."], model.EditModel.FCRAFT_SCHEDULE_NO));
            }

            if (model.EditType == ScheduleEditType.EditPlan && oriModel.FRELEASE_STATUS)
            {
                ERROR(null, 106014, string.Format(_multiLang["执行失败, 任务编号{0}已下发."], model.EditModel.FCRAFT_SCHEDULE_NO));
            }

            //验证工单是否有效
            if (!await ValidateWorderOrderIsValidAsync(new List<string>() { oriModel.FWORK_ORDER_ID }))
            {
                ERROR(null, 106011, _multiLang["验证工单状态失败."]);
            }


            oriModel.FPLAN_QTY = model.EditModel.FPLAN_QTY;

            //求汇总数
            var totalPlanQty = woCraftModels.Sum(p => p.FPLAN_QTY);

            //验证是否超出工单计划数
            //if (totalPlanQty > oriModel.FPRO_QTY)
            //{
            //    ERROR(null, 106002, string.Format(_multiLang["工单{0},工艺{1},计划产出数量比工单生产数量超出{2}"],
            //                         model.EditModel.FWORK_ORDER_NO,
            //                         model.EditModel.FCRAFT_NAME,
            //                         (totalPlanQty - oriModel.FPRO_QTY).ToString("#,##0.######")));
            //}

            //验证是否小于已接收数
            //if (totalPlanQty < oriModel.FRECV_QTY)
            //{
            //    ERROR(null, 106003, string.Format(_multiLang["工单{0},工艺{1},计划产出数量比接收数超出{2}"],
            //                         model.EditModel.FWORK_ORDER_NO,
            //                         model.EditModel.FCRAFT_NAME,
            //                         (oriModel.FRECV_QTY - totalPlanQty).ToString("#,##0.######")));
            //}

            if (totalPlanQty < oriModel.FFINISH_QTY)
            {
                ERROR(null, 106003, string.Format(_multiLang["工单{0},工艺{1},计划产出数量少于完工数 {2}"],
                                     model.EditModel.FWORK_ORDER_NO,
                                     model.EditModel.FCRAFT_NAME,
                                     (oriModel.FFINISH_QTY - totalPlanQty).ToString("#,##0.######")));
            }





            //获取新增拆分数据
            var taskDatas = model.TaskSplitList.Where(p => p.FCRAFT_SCHEDULE_ID != model.EditModel.FCRAFT_SCHEDULE_ID).ToList();

            foreach (var taskData in taskDatas)
            {
                if (taskData != null && taskData.FPLAN_QTY > 0 && taskData.FSTATION_ID != null)
                {

                    var cloneSchCraft = (WOCraftScheduleModel)_serializer.Deserialize(_serializer.Serialize(model.EditModel), typeof(WOCraftScheduleModel));

                    cloneSchCraft.FRELEASE_STATUS = false;   //待下发状态

                    cloneSchCraft.FRELEASER = user.UserPsnName;
                    cloneSchCraft.FRELEASER_ID = user.UserPsnId;
                    cloneSchCraft.FRELEASE_DATE = _iauth.GetCurDateTime();

                    cloneSchCraft.FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid();
                    cloneSchCraft.FCRAFT_SCHEDULE_STATUS_ID = GuidHelper.NewGuid();

                    cloneSchCraft.FSTATION_ID = taskData.FSTATION_ID;
                    cloneSchCraft.FPLAN_QTY = taskData.FPLAN_QTY;
                    cloneSchCraft.FWORK_NUM = taskData.FWORK_NUM;
                    cloneSchCraft.FPLAN_ST_DATE = taskData.FPLAN_ST_DATE;
                    cloneSchCraft.FPLAN_ED_DATE = taskData.FPLAN_ED_DATE;
                    cloneSchCraft.FPLAN_USE_HOUR = taskData.FPLAN_USE_HOUR;
                    cloneSchCraft.Persons = taskData.Persons;


                    newItems.Add(cloneSchCraft);
                }
            }

            //编辑拆分任务中的当前排程
            var editData = model.TaskSplitList.Where(p => p.FCRAFT_SCHEDULE_ID == model.EditModel.FCRAFT_SCHEDULE_ID).FirstOrDefault();
            if (editData != null)
            {
                model.EditModel.FSTATION_ID = editData.FSTATION_ID;
                model.EditModel.FPLAN_QTY = editData.FPLAN_QTY;
                model.EditModel.FWORK_NUM = editData.FWORK_NUM;
                model.EditModel.FPLAN_ST_DATE = editData.FPLAN_ST_DATE;
                model.EditModel.FPLAN_ED_DATE = editData.FPLAN_ED_DATE;
                model.EditModel.FPLAN_USE_HOUR = editData.FPLAN_USE_HOUR;
                model.EditModel.Persons = editData.Persons;
            }

            //把未排满的数量加入计划，状态为待下发
            //if (totalPlanQty < oriModel.FPRO_QTY)
            //{
            //    var cloneSchCraft = (WOCraftScheduleModel)_serializer.Deserialize(_serializer.Serialize(model.EditModel), typeof(WOCraftScheduleModel));

            //    cloneSchCraft.FRELEASE_STATUS = false;   //待下发状态

            //    cloneSchCraft.FRELEASER = user.UserPsnName;
            //    cloneSchCraft.FRELEASER_ID = user.UserPsnId;
            //    cloneSchCraft.FRELEASE_DATE = _iauth.GetCurDateTime();

            //    cloneSchCraft.FCRAFT_SCHEDULE_ID = GuidHelper.NewGuid();
            //    cloneSchCraft.FCRAFT_SCHEDULE_STATUS_ID = GuidHelper.NewGuid();

            //    cloneSchCraft.FPLAN_QTY = oriModel.FPRO_QTY - totalPlanQty;
            //    newItems.Add(cloneSchCraft);
            //}

            //
            return (true, newItems);
        }
        #endregion


        #region 修改排程任务工位
        /// <summary>
        /// 修改排程任务工位
        /// </summary>
        /// <returns></returns>
        public async Task<DataResult<WOCraftScheduleModel>> ChangeStationAsync(ChangeStationModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            if (!await ValidateChangeStationAsync(model))
            {

                ERROR(null, 107001, _multiLang["更换工位验证失败."]);
            }

            var db = _isugar.DB;
            try
            {
                db.BeginTran();

                await db.Updateable<T_MESD_CRAFT_SCHEDULE>()
                    .SetColumns(it => it.FSTATION_ID == model.FSTATION_ID)
                    .UpdateColumns(p => new { p.FSTATION_ID })
                    .Where(p => p.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                    .ExecuteCommandAsync();

                db.CommitTran();
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }


            //返回排程任务数据
            QueryRequestModel request = new QueryRequestModel()
            {
                PageIndex = 1,
                PageSize = 10000,
                WhereGroup = new QueryWhereGroupModel()
                {
                    Items = new List<QueryWhereItemModel>(),
                    GroupType = EnumGroupType.AND,
                }
            };
            request.WhereGroup.Items.Add(new QueryWhereItemModel
            {
                FieldName = "sch.FCRAFT_SCHEDULE_ID",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Equal,
                Value = model.FCRAFT_SCHEDULE_ID
            });

            var results = await QueryScheduleTaskAsync(request);
            if (results.StatusCode != 200)
            {
                ERROR(results, results.StatusCode, results.Message);
            }

            WOCraftScheduleModel schModel = null;
            if (results.Entity != null && results.Entity.Count > 0)
            {
                schModel = results.Entity[0];
            }
            else
            {
                ERROR(null, 100790, _multiLang["执行失败, 排程任务数据不存在"]);
            }

            DataResult<WOCraftScheduleModel> result = new DataResult<WOCraftScheduleModel>
            {
                Entity = schModel,
                StatusCode = 200,
            };
            return await OK(result);

        }
        /// <summary>
        ///  验证更换工位
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<bool> ValidateChangeStationAsync(ChangeStationModel model)
        {

            if (model == null)
            {
                ERROR(null, 107001, string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model"));
            }

            if (string.IsNullOrWhiteSpace(model.FCRAFT_SCHEDULE_ID))
            {
                ERROR(null, 107002, string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model.FCRAFT_SCHEDULE_ID"));
            }

            if (string.IsNullOrWhiteSpace(model.FSTATION_ID))
            {
                ERROR(null, 107003, string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model.FSTATION_ID"));
            }


            if (string.IsNullOrWhiteSpace(model.FOLD_STATION_ID))
            {
                ERROR(null, 107003, string.Format(_multiLang["执行失败, 传入参数{0}为空."], "model.FOLD_STATION_ID"));
            }

            if (model.FOLD_STATION_ID == model.FSTATION_ID)
            {
                ERROR(null, 107100, _multiLang["执行失败, 更换后工位不能与原工位相等."]);
            }

            var user = await _iauth.GetUserAccountAsync();

            //验证员工
            if (string.IsNullOrWhiteSpace(user.UserPsnId) || string.IsNullOrWhiteSpace(user.UserPsnName))
            {
                ERROR(null, 107102, _multiLang["执行失败, 当前用户员工信息为空."]);
            }


            //排程任务
            var db = _isugar.DB;

            //排程任务是否有加工中的任务
            var workingData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE>
               ((job, sch) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID))
               .Where((job, sch) => job.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID &&
                       (job.FWORK_STATUS == WorkStatus.working.ToString() ||
                       job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                       job.FWORK_STATUS == WorkStatus.paused.ToString()))
               .Select((job, sch) => new { job.FCRAFT_JOB_BOOKING_NO })
               .FirstAsync();
            if (workingData != null)
            {
                ERROR(null, 107103, string.Format(_multiLang["执行失败, 存在加工中任务 {0} ,不能更改工位."], workingData.FCRAFT_JOB_BOOKING_NO));
            }

            var schData = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((sch, schStatus) => sch.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                .Select((sch, schStatus) => new
                {
                    sch.FCRAFT_SCHEDULE_ID,
                    sch.FCRAFT_SCHEDULE_NO,
                    schStatus.FIF_CLOSE,
                    schStatus.FRECV_QTY,
                    sch.FPLAN_QTY,
                    schStatus.FRELEASE_STATUS,
                    schStatus.FFINISH_QTY,
                })
                .FirstAsync();
            if (schData == null)
            {
                ERROR(null, 107001, _multiLang["排程任务数据不存在, 请查正."]);
            }

            //任务是否已结案
            if (schData.FIF_CLOSE == 1 || schData.FIF_CLOSE == 3)
            {
                ERROR(null, 107002, string.Format(_multiLang["执行失败, 排程任务 {0} 已结案."], schData.FCRAFT_SCHEDULE_NO));
            }

            //任务是否未下发
            if (!schData.FRELEASE_STATUS)
            {
                ERROR(null, 107003, string.Format(_multiLang["执行失败, 排程任务 {0} 未下发."], schData.FCRAFT_SCHEDULE_NO));
            }

            //任务是否已完工
            if (schData.FFINISH_QTY >= schData.FPLAN_QTY)
            {
                ERROR(null, 107004, string.Format(_multiLang["执行失败, 排程任务 {0} 已全部完工."], schData.FCRAFT_SCHEDULE_NO));
            }

            //工单
            var woData = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>
                ((sch, wo, woStatus) => new JoinQueryInfos(JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                        JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID))
                .Where((sch, wo, woStatus) => sch.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                .Select((sch, wo, woStatus) => new
                {
                    wo.FWORK_ORDER_ID,
                    wo.FWORK_ORDER_NO,
                    woStatus.FIF_CLOSE,
                    woStatus.FIF_CANCEL,
                    sch.FWORK_ORDER_CRAFT_ID,
                    sch.FCRAFT_SCHEDULE_NO
                })
                .FirstAsync();
            if (woData == null)
            {
                ERROR(null, 107005, _multiLang["排程任务对应的工单数据不存在, 请查正."]);
            }

            //工单是否已结案
            if (woData.FIF_CLOSE == 1 || woData.FIF_CLOSE == 3)
            {
                ERROR(null, 107006, string.Format(_multiLang["执行失败, 排程任务 {0} , 工单 {1} 已结案."], schData.FCRAFT_SCHEDULE_NO, woData.FWORK_ORDER_NO));
            }

            //工单是否已作废
            if (woData.FIF_CANCEL)
            {
                ERROR(null, 107007, string.Format(_multiLang["执行失败, 排程任务 {0} , 工单 {1} 已作废."], schData.FCRAFT_SCHEDULE_NO, woData.FWORK_ORDER_NO));
            }

            return await Task.FromResult(true);

        }
        #endregion

        #region 公共子方法
        /// <summary>
        /// 验证工单是否有效
        /// </summary>
        /// <param name="woIds"></param>
        /// <returns></returns>
        private async Task<bool> ValidateWorderOrderIsValidAsync(List<string> woIds)
        {
            var rpcServer = this.GetService<IMES003WorkOrderService>("MES003WorkOrder");
            var woResult = await rpcServer.ValidateWorderOrderIsValidAsync(woIds);
            if (woResult.StatusCode != 200)
            {
                ERROR(woResult, woResult.StatusCode, woResult.Message);
            }
            return await Task.FromResult(woResult.Entity);
        }
        #endregion

        #region 委外状态是
        /// <summary>
        /// 委外状态是
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> OutAsync(List<string> ids)
        {

            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var schNo = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
              ((sch, schStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
              .Where((sch, schStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID) && schStatus.FRELEASE_STATUS == true)
              .Select((sch, schStatus) => sch.FCRAFT_SCHEDULE_NO)
              .FirstAsync();
            DataResult<object> result = new DataResult<object>
            {
                StatusCode = 200,
                IsSucceed = false,

            };
            if (!string.IsNullOrWhiteSpace(schNo))
            {
                result = new DataResult<object>
                {
                    StatusCode = 11111,
                    IsSucceed = false,
                    Message = "任务编号" + schNo + "已下发,不能转委外申请单."
                };
                return await OK(result);
            }

            //构造数据
            List<string> schStatusIds = (await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS>()
                     .Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID))
                     .Select(p => p.FCRAFT_SCHEDULE_STATUS_ID)
                     .ToListAsync()).Distinct().ToList();

            var daos = schStatusIds.Select(statusId =>

                new T_MESD_CRAFT_SCHEDULE_STATUS
                {
                    FIS_OUT = 1,
                    FCRAFT_SCHEDULE_STATUS_ID = statusId
                }
            ).ToList();

            //更新结案状态
            await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daos)
            .UpdateColumns(t => new { t.FIS_OUT })
            .ExecuteCommandAsync();



            //返回结果
            var dynamics = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER_CRAFT_STATUS>
                ((sch, schStatus, craftStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                        JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
                .Where((sch, schStatus, craftStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID))
                .Select((sch, schStatus, craftStatus) => new
                {
                    FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,
                    FIS_OUT = schStatus.FIS_OUT,
                })
                .ToListAsync();

            result = new DataResult<object>
            {
                StatusCode = 200,
                Entity = dynamics,
            };

            return await OK(result);
        }
        #endregion


        #region 委外状态否
        /// <summary>
        /// 委外状态否
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> UnOutAsync(List<string> ids)
        {

            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;


            //构造数据
            List<string> schStatusIds = (await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS>()
                     .Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID))
                     .Select(p => p.FCRAFT_SCHEDULE_STATUS_ID)
                     .ToListAsync()).Distinct().ToList();

            var daos = schStatusIds.Select(statusId =>

                new T_MESD_CRAFT_SCHEDULE_STATUS
                {
                    FIS_OUT = 0,
                    FCRAFT_SCHEDULE_STATUS_ID = statusId
                }
            ).ToList();

            //更新结案状态
            await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(daos)
            .UpdateColumns(t => new { t.FIS_OUT })
            .ExecuteCommandAsync();



            //返回结果
            var dynamics = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER_CRAFT_STATUS>
                ((sch, schStatus, craftStatus) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                        JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
                .Where((sch, schStatus, craftStatus) => ids.Contains(sch.FCRAFT_SCHEDULE_ID))
                .Select((sch, schStatus, craftStatus) => new
                {
                    FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,
                    FIS_OUT = schStatus.FIS_OUT,
                })
                .ToListAsync();

            DataResult<object> result = new DataResult<object>
            {
                StatusCode = 200,
                Entity = dynamics,
            };

            return await OK(result);
        }
        #endregion


        public static Dictionary<string, bool> JudgeNewSchedule = new Dictionary<string, bool>();
        /// <summary>
        /// 根据工位ID 获取是否有最新的排程信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<bool>> JudgeNewScheduleByStationIdAsync(string stationId)
        {
            DataResult<bool> result = new DataResult<bool>
            {
                StatusCode = 200,
                Entity = false,
            };

            if (JudgeNewSchedule.ContainsKey(stationId))
            {
                result.Entity = JudgeNewSchedule[stationId];

                JudgeNewSchedule[stationId] = false;
            }


            return await OK(result);

        }

        /// <summary>
        /// 更新工位的排程信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task UpdateNewScheduleAsync(List<string> ids)
        {

            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var woCrafts = (await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                           .Where(p => ids.Contains(p.FCRAFT_SCHEDULE_ID))
                           .ToListAsync()).Distinct().ToList();

            foreach (var item in woCrafts)
            {
                if (JudgeNewSchedule.ContainsKey(item.FSTATION_ID))
                {
                    JudgeNewSchedule[item.FSTATION_ID] = true;
                }
                else
                {
                    JudgeNewSchedule.Add(item.FSTATION_ID, true);
                }
            }
        }

        /// <summary>
        /// 更新排程颜色（佳乐）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<DataResult<bool>> SaveFcraftScheduleColorAsync(WOCraftScheduleModel model)
        {

            var db = _isugar.DB;

            DataResult<bool> result = new DataResult<bool>
            {
                StatusCode = 200,
                Entity = false,
            };

            if (string.IsNullOrEmpty(model.FCRAFT_SCHEDULE_ID))
            {
                ERROR(model, result.StatusCode = 101000, result.Message = "id不能为空");
            }

            try
            {
                db.BeginTran();
                //更新T_MESD_CRAFT_SCHEDULE表颜色FLOT_NO_COLOR字段
               await db.Updateable<T_MESD_CRAFT_SCHEDULE>()
                .SetColumns(it => it.FLOT_NO_COLOR == model.FLOT_NO_COLOR) // 蓝色
                .Where(it => it.FCRAFT_SCHEDULE_ID == model.FCRAFT_SCHEDULE_ID)
                .ExecuteCommandAsync();

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }

            result.Entity = true;
            result.Message = "修改排程颜色成功！";


            return result;
        }




    }
}
