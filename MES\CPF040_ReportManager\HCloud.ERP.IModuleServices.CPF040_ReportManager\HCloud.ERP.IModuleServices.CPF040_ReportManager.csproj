﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <Version>1.0.7</Version>    
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>    
  </PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">

		<OutputPath>F:\modules\mes-system-web\Mesroot\MicroServer\Modules</OutputPath>
	</PropertyGroup>


	<ItemGroup>
    <Compile Remove="Models\Response\TmsdmUnitModel.cs" />
    <Compile Remove="Models\Response\UnitModel.cs" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="HCloud.Core.ProxyGenerator" Version="1.0.30" />
    <PackageReference Include="HCloud.Core.HCPlatform" Version="1.0.91" />
  </ItemGroup>

</Project>
