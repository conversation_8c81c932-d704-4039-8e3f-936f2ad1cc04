﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <Version>1.0.58</Version>    
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>    
  </PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<OutputPath>F:\modules\mes-system-web\Mesroot\MicroServer\Modules</OutputPath>
	</PropertyGroup>
	<ItemGroup>
	  <PackageReference Include="HCloud.Core.HCPlatform" Version="1.0.113" />
	  <PackageReference Include="HCloud.Core.ProxyGenerator" Version="1.0.30" />
	  <PackageReference Include="QRCoder" Version="1.6.0" />
	</ItemGroup>
	<ItemGroup>
	  <Reference Include="HCloud.ERP.IModuleServices.COP001_SaleOrder">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.COP001_SaleOrder.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.MES003_WorkOrder">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.MES003_WorkOrder.dll</HintPath>
	  </Reference>
	  <Reference Include="HCloud.ERP.IModuleServices.MES005_CraftSchedule">
	    <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.MES005_CraftSchedule.dll</HintPath>
	  </Reference>
	</ItemGroup>

</Project>
