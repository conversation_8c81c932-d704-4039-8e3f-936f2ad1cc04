﻿using HCloud.Core.Common;
using HCloud.Core.HCPlatform.Auth.Internal;
using HCloud.Core.HCPlatform.CommonData;
using HCloud.Core.HCPlatform.Ioc;
using HCloud.Core.HCPlatform.Multilingual;
using HCloud.Core.HCPlatform.Serialization;
using HCloud.Core.HCPlatform.ThirdParty;
using HCloud.Core.HCPlatform.Utilities;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.ProxyGenerator.Models;
using HCloud.Core.Sugar;
using HCloud.ERP.IModuleServices.IPUR024SupplierDeliveryOrder;
using HCloud.ERP.IModuleServices.IU011_PurInquiry.Models;
using HCloud.ERP.IModuleServices.MES003_WorkOrder;
using HCloud.ERP.IModuleServices.MES017_TAG;
using HCloud.ERP.IModuleServices.PUR011_OutOrder.Models;
using HCloud.ERP.IModuleServices.PUR012_OutRecv.Models;
using HCloud.ERP.IModuleServices.PUR022_SupplierOrder.Models.DataModel;
using HCloud.ERP.IModuleServices.PUR024_SupplierDeliveryOrder.Models.DataModel;
using HCloud.ERP.IModuleServices.PUR024_SupplierDeliveryOrder.Models.Response;
using HCloud.ERP.IModuleServices.STK001_Store.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Utilities;
using QRCoder;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
namespace HCloud.ERP.ModuleServices.PUR024_SupplierDeliveryOrder
{
    [ModuleName("PUR024SupplierDeliveryOrder")]
    public partial class PUR024SupplierDeliveryOrderService : ProxyServiceBase, IPUR024SupplierDeliveryOrderService
    {
        private readonly ILogger _Logger = null;
        private readonly IAuth _iauth = null;
        private readonly ISugar<PUR024SupplierDeliveryOrderService> _isugar = null;
        private readonly IMultilingualResource _multiLang = null;
        private readonly ICommonDataProvider _commonDataProvider = null;
        private readonly ISerializer<string> _serializer = null;
        private readonly HttpClient _httpClient = null;
        //注入获取参数
        private readonly IThirdPartySetting _ThirdPartySetting = null;
        private readonly IModuleServices.SYS000_Common.Services.IBusinessDataService _businessService = null;


        public PUR024SupplierDeliveryOrderService(ILogger<PUR024SupplierDeliveryOrderService> logger,
             ISugar<PUR024SupplierDeliveryOrderService> sugar, IAuth auth, IMultilingualResource multiLang,
             ICommonDataProvider commonDataProvider, ISerializer<string> serializer,
             IThirdPartySetting ThirdPartySetting,
             HttpClient httpClient,
               IModuleServices.SYS000_Common.Services.IBusinessDataService businessService
            )
        {
            _Logger = logger;
            _iauth = auth;
            _isugar = sugar;
            _multiLang = multiLang;
            _commonDataProvider = commonDataProvider;
            _serializer = serializer;
            _ThirdPartySetting = ThirdPartySetting;
            _httpClient = httpClient;
            _businessService = businessService;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<SupplierDeliveryOrderModel>>> GetAllAsync(QueryRequestModel model)
        {
            if (model == null)
            {
                ERROR(null, 100010, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model"));
            }

            var user = await _iauth.GetUserAccountAsync();

            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);
            var db = _isugar.DB;
            var _mainDB = _isugar.GetDB("MAIN");
            RefAsync<int> totalCount = new RefAsync<int>();

            var FPROVIDER_IDS = await _mainDB.Queryable<T_MSDM_TRAN_ENTITY>().Where(n => n.FEMP_ID == user.UserPsnId).Select(n => n.FTRAN_ENTITY_ID).Distinct().ToListAsync();

            //查询结果
            List<SupplierDeliveryOrderModel> adjusts =
            await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER, T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>
                ((a, b) => new JoinQueryInfos(JoinType.Left, a.FSUPPLIER_DELIVERY_ORDER_ID == b.FSUPPLIER_DELIVERY_ORDER_ID))
                .OrderBy((a, b) => a.FCDATE, OrderByType.Desc)  //按建立日期倒序
                 .WhereIF(FPROVIDER_IDS != null && FPROVIDER_IDS.Count > 0, (a, b) => FPROVIDER_IDS.Contains(a.FPROVIDER_ID))
                .Where(wheres)
                .Select<SupplierDeliveryOrderModel>()
                .ToPageListAsync(model.PageIndex, model.PageSize, totalCount);

            DataResult<List<SupplierDeliveryOrderModel>> dataResult = new DataResult<List<SupplierDeliveryOrderModel>>()
            {
                Entity = adjusts,
                StatusCode = 200,
                Pager = new PagerResult()
                {
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                    TotalRecords = totalCount.Value,
                },
            };

            return await OK(dataResult);

        }

        /// <summary>
        /// 根据ID获取表单对象
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<DataResult<SupplierDeliveryOrderModel>> GetByIdAsync(string id)
        {

            try
            {
                var db = _isugar.DB;

                if (string.IsNullOrWhiteSpace(id))
                {
                    ERROR(null, 100010, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "id"));
                }

                var user = await _iauth.GetUserAccountAsync();
                var _mstinfo = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER, T_PURD_SUPPLIER_DELIVERY_ORDER_CUSTOM, T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>((a, b, c) =>
                new JoinQueryInfos(JoinType.Left, b.FSUPPLIER_DELIVERY_ORDER_ID == a.FSUPPLIER_DELIVERY_ORDER_ID, JoinType.Left, c.FSUPPLIER_DELIVERY_ORDER_ID == a.FSUPPLIER_DELIVERY_ORDER_ID))
                    .Where((a, b, c) => a.FSUPPLIER_DELIVERY_ORDER_ID == id).Select<SupplierDeliveryOrderModel>().FirstAsync();
                if (_mstinfo == null)
                {
                    ERROR(null, 100010, string.Format(_multiLang["执行失败, 查询不到对应的数据."]));
                }
                _mstinfo.Items = new List<SupplierDeliveryOrderImsModel>();
                var items = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER, T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL, T_PURD_SUPPLIER_ORDER_MATERIAL>((a, b, c) =>
            new JoinQueryInfos(
                JoinType.Left, b.FSUPPLIER_DELIVERY_ORDER_ID == a.FSUPPLIER_DELIVERY_ORDER_ID,
                  JoinType.Left, c.FSUPPLIER_ORDER_MATERIAL_ID == b.FSUPPLIER_ORDER_MATERIAL_ID))
                .Where((a, b, c) => a.FSUPPLIER_DELIVERY_ORDER_ID == id)
                .OrderBy((a, b, c) => b.FSHOW_SEQNO, OrderByType.Asc)
                .Select<SupplierDeliveryOrderImsModel>()
                .ToListAsync();

                var workService = GetService<IMES003WorkOrderService>("MES003WorkOrder");
                var workinfo = await workService.GetWorkOrdCraftByIdsAsync(items.Select(p => p.FWORK_ORDER_CRAFT_ID).ToList());

                var itemIds = items.Select(n => n.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID).Distinct().ToList();
                var TagItem = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL_TAG>().Where(n => itemIds.Contains(n.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID)).Select<SupplierDeliveryOrderImsTagModel>().ToListAsync();


                //合并查询人员信息
                var PersonsIds = new List<string>();
                PersonsIds = TagItem.SelectMany(n => new[] { n.FTEXT6, n.FTEXT7 }).Distinct().ToList();

                //调用rpc获取人员信息
                var personsResult = await _businessService.GetEmployeeByIdsAsync(PersonsIds);
                if (personsResult.StatusCode != 200)
                {
                    ERROR(personsResult, personsResult.StatusCode, personsResult.Message);
                }
                var personDict = personsResult.Entity.ToDictionary(p => p.FEMP_ID, p => p.FEMP_NAME);

                List<string> matIds = items.Select(p => p.FMATERIAL_ID).Distinct().ToList();

                var matinfos = await _businessService.GetMaterialByIdsAsync(matIds);

                var matUnitIds = matinfos.Entity.SelectMany(p => { var ids = new List<string>() { p.FUNIT_ID, p.FPRO_UNIT_ID }; return ids; }).Distinct().ToList();
                var unitInfos = await _businessService.GetUnitNameByIdsAsync(matUnitIds);
                items.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
                {

                    var matdata = matinfos.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                    if (matdata != null)
                    {
                        item.FMATERIAL_CODE = matdata.FMATERIAL_CODE;
                        item.FMATERIAL_NAME = matdata.FMATERIAL_NAME;
                        item.FSPEC_DESC = matdata.FSPEC_DESC;
                        item.FGOODS_MODEL = matdata.FGOODS_MODEL;

                        var proUnitData = unitInfos.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FPRO_UNIT_ID);
                        if (proUnitData != null)
                        {
                            item.FPRO_UNIT_NAME = proUnitData.FUNIT_NAME;
                            item.FPRO_UNIT_CODE = proUnitData.FUNIT_CODE;
                        }

                    }
                    var work = workinfo.Entity.Where(p => p.FWORK_ORDER_ID == item.FWORK_ORDER_ID)
             .FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == item.FWORK_ORDER_CRAFT_ID);
                    if (work != null)
                    {
                        item.FCRAFT_CODE = work.FCRAFT_CODE;
                        item.FCRAFT_NAME = work.FCRAFT_NAME;
                        item.FCRAFT_ID = work.FCRAFT_ID;
                        item.FWORK_ORDER_NO = work.FWORK_ORDER_NO;
                    }
                });
                TagItem.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
                {

                    var matdata = matinfos.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                    if (matdata != null)
                    {
                        item.FMATERIAL_CODE = matdata.FMATERIAL_CODE;
                        item.FMATERIAL_NAME = matdata.FMATERIAL_NAME;
                        item.FSPEC_DESC = matdata.FSPEC_DESC;
                        item.FGOODS_MODEL = matdata.FGOODS_MODEL;

                        var proUnitData = unitInfos.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FPRO_UNIT_ID);
                        if (proUnitData != null)
                        {
                            item.FPRO_UNIT_NAME = proUnitData.FUNIT_NAME;
                            item.FPRO_UNIT_CODE = proUnitData.FUNIT_CODE;
                        }

                    }

                });
                foreach (var child in items)
                {
                    child.TagItems = new List<SupplierDeliveryOrderImsTagModel>();
                    child.TagItems = TagItem.Where(n => n.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID == child.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID).ToList();

                    foreach (var tag in child.TagItems)
                    {
                        //使用字典快速查找作业员信息
                        if (tag.FTEXT6 != null && personDict.TryGetValue(tag.FTEXT6, out var workPersonName))
                        {
                            tag.FWORK_PERSON_NAME = workPersonName;
                        }

                        // 使用字典快速查找检验员信息
                        if (tag.FTEXT7 != null && personDict.TryGetValue(tag.FTEXT7, out var chkPersonName))
                        {
                            tag.FCHECK_PERSON_NAME = chkPersonName; 
                        }
                    }
                }
                _mstinfo.Items = items;

                DataResult<SupplierDeliveryOrderModel> result = new DataResult<SupplierDeliveryOrderModel>() { StatusCode = 200, Entity = _mstinfo };
                return await OK(result);
            }
            catch (Exception)
            {

                throw;
            }


        }
        /// <summary>
        /// 根据id获取细表查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<DataResult<SupOrderImsAndTagModel>> GetImByIdAsync(string id)
        {
            var db = _isugar.DB;

            var items = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER, T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL, T_PURD_SUPPLIER_ORDER_MATERIAL>((a, b, c) =>
            new JoinQueryInfos(
                JoinType.Left, b.FSUPPLIER_DELIVERY_ORDER_ID == a.FSUPPLIER_DELIVERY_ORDER_ID,
                  JoinType.Left, c.FSUPPLIER_ORDER_MATERIAL_ID == b.FSUPPLIER_ORDER_MATERIAL_ID))
                .Where((a, b, c) => a.FSUPPLIER_DELIVERY_ORDER_ID == id)
                .OrderBy((a, b, c) => b.FSHOW_SEQNO, OrderByType.Asc)
                .Select<SupplierDeliveryOrderImsModel>()
                .ToListAsync();
            var itemIds = items.Select(n => n.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID).Distinct().ToList();
            var TagItem = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL_TAG>().Where(n => itemIds.Contains(n.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID)).Select<SupplierDeliveryOrderImsTagModel>().ToListAsync();

            List<string> matIds = items.Select(p => p.FMATERIAL_ID).Distinct().ToList();

            var matinfos = await _businessService.GetMaterialByIdsAsync(matIds);

            var matUnitIds = matinfos.Entity.SelectMany(p => { var ids = new List<string>() { p.FUNIT_ID, p.FPRO_UNIT_ID }; return ids; }).Distinct().ToList();
            var unitInfos = await _businessService.GetUnitNameByIdsAsync(matUnitIds);
            var workService = GetService<IMES003WorkOrderService>("MES003WorkOrder");
            var workinfo = await workService.GetWorkOrdCraftByIdsAsync(items.Select(p => p.FWORK_ORDER_CRAFT_ID).ToList());

            items.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {

                var matdata = matinfos.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                if (matdata != null)
                {
                    item.FMATERIAL_CODE = matdata.FMATERIAL_CODE;
                    item.FMATERIAL_NAME = matdata.FMATERIAL_NAME;
                    item.FSPEC_DESC = matdata.FSPEC_DESC;
                    item.FGOODS_MODEL = matdata.FGOODS_MODEL;

                    var proUnitData = unitInfos.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FPRO_UNIT_ID);
                    if (proUnitData != null)
                    {
                        item.FPRO_UNIT_NAME = proUnitData.FUNIT_NAME;
                        item.FPRO_UNIT_CODE = proUnitData.FUNIT_CODE;
                    }

                }
                var work = workinfo.Entity.Where(p => p.FWORK_ORDER_ID == item.FWORK_ORDER_ID)
                   .FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == item.FWORK_ORDER_CRAFT_ID);
                if (work != null)
                {
                    item.FCRAFT_CODE = work.FCRAFT_CODE;
                    item.FCRAFT_NAME = work.FCRAFT_NAME;
                    item.FCRAFT_ID = work.FCRAFT_ID;
                    item.FWORK_ORDER_NO = work.FWORK_ORDER_NO;
                }

            });

            TagItem.AsParallel().WithDegreeOfParallelism(4).ForAll(async item =>
            {

                var matdata = matinfos.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                if (matdata != null)
                {
                    item.FMATERIAL_CODE = matdata.FMATERIAL_CODE;
                    item.FMATERIAL_NAME = matdata.FMATERIAL_NAME;
                    item.FSPEC_DESC = matdata.FSPEC_DESC;
                    item.FGOODS_MODEL = matdata.FGOODS_MODEL;

                    var proUnitData = unitInfos.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FPRO_UNIT_ID);
                    if (proUnitData != null)
                    {
                        item.FPRO_UNIT_NAME = proUnitData.FUNIT_NAME;
                        item.FPRO_UNIT_CODE = proUnitData.FUNIT_CODE;
                    }

                }
                item.FBARCODE_URL = !string.IsNullOrWhiteSpace(item.FBARCODE_NO) ? GenCode(item.FBARCODE_NO) : "";
            });
            SupOrderImsAndTagModel im = new SupOrderImsAndTagModel
            {
                Items = items,
                TagItems = TagItem,
            };
            DataResult<SupOrderImsAndTagModel> result = new DataResult<SupOrderImsAndTagModel>() { StatusCode = 200, Entity = im };
            return await OK(result);
        }

        private string GenCode(string no)
        {
            QRCodeGenerator qRCodeGenerator = new QRCodeGenerator();
            QRCodeData qRCodeData = qRCodeGenerator.CreateQrCode(no, QRCodeGenerator.ECCLevel.Q);
            QRCode qRCode = new QRCode(qRCodeData);
            Bitmap qrCodeImage = qRCode.GetGraphic(20); // 20是二维码的大小，可以根据需要调整
            MemoryStream ms = new MemoryStream();
            qrCodeImage.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
            byte[] arr = new byte[ms.Length];
            ms.Position = 0;
            ms.Read(arr, 0, (int)ms.Length);
            ms.Close();
            String strbaser64 = Convert.ToBase64String(arr);
            return strbaser64;

        }
        /// <summary>
        /// 根据细表ID获取标签记录
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<DataResult<List<SupplierDeliveryOrderImsTagModel>>> GetImTagByIdAsync(string id)
        {
            var db = _isugar.DB;

            var items = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER, T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL, T_PURD_SUPPLIER_ORDER_MATERIAL>((a, b, c) =>
            new JoinQueryInfos(
                JoinType.Left, b.FSUPPLIER_DELIVERY_ORDER_ID == a.FSUPPLIER_DELIVERY_ORDER_ID,
                  JoinType.Left, c.FSUPPLIER_ORDER_MATERIAL_ID == b.FSUPPLIER_ORDER_MATERIAL_ID))
                .Where((a, b, c) => a.FSUPPLIER_DELIVERY_ORDER_ID == id)
                .OrderBy((a, b, c) => b.FSHOW_SEQNO, OrderByType.Asc)
                .Select<SupplierDeliveryOrderImsModel>()
                .ToListAsync();
            var itemIds = items.Select(n => n.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID).Distinct().ToList();
            var TagItem = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL_TAG>().Where(n => itemIds.Contains(n.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID)).Select<SupplierDeliveryOrderImsTagModel>().ToListAsync();

            List<string> matIds = items.Select(p => p.FMATERIAL_ID).Distinct().ToList();

            var matinfos = await _businessService.GetMaterialByIdsAsync(matIds);

            var matUnitIds = matinfos.Entity.SelectMany(p => { var ids = new List<string>() { p.FUNIT_ID, p.FPRO_UNIT_ID }; return ids; }).Distinct().ToList();
            var unitInfos = await _businessService.GetUnitNameByIdsAsync(matUnitIds);

            TagItem.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {

                var matdata = matinfos.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                if (matdata != null)
                {
                    item.FMATERIAL_CODE = matdata.FMATERIAL_CODE;
                    item.FMATERIAL_NAME = matdata.FMATERIAL_NAME;
                    item.FSPEC_DESC = matdata.FSPEC_DESC;
                    item.FGOODS_MODEL = matdata.FGOODS_MODEL;

                    var proUnitData = unitInfos.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FPRO_UNIT_ID);
                    if (proUnitData != null)
                    {
                        item.FPRO_UNIT_NAME = proUnitData.FUNIT_NAME;
                        item.FPRO_UNIT_CODE = proUnitData.FUNIT_CODE;
                    }

                }
            });

            DataResult<List<SupplierDeliveryOrderImsTagModel>> result = new DataResult<List<SupplierDeliveryOrderImsTagModel>>() { StatusCode = 200, Entity = TagItem };
            return await OK(result);
        }

        /// <summary>
        /// 委外收货单 选择界面提供数据查询
        /// </summary>
        public async Task<DataResult<List<SupplierDeliveryOrderModel>>> GetAllForRecvAsync(QueryRequestModel model)
        {
            if (model == null)
            {
                ERROR(null, 100010, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model"));
            }

            var db = _isugar.DB;
            RefAsync<int> totalCount = new RefAsync<int>();

            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);

            List<SupplierDeliveryOrderModel> masterRecords =
            await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER, T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>
                ((a, b) => new JoinQueryInfos(JoinType.Left, a.FSUPPLIER_DELIVERY_ORDER_ID == b.FSUPPLIER_DELIVERY_ORDER_ID))
                .OrderBy((a, b) => a.FCDATE, OrderByType.Desc)  //按建立日期倒序
                .Select<SupplierDeliveryOrderModel>()
                .Where(wheres).OrderBy(a => a.FCDATE, OrderByType.Desc)
                .ToPageListAsync(model.PageIndex, model.PageSize, totalCount);

            if (masterRecords == null || !masterRecords.Any())
            {
                return await OK(new DataResult<List<SupplierDeliveryOrderModel>>
                {
                    StatusCode = 200,
                    Entity = new List<SupplierDeliveryOrderModel>()
                });
            }

            var masterIds = masterRecords.Select(p => p.FSUPPLIER_DELIVERY_ORDER_ID).ToList();

            var allDeliveryMaterials = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL, T_PURD_OUT_ORDER>
                ((a, b) => new JoinQueryInfos(JoinType.Left, a.FOUT_ORDER_ID == b.FOUT_ORDER_ID))
                .Where((a, b) => masterIds.Contains(a.FSUPPLIER_DELIVERY_ORDER_ID))
                .Select<SupplierDeliveryOrderImsModel>()
                .OrderBy(a => a.FSHOW_SEQNO, OrderByType.Asc)
                .ToListAsync();

            var outOrderMatIds = allDeliveryMaterials.Select(p => p.FOUT_ORDER_MATERIAL_ID).Distinct().ToList();
            if (outOrderMatIds.Any())
            {
                var originalOrderMaterials = await db.Queryable<T_PURD_OUT_ORDER_MATERIAL>()
                   .Where(it => outOrderMatIds.Contains(it.FOUT_ORDER_MATERIAL_ID))
                   .ToListAsync();

                // 【新增】第三次查询：获取这些委外订单行已经收货的数量(RecvCount)
                var receivedMaterials = await db.Queryable<T_PURD_OUT_RECV_MATERIAL, T_PURD_OUT_RECV_STATUS>(
                    (a, b) => new JoinQueryInfos(JoinType.Left, a.FOUT_RECV_ID == b.FOUT_RECV_ID))
                    .Where((a, b) => outOrderMatIds.Contains(a.FSOURCE_DETAIL_ID) && b.FIF_CANCEL == false)
                    .Select(a => new { a.FSOURCE_DETAIL_ID, a.FPRO_UNIT_QTY })
                    .ToListAsync();

                // 获取工艺信息
                var workCraftIds = originalOrderMaterials.Select(p => p.FWORK_ORDER_CRAFT_ID).Distinct().ToList();
                var workService = GetService<IMES003WorkOrderService>("MES003WorkOrder");
                var workCraftInfos = await workService.GetWorkOrdCraftByIdsAsync(workCraftIds);

                var matIds = allDeliveryMaterials.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                var matInfos = await _businessService.GetMaterialByIdsAsync(matIds);
                var matUnitIds = matInfos.Entity.SelectMany(p => new List<string>() { p.FUNIT_ID, p.FPRO_UNIT_ID }).Distinct().ToList();
                var unitInfos = await _businessService.GetUnitNameByIdsAsync(matUnitIds);

                allDeliveryMaterials.AsParallel().ForAll(item =>
                {
                    var matdata = matInfos.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                    if (matdata != null)
                    {
                        item.FMATERIAL_CODE = matdata.FMATERIAL_CODE;
                        item.FMATERIAL_NAME = matdata.FMATERIAL_NAME;
                        item.FSPEC_DESC = matdata.FSPEC_DESC;
                        var proUnitData = unitInfos.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FPRO_UNIT_ID);
                        if (proUnitData != null) item.FPRO_UNIT_NAME = proUnitData.FUNIT_NAME;
                    }

                    var originalLine = originalOrderMaterials.FirstOrDefault(p => p.FOUT_ORDER_MATERIAL_ID == item.FOUT_ORDER_MATERIAL_ID);
                    if (originalLine != null)
                    {
                        item.OrdCount = originalLine.FPRO_UNIT_QTY; // 委外数量
                        item.RecvCount = receivedMaterials.Where(r => r.FSOURCE_DETAIL_ID == originalLine.FOUT_ORDER_MATERIAL_ID).Sum(r => r.FPRO_UNIT_QTY); // 已收货数量

                        // 关联工艺信息
                        var craftInfo = workCraftInfos.Entity.FirstOrDefault(c => c.FWORK_ORDER_CRAFT_ID == originalLine.FWORK_ORDER_CRAFT_ID);
                        if (craftInfo != null)
                        {
                            item.FCRAFT_CODE = craftInfo.FCRAFT_CODE;
                            item.FCRAFT_NAME = craftInfo.FCRAFT_NAME;
                            item.FCRAFT_ID = craftInfo.FCRAFT_ID;

                            item.FWORK_ORDER_NO = craftInfo.FWORK_ORDER_NO;
                        }
                    }

                });
            }

            masterRecords.AsParallel().ForAll(master =>
            {
                master.Items = allDeliveryMaterials
                    .Where(m => m.FSUPPLIER_DELIVERY_ORDER_ID == master.FSUPPLIER_DELIVERY_ORDER_ID)
                    .ToList();
            });

            DataResult<List<SupplierDeliveryOrderModel>> dataResult = new DataResult<List<SupplierDeliveryOrderModel>>()
            {
                Entity = masterRecords,
                StatusCode = 200,
                Pager = new PagerResult()
                {
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                    TotalRecords = totalCount.Value,
                },
            };

            return await OK(dataResult);
        }

    }
}
