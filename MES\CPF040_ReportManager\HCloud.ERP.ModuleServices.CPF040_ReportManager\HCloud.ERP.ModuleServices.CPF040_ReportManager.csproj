﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	
    <OutputPath>F:\modules\mes-system-web\Mesroot\MicroServer\Modules</OutputPath>
  </PropertyGroup>  

  <ItemGroup>
    <PackageReference Include="HCloud.Core.ProxyGenerator" Version="1.0.30" />
    <PackageReference Include="HCloud.Core.HCPlatform" Version="1.0.91" />
  </ItemGroup>  

  <ItemGroup>
    <ProjectReference Include="..\HCloud.ERP.IModuleServices.CPF040_ReportManager\HCloud.ERP.IModuleServices.CPF040_ReportManager.csproj" />
  </ItemGroup>  

  <ItemGroup>
    <Reference Include="HCloud.Core.Common">
      <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.Core.Common.dll</HintPath>
    </Reference>
    <Reference Include="HCloud.Core.Sugar">
      <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.Core.Sugar.dll</HintPath>
    </Reference>
    <Reference Include="HCloud.Core.System">
      <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.Core.System.dll</HintPath>
    </Reference>
    <Reference Include="HCloud.ERP.IModuleServices.CPF007_Program">
      <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.CPF007_Program.dll</HintPath>
    </Reference>
    <Reference Include="HCloud.ERP.IModuleServices.CPF009_DataDic">
      <HintPath>..\..\..\..\mes-system-web\Mesroot\MicroServer\Modules\HCloud.ERP.IModuleServices.CPF009_DataDic.dll</HintPath>
    </Reference>
  </ItemGroup>  



</Project>
