﻿using HCloud.Core.Common;
using HCloud.Core.HCPlatform.Auth.Internal;
using HCloud.Core.HCPlatform.CommonData;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.ProxyGenerator.Models;
using HCloud.Core.Sugar;
using HCloud.ERP.IModuleServices.IU011_PurInquiry.Models;
using HCloud.ERP.IModuleServices.MES017_TAG;
using HCloud.ERP.IModuleServices.MES017_TAG.Models.Response;
using HCloud.ERP.IModuleServices.PUR011_OutOrder.Models;
using HCloud.ERP.IModuleServices.PUR022_SupplierOrder.Models.DataModel;
using HCloud.ERP.IModuleServices.PUR024_SupplierDeliveryOrder.Models.DataModel;
using HCloud.ERP.IModuleServices.PUR024_SupplierDeliveryOrder.Models.Response;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Crypto.Agreement.Srp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HCloud.ERP.ModuleServices.PUR024_SupplierDeliveryOrder
{
    public partial class PUR024SupplierDeliveryOrderService
    {
        /// <summary>
        /// 保存数据
        /// </summary>
        /// <returns></returns>
        public async Task<DataResult<List<SupplierDeliveryOrderModel>>> SaveAsync(SupplierDeliveryOrderModel model)
        {
            if (model == null)
            {
                ERROR(null, 100010, string.Format(_multiLang["执行失败, 传入参数 {0} 为空."], "model"));
            }
            var _Isadd = false;
            if (string.IsNullOrWhiteSpace(model.FSUPPLIER_DELIVERY_ORDER_ID))
            {
                _Isadd = true;
            }
            //保存前检查
            if (!(await SaveCheckAsync(model)))
            {
                ERROR(null, 100100, _multiLang["保存验证失败."]);
            }
            var db = _isugar.DB;
            try
            {
                var user = await _iauth.GetUserAccountAsync();
                var _mainDB = _isugar.GetDB("MAIN");
                var FPROVIDER_IDS = (await _mainDB.Queryable<T_MSDM_TRAN_ENTITY>()
                    .Where(n => n.FEMP_ID == user.UserPsnId)
                    .Select(n => n.FTRAN_ENTITY_ID)
                    .ToListAsync()).Distinct().ToList();
                if (FPROVIDER_IDS == null || FPROVIDER_IDS.Count() == 0)
                {
                    ERROR(null, 100600, _multiLang["保存失败.该账号未绑定供应商，无法进行订单操作!"]);
                }

                var _mst = model.MapToDestObj<SupplierDeliveryOrderModel, T_PURD_SUPPLIER_DELIVERY_ORDER>();
                _mst.FPROVIDER_ID = FPROVIDER_IDS[0];

                model.Items.ForEach(item =>
                {
                    if (string.IsNullOrWhiteSpace(item.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID))
                    {
                        item.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID = GuidHelper.NewGuid();
                    }
                });

                var _mstItemAdd = model.Items.MapToDestObj<SupplierDeliveryOrderImsModel, T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL>();
                _mstItemAdd.ForEach(item => {
                    item.FSUPPLIER_DELIVERY_ORDER_ID = _mst.FSUPPLIER_DELIVERY_ORDER_ID;
                });

                var _TagItemAdd = new List<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL_TAG>();
                //状态
                var _mst_Status = model.MapToDestObj<SupplierDeliveryOrderModel, T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>();
                //扩展
                var _mst_Custom = model.MapToDestObj<SupplierDeliveryOrderModel, T_PURD_SUPPLIER_DELIVERY_ORDER_CUSTOM>();
                var rpcServer = this.GetService<IMES017TagService>("MES017Tag");

                foreach (var item in model.Items)
                {
                    if (item.TagItems != null && item.TagItems.Count > 0)
                    {
                        var _itemTagAddList = item.TagItems.ToList().MapToDestObj<SupplierDeliveryOrderImsTagModel, T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL_TAG>();

                        if (_itemTagAddList != null && _itemTagAddList.Count > 0)
                        {
                            var _genlist = new List<TagGenModel>();
                            _itemTagAddList.ForEach(x =>
                            {
                                x.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID = item.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID;
                                x.FSUPPLIER_DELIVERY_ORDER_MATERIAL_TAG_ID = string.IsNullOrWhiteSpace(x.FSUPPLIER_DELIVERY_ORDER_MATERIAL_TAG_ID) ? GuidHelper.NewGuid() : x.FSUPPLIER_DELIVERY_ORDER_MATERIAL_TAG_ID;
                                x.FMATERIAL_ID = item.FMATERIAL_ID;
                                x.FSUPPLIER_DELIVERY_ORDER_ID = _mst.FSUPPLIER_DELIVERY_ORDER_ID;
                                _genlist.Add(new TagGenModel { GEN_NO = x.FMATERIAL_ID, FBILL_SOURCE_ITEM_ID = x.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID, FBILL_SOURCE_NO = _mst.FSUPPLIER_DELIVERY_ORDER_NO, FSTK_QTY = x.FPRODUCT_NUM, FBILL_TYPE = "SupDelOrderOutScan".ToUpper() });
                            });
                            _TagItemAdd.AddRange(_itemTagAddList);
                            var TagList = await rpcServer.GenerateOrderCodeList(_genlist);
                            if (TagList.StatusCode != 200)
                            {
                                ERROR(null, TagList.StatusCode, TagList.Message);
                            }
                            _itemTagAddList.ForEach(x => x.FBARCODE_NO = TagList.Entity.FirstOrDefault(n => n.FBILL_SOURCE_ITEM_ID == x.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID)?.FBARCODE_NO);

                        }
                    }
                }

                db.BeginTran();
                if (_Isadd)
                {
                    _mst_Status.FSUPPLIER_DELIVERY_ORDER_ID = _mst.FSUPPLIER_DELIVERY_ORDER_ID;
                    _mst_Custom.FSUPPLIER_DELIVERY_ORDER_ID = _mst.FSUPPLIER_DELIVERY_ORDER_ID;
                    _mst_Custom.FSUPPLIER_DELIVERY_ORDER_CUSTOM_ID = GuidHelper.NewGuid();
                    await db.Insertable<T_PURD_SUPPLIER_DELIVERY_ORDER>(_mst).IgnoreColumns(p => new { p.FMODIDATE, p.FMODIFIER, p.FMODIFIER_ID }).ExecuteCommandAsync();
                    await db.Insertable<T_PURD_SUPPLIER_DELIVERY_ORDER_CUSTOM>(_mst_Custom).ExecuteCommandAsync();
                    await db.Insertable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL>(_mstItemAdd).IgnoreColumns(p => new { p.FMODIDATE, p.FMODIFIER, p.FMODIFIER_ID }).ExecuteCommandAsync();
                    await db.Insertable<T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>(_mst_Status).IgnoreColumns(p => new
                    {
                        p.FAPPDATE,
                        p.FAPPROVER,
                        p.FAPPROVE_ID,
                        p.FCANCELDATE,
                        p.FCANCELER,
                        p.FCANCEL_ID,

                    }).ExecuteCommandAsync();
                    await db.Insertable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL_TAG>(_TagItemAdd).IgnoreColumns(p => new { p.FMODIDATE, p.FMODIFIER, p.FMODIFIER_ID }).ExecuteCommandAsync();
                }
                else
                {
                    //更新主表
                    await db.Updateable<T_PURD_SUPPLIER_DELIVERY_ORDER>(_mst).IgnoreColumns(p => new { p.FCDATE, p.FCREATOR, p.FCREATOR_ID }).ExecuteCommandAsync();
                    if (_mstItemAdd != null && _mstItemAdd.Count > 0)
                    {
                        await db.Deleteable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL>().Where(n => n.FSUPPLIER_DELIVERY_ORDER_ID == _mst.FSUPPLIER_DELIVERY_ORDER_ID).ExecuteCommandAsync();
                        await db.Insertable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL>(_mstItemAdd).IgnoreColumns(p => new { p.FMODIDATE, p.FMODIFIER, p.FMODIFIER_ID }).ExecuteCommandAsync();
                    }

                    if (_TagItemAdd != null && _TagItemAdd.Count > 0)
                    {
                        //清空对应的物料标签
                        await db.Deleteable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL_TAG>().Where(n => n.FSUPPLIER_DELIVERY_ORDER_ID == _mst.FSUPPLIER_DELIVERY_ORDER_ID).ExecuteCommandAsync();
                        await db.Insertable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL_TAG>(_TagItemAdd).IgnoreColumns(p => new { p.FMODIDATE, p.FMODIFIER, p.FMODIFIER_ID }).ExecuteCommandAsync();
                    }
                }
                db.CommitTran();
             
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }

            QueryRequestModel Querymodel = new QueryRequestModel()
            {
                WhereGroup = new QueryWhereGroupModel(),
                PageIndex = 1,
                PageSize = 30,
            };
            Querymodel.WhereGroup.GroupType = EnumGroupType.AND;
            Querymodel.WhereGroup.Items = new List<QueryWhereItemModel>();

            //主表数据
            var queryResult = await GetAllAsync(Querymodel);
            if (queryResult.StatusCode != 200)
            {
                ERROR(queryResult, queryResult.StatusCode, queryResult.Message);
            }
            return queryResult;
        }






        //效验数据
        private async Task<bool> SaveCheckAsync(SupplierDeliveryOrderModel model)
        {

            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            if (model == null)
            {
                ERROR(null, 100010, string.Format(_multiLang["传入参数 {0} 为null"], "model"));
            }



            bool isAdd = string.IsNullOrWhiteSpace(model.FSUPPLIER_DELIVERY_ORDER_ID);


            if (!isAdd)//新增
            {

                //验证是否已删除/已审核/已作废
                var dbData = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER, T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>
                    ((a, b) => new JoinQueryInfos(JoinType.Left, a.FSUPPLIER_DELIVERY_ORDER_ID == b.FSUPPLIER_DELIVERY_ORDER_ID))
                    .Where((a, b) => a.FSUPPLIER_DELIVERY_ORDER_ID == model.FSUPPLIER_DELIVERY_ORDER_ID)
                    .Select((a, b) => new
                    {
                        a.FSUPPLIER_DELIVERY_ORDER_ID,
                        b.FCFLAG,
                        b.FIF_CANCEL,
                    }).FirstAsync();

                if (dbData == null)
                {
                    ERROR(null, 100020, _multiLang["记录已不存在."]);
                }

                if (dbData.FCFLAG == 1)
                {
                    ERROR(null, 100030, _multiLang["记录已审核,不能修改保存."]);
                }
                if (dbData.FIF_CANCEL)
                {
                    ERROR(null, 100050, _multiLang["记录已作废,不能修改保存."]);
                }
            }

            if (isAdd)
            {
                //新增
                model.FSUPPLIER_DELIVERY_ORDER_ID = _commonDataProvider.NewGuid();   //id
                model.FSUPPLIER_DELIVERY_ORDER_STATUS_ID = _commonDataProvider.NewGuid();
            }

            //自动产生单号
            if (string.IsNullOrWhiteSpace(model.FSUPPLIER_DELIVERY_ORDER_NO))
            {
                if (!await GenNoAsync(model))
                {
                    ERROR(null, 100051, _multiLang["产生发货单编号错误."]);
                }
            }

            //判断编号是否存在
            var isExists = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER>()
                .Where(p => p.FSUPPLIER_DELIVERY_ORDER_NO == model.FSUPPLIER_DELIVERY_ORDER_NO && p.FSUPPLIER_DELIVERY_ORDER_ID != model.FSUPPLIER_DELIVERY_ORDER_ID)
                .AnyAsync();
            if (isExists)
            {
                ERROR(null, 100063, string.Format(_multiLang["发货单编号{0}已存在, 不能重复."], model.FSUPPLIER_DELIVERY_ORDER_NO));
            }


            //判断出库明细是否为空
            if (model.Items == null || model.Items.Count == 0)
            {
                ERROR(null, 111111, $"发货明细不能为空");
            }
            //判断出库明细出库数量是否为空
            if (model.Items.Any(m => m.FDELIVERY_QTY == null || m.FDELIVERY_QTY <= 0))
            {
                ERROR(null, 111111, $"发货明细出库数不能为0");
            }
            var _FSUPPLIER_ORDER_MATERIAL_IDs = model.Items.Select(n => n.FSUPPLIER_ORDER_MATERIAL_ID).Distinct().ToList();
            var _SupplierOrderMaterialList = await db.Queryable<T_PURD_SUPPLIER_ORDER_MATERIAL>().Where(n => _FSUPPLIER_ORDER_MATERIAL_IDs.Contains(n.FSUPPLIER_ORDER_MATERIAL_ID)).ToListAsync();
            //判断发货数是否大于客户订单发货数量
            //判断标签数量和出库明细数量一致
            foreach (var item in model.Items)
            {
                if (item.TagItems != null && item.TagItems.Count > 0)
                {

                    if (item.FDELIVERY_QTY != item.TagItems.Sum(n => n.FPRODUCT_NUM))
                    {
                        ERROR(null, 111111, $"存在物料标签管理，但标签产品总数{item.TagItems.Sum(n => n.FPRODUCT_NUM)}和发货单的对应的物料明细发货数量{item.FDELIVERY_QTY}不一致");
                    }
                }


            }
            foreach (var item in _SupplierOrderMaterialList)
            {
                if (item.FPRO_UNIT_QTY - item.FEND_DELIVERY_QTY < model.Items.Where(n => n.FSUPPLIER_ORDER_MATERIAL_ID == item.FSUPPLIER_ORDER_MATERIAL_ID).Sum(n => n.FDELIVERY_QTY))
                {
                    ERROR(null, 111111, $"发货量：{model.Items.Where(n => n.FSUPPLIER_ORDER_MATERIAL_ID == item.FSUPPLIER_ORDER_MATERIAL_ID).Sum(n => n.FDELIVERY_QTY)}大于客户订单的待发货数：{item.FPRO_UNIT_QTY - item.FEND_DELIVERY_QTY}");
                }
            }

            return true;

        }


        /// <summary>
        /// 产生编号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<bool> GenNoAsync(SupplierDeliveryOrderModel model)
        {
            GenBillCodeModel billModel = new GenBillCodeModel()
            {
                FBILL_COUNT = 1,
                FBILL_RULE_CODE = "BR-CPF-0110",
                FENTITY_DESC = "送货单-供应商",
                FENTITY_NAME = "T_PURD_SUPPLIER_DELIVERY_ORDER",
                FFIELD_DESC = "送货单编号-供应商",
                FFIELD_NAME = "FSUPPLIER_DELIVERY_ORDER_NO",
                Parms = new List<GenBillCodeParmModel> { }
            };
            billModel.Parms.Add(new GenBillCodeParmModel
            {
                FPARM_CODE = "Date",
                FPARM_VALUE = _iauth.GetCurDateTime().ToString("yyyyMMdd"),
            });

            var billResult = await _commonDataProvider.GenEntBillCodeAsync(billModel);
            if (billResult == null || billResult.Count == 0 || string.IsNullOrWhiteSpace(billResult[0]))
            {
                ERROR(null, 100600, _multiLang["编码规则生成编号为空, 需检查系统编码配置."]);
            }
            model.FSUPPLIER_DELIVERY_ORDER_NO = billResult[0];

            return await OK(true);
        }

        #region 审核
        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<dynamic>> ApproveAsync(List<string> ids)
        {
            //审核前检查
            if (!await ApproveCheckAsync(ids, "ApproveCheck"))
            {
                ERROR(null, 106000, _multiLang["审核检查失败."]);
            }

            var db = _isugar.DB;

            var user = await _iauth.GetUserAccountAsync();
            var carftscheduleOrdIds = new List<string>();
            List<SupplierDeliveryOrderModel> outOrds = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER, T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>
               ((a, b) => new JoinQueryInfos(JoinType.Left, a.FSUPPLIER_DELIVERY_ORDER_ID == b.FSUPPLIER_DELIVERY_ORDER_ID))
               .OrderBy((a, b) => a.FCDATE, OrderByType.Desc)  //按建立日期倒序
               .Where((a, b) => ids.Contains(a.FSUPPLIER_DELIVERY_ORDER_ID))
               .Select<SupplierDeliveryOrderModel>()
               .ToListAsync();

            try
            {
                db.BeginTran();

                await db.Updateable<T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>(t => new T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS
                {
                    FCFLAG = 1,
                    FAPPDATE = _iauth.GetCurDateTime(),
                    FAPPROVER = user.UserPsnName,
                    FAPPROVE_ID = user.UserPsnId,
                })
                .Where(t => ids.Contains(t.FSUPPLIER_DELIVERY_ORDER_ID))
                .UpdateColumns(t => new { t.FCFLAG, t.FAPPDATE, t.FAPPROVER, t.FAPPROVE_ID })
                .ExecuteCommandAsync();
                //更新对应的客户订单已发货数
                var _SupDelItemOrder = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL>().Where(n => ids.Contains(n.FSUPPLIER_DELIVERY_ORDER_ID)).ToListAsync();
                var _Supitemids = _SupDelItemOrder.Select(n => n.FSUPPLIER_ORDER_MATERIAL_ID).Distinct().ToList();
                var _Outitemids = _SupDelItemOrder.Select(n => n.FOUT_ORDER_MATERIAL_ID).Distinct().ToList();
                var _SupItemOrder = await db.Queryable<T_PURD_SUPPLIER_ORDER_MATERIAL>().Where(n => _Supitemids.Contains(n.FSUPPLIER_ORDER_MATERIAL_ID)).ToListAsync();
                var _OutitemOrder = await db.Queryable<T_PURD_OUT_ORDER_MATERIAL>().Where(n => _Outitemids.Contains(n.FOUT_ORDER_MATERIAL_ID)).ToListAsync();
                foreach (var item in _SupItemOrder)
                {
                    if (item.FEND_DELIVERY_QTY == null)
                    {
                        item.FEND_DELIVERY_QTY = 0;
                    }
                    item.FEND_DELIVERY_QTY += _SupDelItemOrder.Where(n => n.FSUPPLIER_ORDER_MATERIAL_ID == item.FSUPPLIER_ORDER_MATERIAL_ID).Sum(n => n.FDELIVERY_QTY);
                }
                foreach (var item in _OutitemOrder)
                {
                    if (item.FEND_DELIVERY_QTY == null)
                    {
                        item.FEND_DELIVERY_QTY = 0;
                    }
                    item.FEND_DELIVERY_QTY += _SupDelItemOrder.Where(n => n.FOUT_ORDER_MATERIAL_ID == item.FOUT_ORDER_MATERIAL_ID).Sum(n => n.FDELIVERY_QTY);
                }

                await db.Updateable<T_PURD_SUPPLIER_ORDER_MATERIAL>(_SupItemOrder)
               .UpdateColumns(t => new { t.FEND_DELIVERY_QTY })
               .ExecuteCommandAsync();

                await db.Updateable<T_PURD_OUT_ORDER_MATERIAL>(_OutitemOrder)
             .UpdateColumns(t => new { t.FEND_DELIVERY_QTY })
             .ExecuteCommandAsync();
                db.CommitTran();
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }

            return new DataResult<dynamic>
            {
                StatusCode = 200,
                Message = "审核成功"
            };
        }
        #endregion

        /// <summary>
        /// 反审核
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<dynamic>> UnApproveAsync(List<string> ids)
        {
            //反审核前检查
            if (!await ApproveCheckAsync(ids, "UnApproveCheck"))
            {
                ERROR(null, 106100, _multiLang["反审核检查失败."]);
            }


            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;
            try
            {
                db.BeginTran();
                await db.Updateable<T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>(t => new T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS
                {
                    FCFLAG = 2,
                    FAPPDATE = _iauth.GetCurDateTime(),
                    FAPPROVER = user.UserPsnName,
                    FAPPROVE_ID = user.UserPsnId,
                })
                 .Where(t => ids.Contains(t.FSUPPLIER_DELIVERY_ORDER_ID))
                 .UpdateColumns(t => new { t.FCFLAG, t.FAPPDATE, t.FAPPROVER, t.FAPPROVE_ID })
                 .ExecuteCommandAsync();

                //更新对应的客户订单已发货数
                var _SupDelItemOrder = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL>().Where(n => ids.Contains(n.FSUPPLIER_DELIVERY_ORDER_ID)).ToListAsync();
                var _Supitemids = _SupDelItemOrder.Select(n => n.FSUPPLIER_ORDER_MATERIAL_ID).Distinct().ToList();
                var _Outitemids = _SupDelItemOrder.Select(n => n.FOUT_ORDER_MATERIAL_ID).Distinct().ToList();
                var _SupItemOrder = await db.Queryable<T_PURD_SUPPLIER_ORDER_MATERIAL>().Where(n => _Supitemids.Contains(n.FSUPPLIER_ORDER_MATERIAL_ID)).ToListAsync();
                var _OutitemOrder = await db.Queryable<T_PURD_OUT_ORDER_MATERIAL>().Where(n => _Outitemids.Contains(n.FOUT_ORDER_MATERIAL_ID)).ToListAsync();
                foreach (var item in _SupItemOrder)
                {
                    if (item.FEND_DELIVERY_QTY == null)
                    {
                        item.FEND_DELIVERY_QTY = 0;
                    }
                    item.FEND_DELIVERY_QTY -= _SupDelItemOrder.Where(n => n.FSUPPLIER_ORDER_MATERIAL_ID == item.FSUPPLIER_ORDER_MATERIAL_ID).Sum(n => n.FDELIVERY_QTY);
                }
                foreach (var item in _OutitemOrder)
                {
                    if (item.FEND_DELIVERY_QTY == null)
                    {
                        item.FEND_DELIVERY_QTY = 0;
                    }
                    item.FEND_DELIVERY_QTY -= _SupDelItemOrder.Where(n => n.FOUT_ORDER_MATERIAL_ID == item.FOUT_ORDER_MATERIAL_ID).Sum(n => n.FDELIVERY_QTY);
                }

                await db.Updateable<T_PURD_SUPPLIER_ORDER_MATERIAL>(_SupItemOrder)
               .UpdateColumns(t => new { t.FEND_DELIVERY_QTY })
               .ExecuteCommandAsync();

                await db.Updateable<T_PURD_OUT_ORDER_MATERIAL>(_OutitemOrder)
             .UpdateColumns(t => new { t.FEND_DELIVERY_QTY })
             .ExecuteCommandAsync();
                db.CommitTran();

                return new DataResult<dynamic>
                {
                    StatusCode = 200,
                    Message = "反审核成功"
                };
            }
            catch (Exception)
            {

                db.RollbackTran();
                throw;
            }


        }


        #region 删除
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<dynamic>> DeleteAsync(List<string> ids)
        {
            var user = await _iauth.GetUserAccountAsync(); ;

            //删除前检查 
            if (!await ApproveCheckAsync(ids, "DeleteOrCancelCheck"))
            {
                ERROR(null, 100700, _multiLang["删除检查失败."]);
            }

            var db = _isugar.DB;
            try
            {
                db.BeginTran();

                //更新对应的客户订单已发货数
                //var _SupDelItemOrder = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL>().Where(n => ids.Contains(n.FSUPPLIER_DELIVERY_ORDER_ID)).ToListAsync();
                //var _Supitemids = _SupDelItemOrder.Select(n => n.FSUPPLIER_ORDER_MATERIAL_ID).Distinct().ToList();
                //var _SupItemOrder = await db.Queryable<T_PURD_SUPPLIER_ORDER_MATERIAL>().Where(n => _Supitemids.Contains(n.FSUPPLIER_ORDER_MATERIAL_ID)).ToListAsync();

                await db.Deleteable<T_PURD_SUPPLIER_DELIVERY_ORDER>().Where(p => ids.Contains(p.FSUPPLIER_DELIVERY_ORDER_ID)).ExecuteCommandAsync();
                await db.Deleteable<T_PURD_SUPPLIER_DELIVERY_ORDER_CUSTOM>().Where(p => ids.Contains(p.FSUPPLIER_DELIVERY_ORDER_ID)).ExecuteCommandAsync();
                await db.Deleteable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL>().Where(p => ids.Contains(p.FSUPPLIER_DELIVERY_ORDER_ID)).ExecuteCommandAsync();
                await db.Deleteable<T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL_TAG>().Where(p => ids.Contains(p.FSUPPLIER_DELIVERY_ORDER_ID)).ExecuteCommandAsync();
                await db.Deleteable<T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>().Where(p => ids.Contains(p.FSUPPLIER_DELIVERY_ORDER_ID)).ExecuteCommandAsync();

                // foreach (var item in _SupItemOrder)
                // {
                //     item.FEND_DELIVERY_QTY -= _SupDelItemOrder.Where(n => n.FSUPPLIER_ORDER_MATERIAL_ID == item.FSUPPLIER_ORDER_MATERIAL_ID).Sum(n => n.FDELIVERY_QTY);
                // }

                // await db.Updateable<T_PURD_SUPPLIER_ORDER_MATERIAL>(_SupItemOrder)
                //.UpdateColumns(t => new { t.FEND_DELIVERY_QTY })
                //.ExecuteCommandAsync();

                db.CommitTran();
            }
            catch (Exception ex)
            {
                db.RollbackTran();
                throw;
            }
            return new DataResult<dynamic>
            {
                StatusCode = 200,
                Message = "删除成功"
            };



        }



        #endregion

        #region 作废
        /// <summary>
        /// 作废
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<DataResult<dynamic>> CancelAsync(List<string> ids)
        {
            var user = await _iauth.GetUserAccountAsync();


            //作废前检查 
            if (!await ApproveCheckAsync(ids, "DeleteOrCancelCheck"))
            {
                ERROR(null, 100700, _multiLang["作废检查失败."]);
            }

            var db = _isugar.DB;


            //更新作废状态
            await db.Updateable<T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>(t => new T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS
            {
                FIF_CANCEL = true,
                FCANCELDATE = _iauth.GetCurDateTime(),
                FCANCELER = user.UserPsnName,
                FCANCEL_ID = user.UserPsnId,
            })
            .Where(t => ids.Contains(t.FSUPPLIER_DELIVERY_ORDER_ID))
            .UpdateColumns(t => new { t.FIF_CANCEL, t.FCANCELDATE, t.FCANCELER, t.FCANCEL_ID })

            .ExecuteCommandAsync();

            return new DataResult<dynamic>
            {
                StatusCode = 200,
                Message = "作废成功"
            };
        }
        #endregion

        /// <summary>
        /// 检查
        /// </summary>
        private async Task<bool> ApproveCheckAsync(List<string> ids, string checkType)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;
            //
            var billNo = await db.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER, T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS>
            ((a, b) => new JoinQueryInfos(JoinType.Left, a.FSUPPLIER_DELIVERY_ORDER_ID == b.FSUPPLIER_DELIVERY_ORDER_ID))
            .Where((a, b) => ids.Contains(a.FSUPPLIER_DELIVERY_ORDER_ID))
            .Select((a, b) => new { a.FSUPPLIER_DELIVERY_ORDER_NO, b.FCFLAG, b.FIF_CANCEL, b.FSUPPLIER_DELIVERY_ORDER_ID, a.FRECEIVER })
            .ToListAsync();

            //是否作废
            var bill = billNo.Where(b => b.FIF_CANCEL == true).FirstOrDefault();
            if (bill != null)
            {
                ERROR(null, 106112, string.Format(_multiLang["执行失败:编号{0},已作废."], bill.FSUPPLIER_DELIVERY_ORDER_NO));
            }

            if (checkType == "UnApproveCheck")//反审核检查
            {
                bill = billNo.Where(b => b.FCFLAG == 0 || b.FCFLAG == 2).FirstOrDefault();
                if (bill != null)
                {
                    ERROR(null, 106110, string.Format(_multiLang["执行失败:编号{0},已反审核."], bill.FSUPPLIER_DELIVERY_ORDER_NO));
                }
                if (billNo.Any(n => !string.IsNullOrWhiteSpace(n.FRECEIVER)))
                {
                    ERROR(null, 106110, string.Format(_multiLang["执行失败:编号{0},已接收."], bill.FSUPPLIER_DELIVERY_ORDER_NO));
                }
            }

            if (checkType == "DeleteOrCancelCheck")//删除||作废检查
            {
                bill = billNo.Where(b => b.FCFLAG == 1).FirstOrDefault();
                if (bill != null)
                {
                    ERROR(null, 100720, string.Format(_multiLang["执行失败:编号{0},已审核."], bill.FSUPPLIER_DELIVERY_ORDER_NO));

                }
                if (billNo.Any(n => !string.IsNullOrWhiteSpace(n.FRECEIVER)))
                {
                    ERROR(null, 106110, string.Format(_multiLang["执行失败:编号{0},已接收."], bill.FSUPPLIER_DELIVERY_ORDER_NO));
                }

            }
            if (checkType == "ApproveCheck")//审核检查
            {
                bill = billNo.Where(b => b.FCFLAG == 1).FirstOrDefault();
                if (bill != null)
                {
                    ERROR(null, 106010, string.Format(_multiLang["执行失败:编号{0},已审核."], bill.FSUPPLIER_DELIVERY_ORDER_NO));
                }
            }
            return await Task.FromResult(true);
        }

    }
}
