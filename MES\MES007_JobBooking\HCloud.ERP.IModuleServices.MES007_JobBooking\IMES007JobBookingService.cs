﻿using HCloud.Core.HCPlatform.Attributes;
using HCloud.Core.HCPlatform.Ioc;
using HCloud.Core.HCPlatform.Runtime.Server.Implementation.ServiceDiscovery.Attributes;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.ProxyGenerator.Models;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models.Response;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HCloud.ERP.IModuleServices.MES007_JobBooking
{
    [ServiceBundle("api/{Service}/{Method}")]
    public interface IMES007JobBookingService : IServiceKey
    {

        /// <summary>
        /// 查询工单工艺加工信息--根据生产工单ID
        /// </summary>
        Task<DataResult<List<CraftJobBookingModel>>> QueryOrderJOBAsync(List<string> ids);
        /// <summary>
        ///根据工位查询 工单ids
        /// </summary>
        Task<DataResult<List<string>>> QueryWOidsByStationAsync(List<string> ids);

        /// <summary>
        /// 查询工艺排程任务, 返回排程任务及进行中的加工任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<CraftScheduleJobModel>> QueryCraftScheduleJobAsync(CraftScheduleJobQueryModel model);

        /// <summary>
        /// 验证开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<JobBookingOperateModel>> ValidateStartJobAsync(JobBookingOperateModel model);

        /// <summary>
        /// 验证取消开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<JobBookingOperateModel>> ValidateCancelJobAsync(JobBookingOperateModel model);

        /// <summary>
        /// 验证取消完工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<JobBookingOperateModel>> ValidateCancelCompletionAsync(List<string> ids);

        /// <summary>
        /// 验证完工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<JobBookingOperateModel>> ValidateFinishJobAsync(JobBookingOperateModel model);

        /// <summary>
        /// 开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<CraftJobBookingModel>> StartJobAsync(JobBookingOperateModel model);



        /// <summary>
        /// 取消开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<CraftJobBookingModel>> CancelJobAsync(JobBookingOperateModel model);

        /// <summary>
        /// 暂停
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<CraftJobBookingModel>> PauseJobAsync(JobBookingOperateModel model);

        /// <summary>
        /// 继续
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<CraftJobBookingModel>> ResumeJobAsync(JobBookingOperateModel model);

        /// <summary>
        /// 完工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<CraftJobBookingModel>> FinishJobAsync(JobBookingOperateModel model);
        /// <summary>
        /// 完工申报---设备完工iuiu
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<bool>> EquFinishJobAsync(string id);
        /// <summary>
        /// 委外完工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<CraftJobBookingModel>> OutFinishJobAsync(JobBookingOperateModel model);

        /// <summary>
        /// 查询待加工排程任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<object>> QueryWaitJobsAsync(QueryRequestModel model);

        /// <summary>
        /// 查询待加工排程任务--悠悠工位机排程  去掉已完成的排程信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<object>> QueryWaitJobsIUIUAsync(QueryRequestModel model);

        /// <summary>
        /// 查询待加工排程任务--金技工位机排程  去掉已完成的排程信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<object>> QueryWaitJobsJINJIAsync(QueryRequestModel model);

        /// <summary>
        /// 查询待加工排程任务-------鑫海需要  完工后也可以打印
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<object>> QueryJobsAsync(QueryRequestModel model);

        /// <summary>
        /// 验证工单是否在加工中
        /// </summary>
        /// <param name="woIds"></param>
        /// <returns></returns>
        Task<DataResult<bool>> ValidateWorkOrderJobBookingAsync(List<string> woIds);

        /// <summary>
        /// 验证排程任务是否在加工中
        /// </summary>
        /// <param name="woIds"></param>
        /// <returns></returns>
        Task<DataResult<bool>> ValidateScheduleJobBookingAsync(List<string> schIds);


        /// <summary>
        /// 更换工位
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<CraftScheduleJobModel>> ChangeStationAsync(JobBookingOperateModel model);


        /// <summary>
        /// 根据排程任务id查询工艺报工
        /// </summary>
        Task<DataResult<List<CraftJobBookingModel>>> GetJobBookingByAsync(string schId);

        /// <summary>
        /// 根据排程任务id查询工艺报工
        /// </summary>
        Task<DataResult<List<CraftJobBookingModel>>> GetJobBookingByAsync(List<string> Ids);


        /// <summary>
        /// 根据加工id 获取加工信息
        /// </summary>
        Task<DataResult<CraftJobBookingDataModel>> QueryJobBookingAsync(string id);

        /// <summary>
        /// 修改加工数量
        /// </summary>
        Task<DataResult<UpdateJobBookingModel>> UpdateJobBookingAsync(UpdateJobBookingModel model);

        /// <summary>
        /// 取消完工
        /// </summary>
        /// <returns></returns>
        Task<DataResult<List<string>>> CancelCompletion(List<string> ids);

        /// <summary>
        /// 取消委外完工
        /// </summary>
        /// <returns></returns>
        Task<DataResult<List<string>>> CancelOutCompletion(List<string> ids);

        /// <summary>
        /// 根据加工任务ID  获取加工状态
        /// </summary>
        /// <returns></returns>
        Task<DataResult<List<CraftJobBookingModel>>> GetJobBookingStatusAsync(List<string> ids);

        /// <summary>
        /// 查询订单报工-订单列表
        /// </summary>
        [RuleCheck(IsRequired = true, RequiredParas = new string[] { "model" })]
        Task<DataResult<List<OrderBookingModel>>> QueryOrderBookingAsync(OrderBookingQueryModel model);

        /// <summary>
        /// 查询订单报工-工单列表
        /// </summary>
        [RuleCheck(IsRequired = true, RequiredParas = new string[] { "model" })]
        Task<DataResult<List<OrderBookingWOModel>>> QueryOrderBookingWOAsync(OrderBookingWOQueryModel model);

        /// <summary>
        /// 订单报工-完工确认
        /// </summary>
        [RuleCheck(IsRequired = true, RequiredParas = new string[] { "model" })]
        Task<DataResult<List<OrderBookingFinishModel>>> FinishOrderBookingWOAsync(List<OrderBookingFinishRequestModel> model);

        /// <summary>
        /// 订单报工-立即全部完工
        /// </summary>
        [RuleCheck(IsRequired = true, RequiredParas = new string[] { "model" })]
        Task<DataResult<List<OrderBookingFinishModel>>> FinishOrderBookingAllWOAsync(OrderBookingFinishAllRequestModel model);


        /// <summary>
        /// 批量开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<CraftJobBookingModel>>> BatchStartJobAsync(List<JobBookingOperateModel> models);

        /// <summary>
        /// 验证批量开工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<JobBookingOperateModel>>> BatchValidateStartJobAsync(List<JobBookingOperateModel> models);

        /// <summary>
        /// 批量暂停
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        Task<DataResult<List<CraftJobBookingModel>>> BatchPauseJobAsync(List<JobBookingOperateModel> models);

        /// <summary>
        /// 批量继续
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        Task<DataResult<List<CraftJobBookingModel>>> BatchResumeJobAsync(List<JobBookingOperateModel> models);

        /// <summary>
        /// 批量取消开工
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        Task<DataResult<List<CraftJobBookingModel>>> BatchCancelJobAsync(List<JobBookingOperateModel> models);

        /// <summary>
        /// 验证取消开工
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        Task<DataResult<List<JobBookingOperateModel>>> BatchValidateCancelJobAsync(List<JobBookingOperateModel> models);

        /// <summary>
        /// 批量完工
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        Task<DataResult<List<CraftJobBookingModel>>> BatchFinishJobAsync(List<JobBookingOperateModel> models);

        /// <summary>
        /// 批量完工并拆分批次
        /// </summary>
        /// <param name="models">包含拆分参数的报工操作模型列表</param>
        /// <returns>返回完工结果和拆分信息</returns>
        Task<DataResult<List<BatchSplitResultModel>>> BatchFinishJobWithSplitAsync(List<JobBookingOperateModel> models);

        /// <summary>
        /// 验证批次拆分参数
        /// </summary>
        /// <param name="models">报工操作模型列表</param>
        /// <returns>验证结果</returns>
        Task<DataResult<List<JobBookingOperateModel>>> ValidateBatchSplitAsync(List<JobBookingOperateModel> models);


        /// <summary>
        /// 查询待加工排程任务--金技工位机排程  去掉已完成的排程信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<object>> QueryWaitJobsChuangPaiAsync(QueryRequestModel model);
        /// <summary>
        /// 根据排程任务ID生成内外箱标签
        /// </summary>
        /// <returns></returns>
        Task<bool> CreatePackBarCodeAsync(string FCRAFT_SCHEDULE_ID);

        /// <summary>
        /// 根据排程ID获取内外箱标签
        /// </summary>
        /// <param name="FCRAFT_SCHEDULE_ID"></param>
        /// <returns></returns>
        Task<PackInfoModel> GetPackInfoAsync(string FCRAFT_SCHEDULE_ID);
        /// <summary>
        /// 下游工序退回不良品，并创建返工任务
        /// </summary>
        /// <param name="model">退回操作的数据模型</param>
        /// <returns>返回操作结果，成功则Entity为新的返工排程ID</returns>
        Task<DataResult<string>> RejectAndReturnAsync(RejectAndReturnModel model);

        /// <summary>
        /// 根据工单ID列表查询所有标签
        /// </summary>
        /// <param name="woOrdNos">工单编号列表</param>
        /// <returns>标签列表</returns>
        Task<DataResult<List<JobBookingTagModel>>> QueryTagsByWoOrdNosAsync(List<string> woOrdNos);

        /// <summary>
        /// 根据工单编号列表查询未绑定标签的排程任务
        /// </summary>
        /// <param name="model">排除和获取的工单编号</param>
        /// <returns>未绑定标签的标签列表</returns>
        Task<DataResult<List<JobBookingTagModel>>> QueryUnboundTagsByWoOrdNosAsync(QueryAvailableTagsModel model);
    }
}
