﻿using HCloud.Core.ProxyGenerator.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HCloud.ERP.IModuleServices.PUR024_SupplierDeliveryOrder.Models.Response
{
    public class SupplierDeliveryOrderImsTagModel: ModelBase
    {
        /// <summary>
        /// id
        /// </summary>
        public string FSUPPLIER_DELIVERY_ORDER_MATERIAL_TAG_ID { get; set; }

        /// <summary>
        /// 加工任务id
        /// </summary>
        public string FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID { get; set; }


        /// <summary>
        /// FSUPPLIER_DELIVERY_ORDER_ID
        /// </summary>
        public string FSUPPLIER_DELIVERY_ORDER_ID { get; set; }

        /// <summary>
        /// 产品数量
        /// </summary>
        public decimal? FPRODUCT_NUM { get; set; }

        /// <summary>
        /// 二维码
        /// </summary>
        public string? FBARCODE_URL { get; set; }

        /// <summary>
        /// 重量
        /// </summary>
        public decimal? FPRODUCT_WEIGHT { get; set; }

        /// <summary>
        /// 条码号 实际生成的完整条码编号
        /// </summary>
        public string FBARCODE_NO { get; set; }




        /// <summary>
        /// 接头
        /// </summary>
        public string FTEXT1
        {
            get;
            set;
        }
        /// <summary>
        /// 模号
        /// </summary>
        public string FTEXT2
        {
            get;
            set;
        }
        /// <summary>
        /// 班别
        /// </summary>
        public string FTEXT3
        {
            get;
            set;
        }
        /// <summary>
        /// 盘类
        /// </summary>
        public string FTEXT4
        {
            get;
            set;
        }
        /// <summary>
        /// 素材批号
        /// </summary>
        public string FTEXT5
        {
            get;
            set;
        }
        /// <summary>
        /// 作业员
        /// </summary>
        public string FTEXT6
        {
            get;
            set;
        }
        /// <summary>
        /// 检验员
        /// </summary>
        public string FTEXT7
        {
            get;
            set;
        }
        /// <summary>
        /// 机台号
        /// </summary>
        public string FTEXT8
        {
            get;
            set;
        }
        /// <summary>
        /// 电镀批号
        /// </summary>
        public string FTEXT9
        {
            get;
            set;
        }


        /// <summary>
        /// 检验员
        /// </summary>
        public string FCHECK_PERSON_NAME
        {
            get;
            set;
        }
        /// <summary>
        /// 作业员
        /// </summary>
        public string FWORK_PERSON_NAME
        {
            get;
            set;
        }



        /// <summary>
        /// FSHOW_SEQNO字段
        /// </summary>
        public string FSHOW_SEQNO { get; set; }
        /// 物料ID     
        /// </summary>
        public string FMATERIAL_ID { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        public string FMATERIAL_NAME { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        public string FMATERIAL_CODE { get; set; }
        /// <summary>
        /// 产品型号
        /// </summary>
        public string FGOODS_MODEL
        {
            get; set;
        }
        /// <summary>
        /// 生产单位ID
        /// </summary>
        public string FPRO_UNIT_NAME
        {
            get; set;
        }
        /// <summary>
        /// 单位编号
        /// </summary>
        public string FPRO_UNIT_CODE
        {
            get; set;
        }

        /// <summary>
        /// 生产单位数量
        /// </summary>
        public decimal FPRO_UNIT_QTY
        {
            get; set;
        }
        /// <summary>
        /// 物料产品规格
        /// </summary>
        public string FSPEC_DESC { get; set; }

        /// <summary>
        /// 生产单位
        /// </summary>
        public string FPRO_UNIT_ID { get; set; }

    }
}
