﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HCloud.ERP.IModuleServices.MES007_JobBooking.Models
{
    /// <summary>
    /// 下游工序退回不良品
    /// </summary>
    public class RejectAndReturnModel
    {
        /// <summary>
        /// 【必须】被退回的上游原始报工ID (T_MESD_CRAFT_JOB_BOOKING_ID)
        /// 例如，这是 2工位1 最初完成100件时的那条报工记录ID
        /// 加工任务ID
        /// </summary>
        [Required]
        public string FCRAFT_JOB_BOOKING_ID { get; set; }

        /// <summary>
        /// 【必须】本次退回的不良品数量
        /// </summary>
        [Required]
        public decimal FRET_QTY { get; set; }

        /// <summary>
        /// 【必须】发起退回的工位ID (例如, 3工位1 的ID)
        /// </summary>
        [Required]
        public string FSTATION_ID { get; set; }

        /// <summary>
        /// 不良原因 ,隔开
        /// </summary>
        public string FBAD { get; set; }

        /// <summary>
        /// 退回备注
        /// </summary>
        public string FREMARK { get; set; }
    }
}
