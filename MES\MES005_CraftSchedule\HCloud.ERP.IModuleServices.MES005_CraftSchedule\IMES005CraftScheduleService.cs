﻿using HCloud.Core.HCPlatform.Ioc;
using HCloud.Core.HCPlatform.Runtime.Server.Implementation.ServiceDiscovery.Attributes;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.ProxyGenerator.Models;

using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models.Request;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HCloud.ERP.IModuleServices.MES005_CraftSchedule
{
    [ServiceBundle("api/{Service}/{Method}")]
    public interface IMES005CraftScheduleService : IServiceKey
    {
        /// <summary>
        /// 查询排程任务 获取生产工单工时
        /// </summary>

        Task<DataResult<List<OrderSchedule>>> QueryOrderScheduleAsync(List<string> ids);

        /// <summary>
        /// 查询排程任务
        /// </summary>
        Task<DataResult<List<SimpleScheduleModel>>> SimpleScheduleAsync(List<string> ids);

        /// <summary>
        /// 获取简易排程任务
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<DataResult<List<SimpleCrfatScheduleModel>>> SimpleCrfatScheduleAsync(List<string> ids);

        /// <summary>
        /// 根据工单查询排程任务 工单是否排程
        /// </summary>
        Task<DataResult<List<WOCraftScheduleModel>>> WorkOrdIsScheduleAsync(List<string> ids);
        /// <summary>
        /// 根据工位查询工单ids
        /// </summary>
        Task<DataResult<List<string>>> WorkOrdsByStationAsync(List<string> ids);
        /// <summary>
        /// 查询待排程工单工艺
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<ScheduleModel>> QueryWaitScheduleAsync(QueryRequestModel model);


        /// <summary>
        /// 查询待排程工单工艺主表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<WOScheduleModel>>> QueryWaitScheduleMainAsync(QueryRequestModel model);

        /// <summary>
        /// 查询待排程工单数据明细--根据ID
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<WOCraftScheduleModel>>> QueryWaitScheduleDetailAsync(string id);

        /// <summary>
        /// 查询工单是否排程
        /// </summary>
        Task<DataResult<List<string>>> QueryWorkOrdScheduleAsync();

        /// <summary>
        /// 产生排程任务,返回排程批次Id
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        Task<DataResult<string>> GenScheduleTaskAsync(ScheduleModel model);

        /// <summary>
        /// 手工排程  --排序工位下得排程顺序
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        Task<DataResult<bool>> ManScheduleTaskAsync(List<ManualScheduling> model);


        /// <summary>
        /// 根据工单id产生排程任务
        /// </summary>
        /// <param name="woIds"></param>
        /// <returns></returns>
        Task<DataResult<object>> GenScheduleTaskByWoIdsAsync(List<string> woIds);


        /// <summary>
        /// 查询排程任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<WOCraftScheduleModel>>> QueryScheduleTaskAsync(QueryRequestModel model);
        /// <summary>
        /// 查询排程任务--用来手工排程
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<WOCraftScheduleModel>>> QueryScheduleTaskByStationIdAsync(QueryRequestModel model);
        /// <summary>
        /// 查询排程任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<WOCraftScheduleModel>>> QueryScheduleOutTaskAsync(List<string> Ids);

        /// <summary>
        /// 下发任务
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<DataResult<object>> ReleaseTaskByIdsAsync(List<string> ids);

        /// <summary>
        /// 撤回下发任务
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<DataResult<object>> UnReleaseTaskByIdsAsync(List<string> ids);

        /// <summary>
        /// 结案
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<DataResult<object>> EndCloseAsync(List<string> ids);

        /// <summary>
        /// 反结案
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<DataResult<object>> UnEndCloseAsync(List<string> ids);

        /// <summary>
        /// 修改保存排程任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<WOCraftScheduleModel>>> SaveTaskAsync(ScheduleModel model);

        /// <summary>
        /// (mes)兆科修改保存排程任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<WOCraftScheduleModel>>> SaveTaskZKAsync(ScheduleModel model);

        /// <summary>
        /// 是否引用了指定的工单Id
        /// </summary>
        /// <param name="woIds"></param>
        /// <returns></returns>
        Task<DataResult<List<string>>> IsReferenceWorkOrderAsync(List<string> woIds);


        /// <summary>
        /// 修改排程任务工位
        /// </summary>
        /// <returns></returns>
        Task<DataResult<WOCraftScheduleModel>> ChangeStationAsync(ChangeStationModel model);


        /// <summary>
        /// 查询条件获取排程ids列表
        /// </summary>

        Task<DataResult<List<string>>> GetScheduleIDSAsync(QueryRequestModel model);

        /// <summary>
        /// APP查询根据排程任务编号查询 查询进度
        /// </summary>

        Task<DataResult<ScheduleTaskProgressModel>> GetScheduleTaskProgressAsync(string scheduleNo);

        /// <summary>
        /// 查询排程任务明细
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<WOCraftScheduleModel>>> QueryScheduleTaskDetailAsync(QueryRequestModel model);


        /// <summary>
        /// 根据工位查询 未结案的排程任务 和  未完工的加工任务 工位排单列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<StationWorkList>>> QueryScheduleTaskByStationAsync(string id);

        /// <summary>
        /// 根据排程ID转委外申请单
        /// </summary>
        /// <param name="woIds"></param>
        /// <returns></returns>
        Task<DataResult<List<WOCraftScheduleModel>>> GetScheduleByIdsAsync(List<string> Ids);

        /// <summary>
        /// 委外状态是
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<DataResult<object>> OutAsync(List<string> ids);

        /// <summary>
        /// 委外状态否
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<DataResult<object>> UnOutAsync(List<string> ids);



        /// <summary>
        /// 根据工位ID 获取是否有最新的排程信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<DataResult<bool>> JudgeNewScheduleByStationIdAsync(string stationId);

        /// <summary>
        /// 领料获取排程
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<DataResult<List<WORecCraftScheduleModel>>> QueryScheduleTaskByRecAsync(QueryRequestModel model);


        /// <summary>
        /// 工位机通过排程id保存排程卡颜色（佳乐）
        /// </summary>
        /// <param name="WOCraftScheduleModel"></param>
        /// <returns></returns>
        Task<DataResult<bool>> SaveFcraftScheduleColorAsync(WOCraftScheduleModel model);
    }
}
