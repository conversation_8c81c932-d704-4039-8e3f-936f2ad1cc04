using HCloud.Core.Common;
using HCloud.Core.HCPlatform.Auth.Internal;
using HCloud.Core.HCPlatform.Ioc;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.ProxyGenerator.Models;
using HCloud.Core.Sugar;
using HCloud.Core.HCPlatform.Multilingual;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HCloud.ERP.IModuleServices.MES003_WorkOrder;
using HCloud.ERP.IModuleServices.MES003_WorkOrder.Models;
using HCloud.ERP.IModuleServices.MES004_WorkFetch;
using HCloud.ERP.IModuleServices.MES004_WorkFetch.Models;
using HCloud.ERP.IModuleServices.MSD002_Material.Models;
using HCloud.ERP.IModuleServices.MSD002_Material;
using HCloud.ERP.IModuleServices.MSD003_MaterialCate;
using HCloud.ERP.IModuleServices.MSD003_MaterialCate.Models;
using HCloud.ERP.IModuleServices.MSD004_MaterialBOM;
using HCloud.ERP.IModuleServices.MSD004_MaterialBOM.Models;
using HCloud.ERP.IModuleServices.MSD007_Project;
using HCloud.ERP.IModuleServices.MSD007_Project.Models;
using HCloud.Core.HCPlatform.CommonData;
using System.Collections.Concurrent;
using HCloud.ERP.IModuleServices.MES002_Craft;
using HCloud.ERP.IModuleServices.MES002_Craft.Models;
using HCloud.ERP.IModuleServices.ADM024_Employee;
using HCloud.ERP.IModuleServices.ADM024_Employee.Models;
using HCloud.ERP.IModuleServices.MES001WorkArea.Models;
using HCloud.ERP.IModuleServices.MES001WorkArea;
using HCloud.ERP.IModuleServices.MSD001_Unit;
using HCloud.ERP.IModuleServices.COP001_SaleOrder;
using HCloud.ERP.IModuleServices.COP001_SaleOrder.Models;
using HCloud.Core.HCPlatform.Utilities;
using HCloud.ERP.IModuleServices.EOS008_ReportManager.Models;
using HCloud.ERP.IModuleServices.EOS009_CustomProgram;
using HCloud.ERP.IModuleServices.EOS009_CustomProgram.Models;
using Newtonsoft.Json;
using HCloud.ERP.IModuleServices.SYS000_Common.Models.MRPBill;
using HCloud.ERP.IModuleServices.MRP001_Require.Models.Request;
using HCloud.ERP.IModuleServices.SYS000_Common.Services;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models;
using HCloud.ERP.IModuleServices.MES010_WorkOrderChange.Models;
using System;
using HCloud.ERP.IModuleServices.MSD001_Unit.Models;
using HCloud.ERP.IModuleServices.MES003_WorkOrder.Models.Request;
using HCloud.ERP.IModuleServices.PUR011_OutOrder;
using HCloud.Core.HCPlatform.ThirdParty;
using System.Net.Http;
using HCloud.ERP.IModuleServices.MES003_WorkOrder.Models.DataModel;
using HCloud.ERP.IModuleServices.MSD002_Material.Models.Request;
using HCloud.ERP.IModuleServices.QCS018_XFMatCheck.Models;
using HCloud.ERP.IModuleServices.QCS018_XFMatCheck;

namespace HCloud.ERP.ModuleServices.MES003_WorkOrder
{
    [ModuleName("MES003WorkOrder")]
    public partial class MES003WorkOrderService : ProxyServiceBase, IMES003WorkOrderService
    {
        private readonly ILogger _Logger = null;
        private readonly IAuth _iauth = null;
        private readonly ISugar<MES003WorkOrderService> _isugar = null;
        private readonly IMultilingualResource _multiLang = null;
        private readonly ICommonDataProvider _commonDataProvider = null;
        private readonly IBusinessDataService _businessService = null;
        //private readonly IHttpClientFactory _httpClient = null;
        //注入获取参数
        private readonly IThirdPartySetting _ThirdPartySetting = null;
        /// <summary>
        /// 订单工单
        /// </summary>
        private readonly string _SaleWO = "SaleWO";
        /// <summary>
        /// 备库工单
        /// </summary>
        private readonly string _StkWO = "StkWO";
        /// <summary>
        /// 需求分析转工单
        /// </summary>
        private readonly string _RequireWO = "RequireWO";

        /// <summary>
        /// 物料分类_耗材取值
        /// </summary>
        private const string _MSD_MatCateConsumables = "MSD_MatCateConsumables";
        /// <summary>
        /// 公司标志
        /// </summary>
        private const string _Company = "Company";

        public MES003WorkOrderService(ILogger<MES003WorkOrderService> logger,
             ISugar<MES003WorkOrderService> sugar, IAuth auth,
             IMultilingualResource multiLang, ICommonDataProvider commonDataProvider, IBusinessDataService businessService, IThirdPartySetting ThirdPartySetting/*, IHttpClientFactory httpClient*/)
        {
            _Logger = logger;
            _iauth = auth;
            _isugar = sugar;
            _multiLang = multiLang;
            _commonDataProvider = commonDataProvider;
            _businessService = businessService;
            _ThirdPartySetting = ThirdPartySetting;
            //_httpClient = httpClient;
        }
        /// <summary>
        /// 获取生产工单表数据--远程
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<SimpleWorkOrderModel>>> GetWorkOrdersAsync(List<string> ids)
        {

            var db = _isugar.DB;

            var daomodel = await db.Queryable<T_MESD_WORK_ORDER>().Where(p => ids.Contains(p.FWORK_ORDER_ID)).Select<SimpleWorkOrderModel>().ToListAsync();
            DataResult<List<SimpleWorkOrderModel>> result = new DataResult<List<SimpleWorkOrderModel>>()
            {
                StatusCode = 200,
                Entity = daomodel,

            };
            return await OK(result);
        }

        /// <summary>
        /// 获取生产工单的远控执行器  根据订单物料id--特殊
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<ControlActuatorModel>>> GetRemoteControlActuatorAsync(List<string> ids)
        {

            var db = _isugar.DB;

            var daomodel = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_MATERIAL>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID)).Where((a, b) => ids.Contains(a.FSALE_ORDER_PRODUCT_ID)).Select((a, b) => new
            {
                a.FSALE_ORDER_PRODUCT_ID,
                b.FSUB_MATERIAL_ID,//子件物料ID
                b.FUNIT_QTY,//单位用量
            }).Distinct().ToListAsync();


            var matInfos = await GetService<IMSD002MaterialService>("MSD002Material").GetRemoteControlActByIdsAsync(daomodel.Select(p => p.FSUB_MATERIAL_ID).Distinct().ToList());


            var acts = daomodel.Where(p => matInfos.Entity.Select(p => p.FMATERIAL_ID).Contains(p.FSUB_MATERIAL_ID)).Select(p => new ControlActuatorModel
            {
                FSALE_ORDER_PRODUCT_ID = p.FSALE_ORDER_PRODUCT_ID,
                FMATERIAL_ID = p.FSUB_MATERIAL_ID,
                FUNIT_QTY = p.FUNIT_QTY

            }).ToList();
            acts.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var matData = matInfos.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                if (matData != null)
                {
                    item.FMATERIAL_CODE = matData.FMATERIAL_CODE;
                    item.FMATERIAL_NAME = matData.FMATERIAL_NAME;
                    item.FSPEC_DESC = matData.FSPEC_DESC;
                    item.FUNIT_ID = matData.FUNIT_ID;
                }
            });


            DataResult<List<ControlActuatorModel>> result = new DataResult<List<ControlActuatorModel>>()
            {
                StatusCode = 200,
                Entity = acts,

            };
            return await OK(result);
        }




        /// <summary>
        /// 获取所有的生产工单===已领用和总量
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<QTYModel>>> GetAllQTYAsync(List<string> ids)
        {

            var db = _isugar.DB;

            var datar = new List<QTYModel>();
            var qtyone = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => ids.Contains(p.FWORK_ORDER_MATERIAL_ID)).ToListAsync();

            var qtyuse = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL_STATUS>().Where(p => ids.Contains(p.FWORK_ORDER_MATERIAL_ID)).ToListAsync();

            foreach (var item in ids)
            {
                var mod = new QTYModel();
                mod.FWORK_ORDER_MATERIAL_ID = item;
                var qto = qtyone.FirstOrDefault(P => P.FWORK_ORDER_MATERIAL_ID == item);
                if (qto != null)
                {
                    mod.FUSE_QTY = qto.FUSE_QTY;
                }

                var qtu = qtyuse.FirstOrDefault(P => P.FWORK_ORDER_MATERIAL_ID == item);
                if (qtu != null)
                {
                    mod.FFETCH_QTY = qtu.FFETCH_QTY;
                }
                datar.Add(mod);
            }

            DataResult<List<QTYModel>> result = new DataResult<List<QTYModel>>()
            {
                StatusCode = 200,
                Entity = datar,

            };
            return await OK(result);
        }


        /// <summary>
        /// 获取所有的生产工单===工单投料使用
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<WorkFetchModel>> GetAllWorkFetchAsync(HCloud.ERP.IModuleServices.MES003_WorkOrder.Models.RequestModel model)
        {

            var db = _isugar.DB;
            var saleRpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
            var projectServer = this.GetService<IMSD007ProjectService>("MSD007Project");
            if (model.productCateModel != null)
            {
                var matCateServer = this.GetService<IMSD003MaterialCateService>("MSD003MaterialCate");
                var rpcdata = await matCateServer.QueryMaterialCateIdsAsync(model.productCateModel);
                if (rpcdata.StatusCode != 200)
                {
                    ERROR(rpcdata, 111111, "查询物料分类异常");
                }
                QueryWhereItemModel itemdd = new QueryWhereItemModel();
                itemdd.FieldName = "material.FMATERIAL_CATE_ID";
                itemdd.OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.In;
                itemdd.Value = string.Join(",", rpcdata.Entity);
                if (model.productModel.WhereGroup == null && model.productModel.WhereGroup.Items.Count == 0)
                    model.productModel.WhereGroup.GroupType = EnumGroupType.AND;
                model.productModel.WhereGroup.Items.Add(itemdd);
            }
            if (model.productModel.WhereGroup != null && model.productModel.WhereGroup.Items.Count > 0)
            {
                var materiidsrpc = this.GetService<IMSD002MaterialService>("MSD002Material");
                var rpcdata = await materiidsrpc.GetMaterialIDSAsync(model.productModel);
                if (rpcdata.StatusCode != 200)
                {
                    ERROR(rpcdata, 111111, "查询材料异常");
                }

                QueryWhereItemModel itemdd = new QueryWhereItemModel();
                itemdd.FieldName = "a.FMATERIAL_ID";
                itemdd.OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.In;
                itemdd.Value = string.Join(",", rpcdata.Entity);
                model.model.WhereGroup.Items.Add(itemdd);
            }
            if (model.projectModel != null)
            {
                var rpcdata = await projectServer.GetProjectIdsAsync(model.projectModel);
                if (rpcdata.StatusCode != 200)
                {
                    ERROR(rpcdata, 111111, "查询项目异常");
                }

                QueryWhereItemModel itemdd = new QueryWhereItemModel();
                itemdd.FieldName = "so.FPROJECT_ID";
                itemdd.OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.In;
                itemdd.Value = string.Join(",", rpcdata.Entity);
                if (model.saleOrderModel == null)
                {
                    model.saleOrderModel = new QueryRequestModel();
                    model.saleOrderModel.WhereGroup = new QueryWhereGroupModel();
                    model.saleOrderModel.WhereGroup.GroupType = EnumGroupType.AND;
                    model.saleOrderModel.WhereGroup.Items = new List<QueryWhereItemModel>();
                }

                model.saleOrderModel.WhereGroup.Items.Add(itemdd);
            }
            if (model.saleOrderModel != null)
            {
                var saleOrdServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
                var rpcdata = await saleOrdServer.QuerySaleOrderIdsAsync(model.saleOrderModel);
                if (rpcdata.StatusCode != 200)
                {
                    ERROR(rpcdata, 111111, "查询订单异常");
                }

                QueryWhereItemModel itemdd = new QueryWhereItemModel();
                itemdd.FieldName = "a.FSALE_ORDER_ID";
                itemdd.OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.In;
                itemdd.Value = string.Join(",", rpcdata.Entity);
                model.model.WhereGroup.Items.Add(itemdd);
            }
            var wheres = WhereCollections.FromQueryRequestModel(model.model);
            RefAsync<int> totalSize = new RefAsync<int>();

            //取出符合条件的id
            var queryable = db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>(
                (a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID)).Where(wheres);
            // 排除变更中(已建变更单未审核）的生产工单
            var lstExcludeId = await db.Queryable<T_MESD_WORK_ORDER_CHANGE, T_MESD_WORK_ORDER_CHANGE_STATUS>(
                (a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_CHANGE_ID == b.FWORK_ORDER_CHANGE_ID))
                .Where((a, b) => !b.FIF_CANCEL && (b.FCFLAG == 0 || b.FCFLAG == 2))
                .Select((a, b) => a.FWORK_ORDER_ID).ToListAsync();
            if (lstExcludeId != null && lstExcludeId.Count > 0)
                queryable = queryable.Where(a => !lstExcludeId.Contains(a.FWORK_ORDER_ID));
            var woIds = await queryable.Select<WorkOrderModel>().OrderBy(a => a.FCDATE, OrderByType.Desc)
                             .ToPageListAsync(model.model.PageIndex, model.model.PageSize, totalSize);
            var workidss = woIds.Select(p => p.FWORK_ORDER_ID).ToList();

            //获取销售订单信息
            var saleOrdIds = woIds.Select(p => p.FSALE_ORDER_ID).ToList();
            var saleUpdata = await saleRpcServer.GetSaleOrdInfoByIdAsync(saleOrdIds);
            if (saleUpdata.StatusCode != 200)
            {
                ERROR(saleUpdata, 111111, "查询销售订单异常");
            }

            var projectIds = saleUpdata.Entity.Where(p => !string.IsNullOrWhiteSpace(p.FPROJECT_ID)).Select(p => p.FPROJECT_ID).ToList();
            var projectData = await projectServer.GetProjectsByIdsAsync(projectIds);
            if (projectData.StatusCode != 200)
            {
                ERROR(projectData, 111111, "查询项目档案异常");
            }

            var ordermaterail = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => workidss.Contains(p.FWORK_ORDER_ID)).WhereIF(!string.IsNullOrEmpty(model.IsWoHomeMade), p => !p.FIF_REVERSE).OrderBy(p => p.FCDATE, OrderByType.Desc).ToListAsync();

            var unitIds = ordermaterail.SelectMany(p => { var ids = new List<string>() { p.FSUB_UNIT_ID, p.FSUB_STK_UNIT_ID }; return ids; }).ToList();
            var units = await _businessService.GetUnitNameByIdsAsync(unitIds);
            if (units.StatusCode != 200)
            {
                ERROR(units, 111111, $"获取计量单位失败");
            }

            var ordermaterailStatus = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL_STATUS>().Where(p => workidss.Contains(p.FWORK_ORDER_ID)).ToListAsync();

            var craftids = ordermaterail.Select(p => p.FWORK_ORDER_CRAFT_ID).ToList();
            var ordercraft = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>().Where(p => craftids.Contains(p.FWORK_ORDER_CRAFT_ID)).ToListAsync();

            var orderbumid = woIds.Select(p => p.FMATERIAL_ID).ToList();
            var materialIds = ordermaterail.Select(p => p.FSUB_MATERIAL_ID).ToList();
            materialIds.AddRange(orderbumid);


            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            var updata = await rpcServer.GetMaterialUpAsync(materialIds);
            if (updata.StatusCode != 200)
            {
                ERROR(updata, 111111, "查询材料单价异常");
            }

            DataResult<List<SimpleMaterialModel>> materialResult = await GetMaterialsAsync(materialIds);
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }
            //获取仓库 是否 货位管理
            var storeIds = materialResult.Entity.Select(p => p.FSTORE_ID).Distinct().ToList();
            var storeInfos = await _businessService.GetStoresAsync(storeIds);
            if (storeInfos.StatusCode != 200)
            {
                ERROR(storeInfos, storeInfos.StatusCode, storeInfos.Message);
            }


            var matemap = ordermaterail.MapToDestObj<T_MESD_WORK_ORDER_MATERIAL, WorkOrderMaterialModel>();
            matemap.AsParallel().WithDegreeOfParallelism(4).ForAll(p =>
            {
                var workInfo = woIds.FirstOrDefault(w => w.FWORK_ORDER_ID == p.FWORK_ORDER_ID);
                if (workInfo != null)
                {
                    p.FWORK_ORDER_NO = workInfo.FWORK_ORDER_NO;

                }

                var unit = units.Entity.FirstOrDefault(u => u.FUNIT_ID == p.FSUB_UNIT_ID);
                if (unit != null)
                {
                    p.FSUB_UNIT_CODE = unit.FUNIT_CODE;
                    p.FSUB_UNIT_NAME = unit.FUNIT_NAME;

                }
                var unitStk = units.Entity.FirstOrDefault(u => u.FUNIT_ID == p.FSUB_STK_UNIT_ID);
                if (unitStk != null)
                {
                    p.FSUB_STK_UNIT_CODE = unitStk.FUNIT_CODE;
                    p.FSUB_STK_UNIT_NAME = unitStk.FUNIT_NAME;
                }

                var materialup = updata.Entity.FirstOrDefault(m => m.FMATERIAL_ID == p.FSUB_MATERIAL_ID);
                if (materialup != null)
                {
                    p.FREFER_COST_UP = materialup.FREFER_COST_UP;
                    p.FMOVE_AVG_UP = materialup.FMOVE_AVG_UP;

                }

                var material = materialResult.Entity.FirstOrDefault(m => m.FMATERIAL_ID == p.FSUB_MATERIAL_ID);
                if (material != null)
                {
                    p.FSUB_MATERIAL_CODE = material.FMATERIAL_CODE;
                    p.FSUB_MATERIAL_NAME = material.FMATERIAL_NAME;
                    p.FSUB_SPEC_DESC = material.FSPEC_DESC;
                    p.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                    p.FSUB_GOODS_MODEL = material.FGOODS_MODEL;
                    p.FSTORE_ID = material.FSTORE_ID;
                    var storeInfo = storeInfos.Entity.FirstOrDefault(stores => material.FSTORE_ID == stores.FSTORE_ID);
                    if (storeInfo != null)
                    {
                        p.FSTORE_NAME = storeInfo.FSTORE_NAME;
                        p.FSTORE_CODE = storeInfo.FSTORE_CODE;
                    }
                    p.FSTORE_PLACE_ID = material.FSTORE_PLACE_ID;
                    if (!string.IsNullOrEmpty(p.FSTORE_ID))
                    {
                        var store = storeInfos.Entity.FirstOrDefault(store => store.FSTORE_ID == p.FSTORE_ID);
                        if (store != null)
                        {
                            p.FIF_ENABLE_PLACE = store.FIF_ENABLE_PLACE;
                        }
                    }

                    p.FPRO_FETCH_LOT_QTY = material.FPRO_FETCH_LOT_QTY;//生产领用最小批量
                }


                var mstatus = ordermaterailStatus.FirstOrDefault(s => s.FWORK_ORDER_MATERIAL_ID == p.FWORK_ORDER_MATERIAL_ID);
                if (mstatus != null)
                {
                    p.FFETCH_QTY = mstatus.FFETCH_QTY;
                }
                var craftdata = ordercraft.FirstOrDefault(c => c.FWORK_ORDER_CRAFT_ID == p.FWORK_ORDER_CRAFT_ID);
                if (craftdata != null)
                {
                    p.FSTATION_ID = craftdata.FSTATION_ID;
                }
            });

            var ordermap = woIds;
            ordermap.AsParallel().WithDegreeOfParallelism(4).ForAll(p =>
            {
                var material = materialResult.Entity.FirstOrDefault(m => m.FMATERIAL_ID == p.FMATERIAL_ID);
                if (material != null)
                {
                    p.FMATERIAL_CODE = material.FMATERIAL_CODE;
                    p.FMATERIAL_NAME = material.FMATERIAL_NAME;
                    p.FSPEC_DESC = material.FSPEC_DESC;
                    p.FGOODS_MODEL = material.FGOODS_MODEL;
                    p.FMATEIAL_CATE_NAME = material.FMATERIAL_CATE_NAME;
                }
                var saleOrd = saleUpdata.Entity.FirstOrDefault(s => s.FSALE_ORDER_ID == p.FSALE_ORDER_ID);
                if (saleOrd != null)
                {
                    p.FSALE_ORDER_NO = saleOrd.FSALE_ORDER_NO;
                    var projectInfo = projectData.Entity.FirstOrDefault(s => s.FPROJECT_ID == saleOrd.FPROJECT_ID);
                    if (projectInfo != null)
                    {
                        p.FPROJECT_ID = projectInfo.FPROJECT_ID;
                        p.FPROJECT_CODE = projectInfo.FPROJECT_CODE;
                        p.FPROJECT_NAME = projectInfo.FPROJECT_NAME;
                    }

                }

            });
            var resultrtn = new WorkFetchModel()
            {
                workOrderModels = ordermap,
                workOrderMaterialModels = matemap,
            };


            DataResult<WorkFetchModel> result = new DataResult<WorkFetchModel>()
            {
                StatusCode = 200,
                Entity = resultrtn,
                Pager = new PagerResult()
                {
                    PageNum = model.model.PageIndex,
                    PageSize = model.model.PageSize,
                    TotalRecords = totalSize.Value,
                },
            };
            return await OK(result);

        }


        /// <summary>
        /// 获取所有的生产工单主表===工单投料使用
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WorkOrderModel>>> GetAllWorkFetchMainAsync(HCloud.ERP.IModuleServices.MES003_WorkOrder.Models.RequestModel model)
        {
            var db = _isugar.DB;

            if (model.productModel.WhereGroup != null && model.productModel.WhereGroup.Items.Count > 0)
            {

                var materiidsrpc = this.GetService<IMSD002MaterialService>("MSD002Material");
                var rpcdata = await materiidsrpc.GetMaterialIDSAsync(model.productModel);
                if (rpcdata.StatusCode != 200)
                {
                    ERROR(rpcdata, 111111, "查询材料异常");
                }

                QueryWhereItemModel itemdd = new QueryWhereItemModel();
                itemdd.FieldName = "a.FMATERIAL_ID";
                itemdd.OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.In;
                itemdd.Value = string.Join(",", rpcdata.Entity);
                model.model.WhereGroup.Items.Add(itemdd);
            }

            var wheres = WhereCollections.FromQueryRequestModel(model.model);
            RefAsync<int> totalSize = new RefAsync<int>();

            //取出符合条件的id
            var queryable = db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>(
                (a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID)).Where(wheres);
            // 排除变更中(已建变更单未审核）的生产工单
            var lstExcludeId = await db.Queryable<T_MESD_WORK_ORDER_CHANGE, T_MESD_WORK_ORDER_CHANGE_STATUS>(
                (a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_CHANGE_ID == b.FWORK_ORDER_CHANGE_ID))
                .Where((a, b) => !b.FIF_CANCEL && (b.FCFLAG == 0 || b.FCFLAG == 2))
                .Select((a, b) => a.FWORK_ORDER_ID).ToListAsync();
            if (lstExcludeId != null && lstExcludeId.Count > 0)
                queryable = queryable.Where(a => !lstExcludeId.Contains(a.FWORK_ORDER_ID));
            var woIds = await queryable.Select(a => a).OrderBy(a => a.FCDATE, OrderByType.Desc)
                             .ToPageListAsync(model.model.PageIndex, model.model.PageSize, totalSize);
            var workidss = woIds.Select(p => p.FWORK_ORDER_ID).ToList();

            //获取销售订单信息
            var saleOrdIds = woIds.Select(p => p.FSALE_ORDER_ID).ToList();
            var saleRpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
            var saleUpdata = await saleRpcServer.GetSaleOrdInfoByIdAsync(saleOrdIds);
            if (saleUpdata.StatusCode != 200)
            {
                ERROR(saleUpdata, 111111, "查询销售订单异常");
            }



            var ordermaterail = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => workidss.Contains(p.FWORK_ORDER_ID)).WhereIF(!string.IsNullOrEmpty(model.IsWoHomeMade), p => !p.FIF_REVERSE).OrderBy(p => p.FCDATE, OrderByType.Desc).ToListAsync();

            var unitIds = ordermaterail.SelectMany(p => { var ids = new List<string>() { p.FSUB_UNIT_ID, p.FSUB_STK_UNIT_ID }; return ids; }).ToList();
            var units = await _businessService.GetUnitNameByIdsAsync(unitIds);
            if (units.StatusCode != 200)
            {
                ERROR(units, 111111, $"获取计量单位失败");
            }

            var ordermaterailStatus = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL_STATUS>().Where(p => workidss.Contains(p.FWORK_ORDER_ID)).ToListAsync();

            var craftids = ordermaterail.Select(p => p.FWORK_ORDER_CRAFT_ID).ToList();
            var ordercraft = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>().Where(p => craftids.Contains(p.FWORK_ORDER_CRAFT_ID)).ToListAsync();

            var orderbumid = woIds.Select(p => p.FMATERIAL_ID).ToList();
            var materialIds = ordermaterail.Select(p => p.FSUB_MATERIAL_ID).ToList();
            materialIds.AddRange(orderbumid);


            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            var updata = await rpcServer.GetMaterialUpAsync(materialIds);
            if (updata.StatusCode != 200)
            {
                ERROR(updata, 111111, "查询材料单价异常");
            }

            DataResult<List<SimpleMaterialModel>> materialResult = await GetMaterialsAsync(materialIds);
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }

            var matemap = ordermaterail.MapToDestObj<T_MESD_WORK_ORDER_MATERIAL, WorkOrderMaterialModel>();
            matemap.AsParallel().WithDegreeOfParallelism(4).ForAll(p =>
            {
                var workInfo = woIds.FirstOrDefault(w => w.FWORK_ORDER_ID == p.FWORK_ORDER_ID);
                if (workInfo != null)
                {
                    p.FWORK_ORDER_NO = workInfo.FWORK_ORDER_NO;

                }

                var unit = units.Entity.FirstOrDefault(u => u.FUNIT_ID == p.FSUB_UNIT_ID);
                if (unit != null)
                {
                    p.FSUB_UNIT_CODE = unit.FUNIT_CODE;
                    p.FSUB_UNIT_NAME = unit.FUNIT_NAME;

                }
                var unitStk = units.Entity.FirstOrDefault(u => u.FUNIT_ID == p.FSUB_STK_UNIT_ID);
                if (unitStk != null)
                {
                    p.FSUB_STK_UNIT_CODE = unitStk.FUNIT_CODE;
                    p.FSUB_STK_UNIT_NAME = unitStk.FUNIT_NAME;
                }

                var materialup = updata.Entity.FirstOrDefault(m => m.FMATERIAL_ID == p.FSUB_MATERIAL_ID);
                if (materialup != null)
                {
                    p.FREFER_COST_UP = materialup.FREFER_COST_UP;
                    p.FMOVE_AVG_UP = materialup.FMOVE_AVG_UP;
                }

                var material = materialResult.Entity.FirstOrDefault(m => m.FMATERIAL_ID == p.FSUB_MATERIAL_ID);
                if (material != null)
                {
                    p.FSUB_MATERIAL_CODE = material.FMATERIAL_CODE;
                    p.FSUB_MATERIAL_NAME = material.FMATERIAL_NAME;
                    p.FSUB_SPEC_DESC = material.FSPEC_DESC;
                    p.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                    p.FSUB_GOODS_MODEL = material.FGOODS_MODEL;
                }

                var mstatus = ordermaterailStatus.FirstOrDefault(s => s.FWORK_ORDER_MATERIAL_ID == p.FWORK_ORDER_MATERIAL_ID);
                if (mstatus != null)
                {
                    p.FFETCH_QTY = mstatus.FFETCH_QTY;
                }
                var craftdata = ordercraft.FirstOrDefault(c => c.FWORK_ORDER_CRAFT_ID == p.FWORK_ORDER_CRAFT_ID);
                if (craftdata != null)
                {
                    p.FSTATION_ID = craftdata.FSTATION_ID;
                }
            });

            var ordermap = woIds.MapToDestObj<T_MESD_WORK_ORDER, WorkOrderModel>();
            ordermap.AsParallel().WithDegreeOfParallelism(4).ForAll(p =>
            {
                var material = materialResult.Entity.FirstOrDefault(m => m.FMATERIAL_ID == p.FMATERIAL_ID);
                if (material != null)
                {
                    p.FMATERIAL_CODE = material.FMATERIAL_CODE;
                    p.FMATERIAL_NAME = material.FMATERIAL_NAME;

                }
                var saleOrd = saleUpdata.Entity.FirstOrDefault(s => s.FSALE_ORDER_ID == p.FSALE_ORDER_ID);
                if (saleOrd != null)
                {
                    p.FSALE_ORDER_NO = saleOrd.FSALE_ORDER_NO;
                }

            });

            DataResult<List<WorkOrderModel>> result = new DataResult<List<WorkOrderModel>>()
            {
                StatusCode = 200,
                Entity = ordermap,
                Pager = new PagerResult()
                {
                    PageNum = model.model.PageIndex,
                    PageSize = model.model.PageSize,
                    TotalRecords = totalSize.Value,
                },
            };
            return await OK(result);

        }
        /// <summary>
        /// 根据id获取生产工单明细===工单投料使用
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WorkOrderMaterialModel>>> GetWorkFetchDetailAsync(RequestIdModel model)
        {
            var db = _isugar.DB;

            var workord = await db.Queryable<T_MESD_WORK_ORDER>().FirstAsync(p => p.FWORK_ORDER_ID == model.id);
            if (workord == null)
            {
                ERROR(workord, 111111, $"获取生产工单失败");
            }

            var ordermaterail = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => model.id == p.FWORK_ORDER_ID).WhereIF(!string.IsNullOrEmpty(model.IsWoHomeMade), p => !p.FIF_REVERSE).OrderBy(p => p.FCDATE, OrderByType.Desc).ToListAsync();

            var unitIds = ordermaterail.SelectMany(p => { var ids = new List<string>() { p.FSUB_UNIT_ID, p.FSUB_STK_UNIT_ID }; return ids; }).ToList();
            var units = await _businessService.GetUnitNameByIdsAsync(unitIds);
            if (units.StatusCode != 200)
            {
                ERROR(units, 111111, $"获取计量单位失败");
            }

            var ordermaterailStatus = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL_STATUS>().Where(p => model.id == p.FWORK_ORDER_ID).ToListAsync();

            var craftids = ordermaterail.Select(p => p.FWORK_ORDER_CRAFT_ID).ToList();
            var ordercraft = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>().Where(p => craftids.Contains(p.FWORK_ORDER_CRAFT_ID)).ToListAsync();


            var materialIds = ordermaterail.Select(p => p.FSUB_MATERIAL_ID).ToList();



            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            var updata = await rpcServer.GetMaterialUpAsync(materialIds);
            if (updata.StatusCode != 200)
            {
                ERROR(updata, 111111, "查询材料单价异常");
            }

            DataResult<List<SimpleMaterialModel>> materialResult = await GetMaterialsAsync(materialIds);
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }

            var matemap = ordermaterail.MapToDestObj<T_MESD_WORK_ORDER_MATERIAL, WorkOrderMaterialModel>();
            matemap.AsParallel().WithDegreeOfParallelism(4).ForAll(p =>
            {
                p.FWORK_ORDER_NO = workord.FWORK_ORDER_NO;
                p.FWORK_ORDER_ID = workord.FWORK_ORDER_ID;

                var unit = units.Entity.FirstOrDefault(u => u.FUNIT_ID == p.FSUB_UNIT_ID);
                if (unit != null)
                {
                    p.FSUB_UNIT_CODE = unit.FUNIT_CODE;
                    p.FSUB_UNIT_NAME = unit.FUNIT_NAME;

                }
                var unitStk = units.Entity.FirstOrDefault(u => u.FUNIT_ID == p.FSUB_STK_UNIT_ID);
                if (unitStk != null)
                {
                    p.FSUB_STK_UNIT_CODE = unitStk.FUNIT_CODE;
                    p.FSUB_STK_UNIT_NAME = unitStk.FUNIT_NAME;
                }

                var materialup = updata.Entity.FirstOrDefault(m => m.FMATERIAL_ID == p.FSUB_MATERIAL_ID);
                if (materialup != null)
                {
                    p.FREFER_COST_UP = materialup.FREFER_COST_UP;
                    p.FMOVE_AVG_UP = materialup.FMOVE_AVG_UP;
                }

                var material = materialResult.Entity.FirstOrDefault(m => m.FMATERIAL_ID == p.FSUB_MATERIAL_ID);
                if (material != null)
                {
                    p.FSUB_MATERIAL_CODE = material.FMATERIAL_CODE;
                    p.FSUB_MATERIAL_NAME = material.FMATERIAL_NAME;
                    p.FSUB_SPEC_DESC = material.FSPEC_DESC;
                    p.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                    p.FSUB_GOODS_MODEL = material.FGOODS_MODEL;
                }

                var mstatus = ordermaterailStatus.FirstOrDefault(s => s.FWORK_ORDER_MATERIAL_ID == p.FWORK_ORDER_MATERIAL_ID);
                if (mstatus != null)
                {
                    p.FFETCH_QTY = mstatus.FFETCH_QTY;
                }
                var craftdata = ordercraft.FirstOrDefault(c => c.FWORK_ORDER_CRAFT_ID == p.FWORK_ORDER_CRAFT_ID);
                if (craftdata != null)
                {
                    p.FSTATION_ID = craftdata.FSTATION_ID;
                }
            });



            DataResult<List<WorkOrderMaterialModel>> result = new DataResult<List<WorkOrderMaterialModel>>()
            {
                StatusCode = 200,
                Entity = matemap,

            };
            return await OK(result);

        }
        /// <summary>
        /// 转委外单使用
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>

        public async Task<DataResult<List<WorkOrderModel>>> GetWorkOrdersByIdAsync(List<string> Ids)
        {

            var result = new DataResult<List<WorkOrderModel>>()
            {
                Entity = null,
                StatusCode = 200,
            };

            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            //稍后加验证

            //取出符合条件的id
            var workOrders = await db.Queryable<T_MESD_WORK_ORDER>().Where(p => Ids.Contains(p.FWORK_ORDER_ID)).Select<WorkOrderModel>().ToListAsync();

            var workOrderStatus = await db.Queryable<T_MESD_WORK_ORDER_STATUS>().Where(p => Ids.Contains(p.FWORK_ORDER_ID)).ToListAsync();

            //判断状态
            var outWord = workOrders.FirstOrDefault(p => !p.FIF_OUT);
            if (outWord != null)
            {
                ERROR(outWord, 111111, $"工单 {outWord.FWORK_ORDER_NO} 非委外");
            }
            var canWord = workOrderStatus.FirstOrDefault(p => p.FIF_CANCEL);
            if (canWord != null)
            {
                var workInfo = workOrders.FirstOrDefault(p => p.FWORK_ORDER_ID == canWord.FWORK_ORDER_ID);
                ERROR(workInfo, 111111, $"工单 {workInfo.FWORK_ORDER_NO} 已作废");
            }
            ;
            var closeWord = workOrderStatus.FirstOrDefault(p => p.FIF_CLOSE == 1 || p.FIF_CLOSE == 3);
            if (closeWord != null)
            {
                var workInfo = workOrders.FirstOrDefault(p => p.FWORK_ORDER_ID == closeWord.FWORK_ORDER_ID);
                ERROR(workInfo, 111111, $"工单 {workInfo.FWORK_ORDER_NO} 已结案");
            }
            ;

            var cfWord = workOrderStatus.FirstOrDefault(p => p.FCFLAG != 1);
            if (cfWord != null)
            {
                var workInfo = workOrders.FirstOrDefault(p => p.FWORK_ORDER_ID == cfWord.FWORK_ORDER_ID);
                ERROR(workInfo, 111111, $"工单 {workInfo.FWORK_ORDER_NO} 未审核");
            }
            ;
            //判断数量是否可转
            var outOrds = await (GetService<IPUR011OutOrderService>("PUR011OutOrder")).GetByIdsAsync(Ids);

            foreach (var item in outOrds.Entity)
            {
                var outCount = workOrders.FirstOrDefault(p => p.FWORK_ORDER_ID == item.WoMatId);
                if (outCount != null)
                {
                    if (outCount.FPRO_QTY <= item.count)
                    {
                        ERROR(outCount, 111111, $"工单 {outCount.FWORK_ORDER_NO} 数量 {outCount.FPRO_QTY} 已全部转委外单");
                    }
                    outCount.FPRO_QTY = outCount.FPRO_QTY - item.count;//改变可以转的数量
                }
            }


            List<string> materialIds = workOrders.Select(p => p.FMATERIAL_ID).Distinct().ToList();
            //取出物料信息
            DataResult<List<SimpleMaterialModel>> materialResult = await GetMaterialsAsync(materialIds);
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }
            var proids = workOrders.Select(p => p.FPRO_UNIT_ID).ToList();
            var unitids = workOrders.Select(p => p.FUNIT_ID).ToList();
            proids.AddRange(unitids);
            var unit = await _businessService.GetUnitNameByIdsAsync(proids.Distinct().ToList());

            workOrders.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
            {
                //物料
                var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == workOrder.FMATERIAL_ID);
                if (material != null)
                {
                    workOrder.FMATERIAL_CODE = material.FMATERIAL_CODE;
                    workOrder.FMATERIAL_NAME = material.FMATERIAL_NAME;
                    workOrder.FSPEC_DESC = material.FSPEC_DESC;
                    workOrder.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                    workOrder.FGOODS_MODEL = material.FGOODS_MODEL;

                    //workOrder.FMATEIAL_CATE_NAME = String.Concat(!string.IsNullOrWhiteSpace(material.FMATERIAL_CATE_PARENT_NAME) ?
                    //                        material.FMATERIAL_CATE_PARENT_NAME + "/" : string.Empty, material.FMATERIAL_CATE_NAME);

                    //workOrder.FIF_3C = material.FIF_3C;

                }
                var prounit = unit.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrder.FPRO_UNIT_ID);
                if (prounit != null)
                {
                    workOrder.FPRO_UNIT_NAME = prounit.FUNIT_NAME;

                }
                var unitinfo = unit.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrder.FUNIT_ID);
                if (unitinfo != null)
                {
                    workOrder.FUNIT_NAME = unitinfo.FUNIT_NAME;

                }

            });

            result.Entity = workOrders;


            return await OK(result);
        }



        /// <summary>
        /// 委外单 调用  获取工单信息
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>

        public async Task<DataResult<List<WorkOrderModel>>> GetWorkOrderInfosByIdAsync(List<string> Ids)
        {

            var result = new DataResult<List<WorkOrderModel>>()
            {
                Entity = null,
                StatusCode = 200,
            };

            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            //取出符合条件的id
            var workOrders = await db.Queryable<T_MESD_WORK_ORDER>().Where(p => Ids.Contains(p.FWORK_ORDER_ID)).Select<WorkOrderModel>().ToListAsync();

            //非委外工单不能转
            var notOut = workOrders.FirstOrDefault(p => !p.FIF_OUT);
            if (notOut != null)
            {
                ERROR(notOut, 111111, $"工单 {notOut.FWORK_ORDER_NO} 非委外");
            }

            result.Entity = workOrders;


            return await OK(result);
        }

        /// <summary>
        /// 获取所有委外的生产工单
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WorkOrderModel>>> GetOutAllAsync(WorkQueryRequstModel model)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var matIds = new List<string>();

            if (model.MaterialQRM != null)
            {
                //查询物料ids
                var matrpcServer = this.GetService<IModuleServices.MSD002_Material.IMSD002MaterialService>("MSD002Material");
                var matids = await matrpcServer.GetMaterialIDSAsync(model.MaterialQRM);
                if (matids.StatusCode != 200)
                {
                    ERROR(matids, 111111, "根据条件查询物料ID失败");
                }
                matIds = matids.Entity;
            }

            var wheres = WhereCollections.FromQueryRequestModel(model);
            RefAsync<int> totalSize = new RefAsync<int>();
            List<WorkOrderModel> workOrders = null;

            var outOrdserver = GetService<IPUR011OutOrderService>("PUR011OutOrder");

            var isDisplayIds = new List<String>();
            var outInfos = new DataResult<List<WorkOrdInfo>>();

            if (!model.IS_DISPLAY)
            {
                //取出符合条件的id
                var woAlls = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID))
                                  .OrderBy((a, b) => a.FCDATE, OrderByType.Desc)   //按建立日期倒序
                                 .WhereIF(matIds.Count > 0, (a, b) => matIds.Contains(a.FMATERIAL_ID))
                                 .Select((a, b) => new WorkOrderModel
                                 {
                                     FWORK_ORDER_ID = a.FWORK_ORDER_ID,
                                     FPRO_QTY = a.FPRO_QTY
                                 })
                                 .Where(wheres)
                                 .ToListAsync();

                var woAllIds = woAlls.Select(p => p.FWORK_ORDER_ID).ToList();

                outInfos = await outOrdserver.GetByIdsAsync(woAllIds);

                woAlls.AsParallel().WithDegreeOfParallelism(4).ForAll(async item =>
                {
                    var outNum = outInfos.Entity.FirstOrDefault(p => p.WoMatId == item.FWORK_ORDER_ID);
                    item.OutOrdCount = outNum.count;
                });

                isDisplayIds = woAlls.Where(p => p.FPRO_QTY > p.OutOrdCount).Select(p => p.FWORK_ORDER_ID).ToList();
            }


            //取出符合条件的id
            var woIds = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID))
                              .OrderBy((a, b) => a.FCDATE, OrderByType.Desc)   //按建立日期倒序
                             .WhereIF(matIds.Count > 0, (a, b) => matIds.Contains(a.FMATERIAL_ID))
                             .WhereIF(isDisplayIds.Count > 0, (a, b) => isDisplayIds.Contains(a.FWORK_ORDER_ID))
                             .Select((a, b) => new { a.FWORK_ORDER_ID, a.FFULL_WORK_ORDER_ID })
                             .Where(wheres)
                             .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            if (woIds.Count > 0)
            {
                //取出子工单id
                var subWOIds = await GetFullSubWOIdsAsync(woIds.Select(p => p.FFULL_WORK_ORDER_ID).ToList());

                var ids = woIds.Select(p => p.FWORK_ORDER_ID).ToList().Union(subWOIds).ToList();

                //取出父工单id
                ConcurrentBag<string> pathIds = new ConcurrentBag<string>();
                woIds.Select(p => p.FFULL_WORK_ORDER_ID).AsParallel().WithDegreeOfParallelism(4).ForAll(fullId =>
                {
                    if (fullId.Contains("/"))
                    {
                        var fullIdArr = fullId.Split('/');
                        for (int i = 0; i < fullIdArr.Length - 1; i++)
                        {
                            pathIds.Add(fullIdArr[i]);
                        }
                    }
                });

                //得出合并后的工单Id
                ids = ids.Union(pathIds.ToList().Distinct()).ToList();

                //根据ids取工单数据
                workOrders = (await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID))
                    .Select<WorkOrderModel>()
                    .Where((a) => ids.Contains(a.FWORK_ORDER_ID))
                    .ToListAsync()).OrderByDescending(p => p.FWORK_ORDER_NO).ToList();

                if (workOrders.Count > 0)
                {
                    List<string> materialIds = workOrders.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                    //取出物料信息
                    DataResult<List<SimpleMaterialModel>> materialResult = await GetMaterialsAsync(materialIds);
                    if (materialResult.StatusCode != 200)
                    {
                        ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                    }

                    //取计划员姓名
                    DataResult<List<EmployeeModel>> empResults = null;
                    var empIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                    if (empIds.Count > 0)
                    {
                        empResults = await GetEmployeesAsync(empIds);
                        if (empResults.StatusCode != 200)
                        {
                            ERROR(empResults, empResults.StatusCode, empResults.Message);
                        }
                    }

                    //取部门
                    var deptIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FDEPT_ID)).Select(p => p.FDEPT_ID).Distinct().ToList();
                    DataResult<List<MakerModel>> deptResults = null;
                    if (deptIds.Count > 0)
                    {
                        deptResults = await _businessService.GetDeptByIdsAsync(deptIds);
                        if (deptResults.StatusCode != 200)
                        {
                            ERROR(deptResults, deptResults.StatusCode, deptResults.Message);
                        }
                    }

                    //取销售订单(有订单子表id值)
                    DataResult<List<SimpleSaleOrderMaterialModel>> soMaterialsResult = null;
                    var soMaterialIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_PRODUCT_ID))
                                        .Select(p => p.FSALE_ORDER_PRODUCT_ID)
                                        .Distinct()
                                        .ToList();
                    if (soMaterialIds.Count > 0)
                    {
                        soMaterialsResult = await GetSaleOrderMaterialsAsync(soMaterialIds);
                        if (soMaterialsResult.StatusCode != 200)
                        {
                            ERROR(soMaterialsResult, soMaterialsResult.StatusCode, soMaterialsResult.Message);
                        }
                    }

                    //取销售订单(排除订单子表id为空)，子工单: 有订单主表id，无订单子表id
                    DataResult<List<SaleOrderSimpleModel>> saleOrderResult = null;
                    var saleOrderIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID) &&
                                        string.IsNullOrWhiteSpace(p.FSALE_ORDER_PRODUCT_ID)).Select(p => p.FSALE_ORDER_ID).Distinct().ToList();
                    if (saleOrderIds.Count > 0)
                    {
                        saleOrderResult = await GetSaleOrdersAsync(saleOrderIds);
                        if (saleOrderResult.StatusCode != 200)
                        {
                            ERROR(saleOrderResult, saleOrderResult.StatusCode, saleOrderResult.Message);
                        }
                    }


                    //取需求单号  
                    DataResult<List<MrpRequireModel>> requireResult = null;
                    var sourceIds = workOrders.Where(p => p.FWORK_ORDER_TYPE == _RequireWO).Select(p => p.FSOURCE_ID).Distinct().ToList();
                    if (sourceIds.Count > 0)
                    {
                        requireResult = await GetRequiresAsync(sourceIds);
                        if (requireResult.StatusCode != 200)
                        {
                            ERROR(requireResult, requireResult.StatusCode, requireResult.Message);
                        }

                    }

                    workOrders.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                    {
                        //物料
                        var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == workOrder.FMATERIAL_ID);
                        if (material != null)
                        {
                            workOrder.FMATERIAL_CODE = material.FMATERIAL_CODE;
                            workOrder.FMATERIAL_NAME = material.FMATERIAL_NAME;
                            workOrder.FSPEC_DESC = material.FSPEC_DESC;
                            workOrder.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                            workOrder.FGOODS_MODEL = material.FGOODS_MODEL;

                            workOrder.FMATEIAL_CATE_NAME = String.Concat(!string.IsNullOrWhiteSpace(material.FMATERIAL_CATE_PARENT_NAME) ?
                                                    material.FMATERIAL_CATE_PARENT_NAME + "/" : string.Empty, material.FMATERIAL_CATE_NAME);

                            workOrder.FIF_3C = material.FIF_3C;


                        }

                        //计划员
                        if (!string.IsNullOrWhiteSpace(workOrder.FPLAN_EMP_ID) && empResults != null)
                        {
                            var emp = empResults.Entity.FirstOrDefault(p => p.FEMP_ID == workOrder.FPLAN_EMP_ID);
                            if (emp != null)
                            {
                                workOrder.FPLAN_EMP_NAME = emp.FEMP_NAME;
                                workOrder.FPLAN_EMP_CODE = emp.FEMP_CODE;
                            }
                        }

                        //部门
                        if (!string.IsNullOrWhiteSpace(workOrder.FDEPT_ID) && deptResults != null)
                        {
                            var dept = deptResults.Entity.FirstOrDefault(p => p.FMAKER_ID == workOrder.FDEPT_ID);
                            if (dept != null)
                            {
                                workOrder.FDEPT_NAME = dept.FMAKER_NAME;
                            }
                        }

                        //订单
                        if (!string.IsNullOrWhiteSpace(workOrder.FSALE_ORDER_PRODUCT_ID))
                        {
                            if (soMaterialsResult != null)
                            {
                                var so = soMaterialsResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_MATERIAL_ID == workOrder.FSALE_ORDER_PRODUCT_ID);
                                if (so != null)
                                {
                                    workOrder.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                                    workOrder.FSOURCE_NO = so.FSALE_ORDER_NO;

                                    workOrder.FCUSTOM_PARM = so.FCUSTOM_PARM;   //定制参数

                                    workOrder.FPROJECT_ID = so.FPROJECT_ID;  //项目
                                    workOrder.FPROJECT_NAME = so.FPROJECT_NAME;
                                    workOrder.FPROJECT_CODE = so.FPROJECT_CODE;
                                }
                            }
                        }

                        else
                        {
                            if (!string.IsNullOrWhiteSpace(workOrder.FSALE_ORDER_ID) && saleOrderResult != null)
                            {
                                var so = saleOrderResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == workOrder.FSALE_ORDER_ID);
                                if (so != null)
                                {
                                    workOrder.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                                    workOrder.FSOURCE_NO = so.FSALE_ORDER_NO;

                                    workOrder.FPROJECT_ID = so.FPROJECT_ID;  //项目
                                    workOrder.FPROJECT_NAME = so.FPROJECT_NAME;
                                    workOrder.FPROJECT_CODE = so.FPROJECT_CODE;
                                }
                            }
                        }



                        //需求单编号
                        if (requireResult != null)
                        {
                            if (workOrder.FWORK_ORDER_TYPE == _RequireWO)
                            {
                                var require = requireResult.Entity.FirstOrDefault(p => p.FMRP_REQUIRE_ID == workOrder.FSOURCE_ID);
                                if (require != null)
                                {
                                    workOrder.FSOURCE_NO = require.FMRP_REQUIRE_NO;
                                }
                            }
                        }
                    });
                }

            }
            else
            {
                workOrders = new List<WorkOrderModel>();
            }
            //获取自定义存货
            var workMatIds = workOrders.Select(p => p.FMATERIAL_ID).ToList();
            var matCustoms = await GetMaterialCustomsAsync(workMatIds);//获取物料自定义信息
            var customFields = await GetMatCustomFormAsync();//获取物料自定义字段

            //增加null值 判断
            if (matCustoms.StatusCode == 200 && matCustoms.Entity != null && customFields.StatusCode == 200 && customFields.Entity != null)
            {
                workOrders.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
                {
                    var matCustom = matCustoms.Entity.Where(p => p.FMATERIAL_ID == item.FMATERIAL_ID).FirstOrDefault();

                    if (matCustom != null)
                    {
                        var matCustomDic = JsonConvert.DeserializeObject<Dictionary<string, string>>(JsonConvert.SerializeObject(matCustom));
                        foreach (var field in customFields.Entity.CustomFields)
                        {
                            item.MaterialCustom.Add(field.FFIELD_NAME, matCustomDic[field.FFIELD_NAME]);

                        }
                    }
                });
            }


            var workIds = workOrders.Select(p => p.FWORK_ORDER_ID).ToList();
            if (outInfos.Entity == null)
                outInfos = await outOrdserver.GetByIdsAsync(workIds);

            var proids = workOrders.Select(p => p.FPRO_UNIT_ID).ToList();
            var unitids = workOrders.Select(p => p.FUNIT_ID).ToList();
            proids.AddRange(unitids);
            var unit = await _businessService.GetUnitNameByIdsAsync(proids.Distinct().ToList());
            workOrders.AsParallel().WithDegreeOfParallelism(4).ForAll(async item =>
            {
                var unitpro = unit.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FPRO_UNIT_ID);

                if (unitpro != null)
                {
                    item.FPRO_UNIT_NAME = unitpro.FUNIT_NAME;
                }

                var unitbase = unit.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FUNIT_ID);

                if (unitbase != null)
                {
                    item.FUNIT_NAME = unitbase.FUNIT_NAME;
                }

                var outNum = outInfos.Entity.FirstOrDefault(p => p.WoMatId == item.FWORK_ORDER_ID);
                item.OutOrdCount = outNum.count;
            });


            DataResult<List<WorkOrderModel>> result = new DataResult<List<WorkOrderModel>>()
            {
                StatusCode = 200,
                Entity = workOrders,
                Pager = new PagerResult()
                {
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                    TotalRecords = totalSize.Value,
                },
            };
            return await OK(result);

        }


        /// <summary>
        /// 获取所有的生产工单
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WorkOrderModel>>> GetAllAsync(QueryRequestModel model)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            string orderByString = null;
            if (model.Orders != null && model.Orders.Any())
            {
                orderByString = string.Join(",",
                    model.Orders.Select(x => $"{x.Fields[0]} {x.OrderType}"));
            }

            var wheres = WhereCollections.FromQueryRequestModel(model);
            RefAsync<int> totalSize = new RefAsync<int>();
            List<WorkOrderModel> workOrders = null;

            //取出符合条件的id
            var woIds = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID))
                //  .OrderBy((a, b) => a.FCDATE, OrderByType.Desc)   //按建立日期倒序

                .OrderBy(" FWORK_ORDER_NO desc")
                .Select((a, b) => new { a.FWORK_ORDER_ID, a.FFULL_WORK_ORDER_ID })
                             .Where(wheres)
                             .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);


            if (woIds.Count > 0)
            {
                //取出子工单id
                var subWOIds = await GetFullSubWOIdsAsync(woIds.Select(p => p.FFULL_WORK_ORDER_ID).ToList());

                var ids = woIds.Select(p => p.FWORK_ORDER_ID).ToList().Union(subWOIds).ToList();

                //取出父工单id
                ConcurrentBag<string> pathIds = new ConcurrentBag<string>();
                woIds.Select(p => p.FFULL_WORK_ORDER_ID).AsParallel().WithDegreeOfParallelism(4).ForAll(fullId =>
                {
                    if (fullId.Contains("/"))
                    {
                        var fullIdArr = fullId.Split('/');
                        for (int i = 0; i < fullIdArr.Length - 1; i++)
                        {
                            pathIds.Add(fullIdArr[i]);
                        }
                    }
                });

                //得出合并后的工单Id
                ids = ids.Union(pathIds.ToList().Distinct()).ToList();

                //根据ids取工单数据
                workOrders = (await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID))
                    .Select<WorkOrderModel>()
                    .Where((a) => ids.Contains(a.FWORK_ORDER_ID))
                    .ToListAsync()).OrderByDescending(p => p.FWORK_ORDER_NO).ToList();

                if (workOrders.Count > 0)
                {
                    List<string> materialIds = workOrders.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                    //取出物料信息
                    DataResult<List<SimpleMaterialModel>> materialResult = await GetMaterialsAsync(materialIds);
                    if (materialResult.StatusCode != 200)
                    {
                        ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                    }

                    //取计划员姓名
                    DataResult<List<EmployeeModel>> empResults = null;
                    var empIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                    if (empIds.Count > 0)
                    {
                        empResults = await GetEmployeesAsync(empIds);
                        if (empResults.StatusCode != 200)
                        {
                            ERROR(empResults, empResults.StatusCode, empResults.Message);
                        }
                    }

                    //取部门
                    var deptIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FDEPT_ID)).Select(p => p.FDEPT_ID).Distinct().ToList();
                    DataResult<List<MakerModel>> deptResults = null;
                    if (deptIds.Count > 0)
                    {
                        deptResults = await _businessService.GetDeptByIdsAsync(deptIds);
                        if (deptResults.StatusCode != 200)
                        {
                            ERROR(deptResults, deptResults.StatusCode, deptResults.Message);
                        }
                    }

                    var saleIds = workOrders.Select(p => p.FSALE_ORDER_ID).Distinct().ToList();
                    DataResult<List<SaleOrderModel>> saleOrderResults = null;
                    if (saleIds.Count > 0)
                    {
                        saleOrderResults = await GetSaleOrderAllAsync(saleIds);
                        if (saleOrderResults.StatusCode != 200)
                        {
                            ERROR(saleOrderResults, saleOrderResults.StatusCode, saleOrderResults.Message);
                        }
                    }

                    //取销售订单(有订单子表id值)
                    DataResult<List<SimpleSaleOrderMaterialModel>> soMaterialsResult = null;
                    var soMaterialIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_PRODUCT_ID))
                                        .Select(p => p.FSALE_ORDER_PRODUCT_ID)
                                        .Distinct()
                                        .ToList();
                    if (soMaterialIds.Count > 0)
                    {
                        soMaterialsResult = await GetSaleOrderMaterialsAsync(soMaterialIds);
                        if (soMaterialsResult.StatusCode != 200)
                        {
                            ERROR(soMaterialsResult, soMaterialsResult.StatusCode, soMaterialsResult.Message);
                        }
                    }

                    //取销售订单(排除订单子表id为空)，子工单: 有订单主表id，无订单子表id
                    DataResult<List<SaleOrderSimpleModel>> saleOrderResult = null;
                    var saleOrderIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID) &&
                                        string.IsNullOrWhiteSpace(p.FSALE_ORDER_PRODUCT_ID)).Select(p => p.FSALE_ORDER_ID).Distinct().ToList();
                    if (saleOrderIds.Count > 0)
                    {
                        saleOrderResult = await GetSaleOrdersAsync(saleOrderIds);
                        if (saleOrderResult.StatusCode != 200)
                        {
                            ERROR(saleOrderResult, saleOrderResult.StatusCode, saleOrderResult.Message);
                        }
                    }


                    //取需求单号  
                    DataResult<List<MrpRequireModel>> requireResult = null;
                    var sourceIds = workOrders.Where(p => p.FWORK_ORDER_TYPE == _RequireWO).Select(p => p.FSOURCE_ID).Distinct().ToList();
                    if (sourceIds.Count > 0)
                    {
                        requireResult = await GetRequiresAsync(sourceIds);
                        if (requireResult.StatusCode != 200)
                        {
                            ERROR(requireResult, requireResult.StatusCode, requireResult.Message);
                        }

                    }
                    var crafts = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>().Where(p => ids.Contains(p.FWORK_ORDER_ID)).ToListAsync();

                    workOrders.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                    {



                        //物料
                        var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == workOrder.FMATERIAL_ID);
                        if (material != null)
                        {
                            workOrder.FMATERIAL_CODE = material.FMATERIAL_CODE;
                            workOrder.FMATERIAL_NAME = material.FMATERIAL_NAME;
                            workOrder.FSPEC_DESC = material.FSPEC_DESC;
                            workOrder.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                            workOrder.FGOODS_MODEL = material.FGOODS_MODEL;

                            workOrder.FMATEIAL_CATE_NAME = String.Concat(!string.IsNullOrWhiteSpace(material.FMATERIAL_CATE_PARENT_NAME) ?
                                                    material.FMATERIAL_CATE_PARENT_NAME + "/" : string.Empty, material.FMATERIAL_CATE_NAME);

                            workOrder.FIF_3C = material.FIF_3C;


                        }

                        //计划员
                        if (!string.IsNullOrWhiteSpace(workOrder.FPLAN_EMP_ID) && empResults != null)
                        {
                            var emp = empResults.Entity.FirstOrDefault(p => p.FEMP_ID == workOrder.FPLAN_EMP_ID);
                            if (emp != null)
                            {
                                workOrder.FPLAN_EMP_NAME = emp.FEMP_NAME;
                                workOrder.FPLAN_EMP_CODE = emp.FEMP_CODE;
                            }
                        }

                        //部门
                        if (!string.IsNullOrWhiteSpace(workOrder.FDEPT_ID) && deptResults != null)
                        {
                            var dept = deptResults.Entity.FirstOrDefault(p => p.FMAKER_ID == workOrder.FDEPT_ID);
                            if (dept != null)
                            {
                                workOrder.FDEPT_NAME = dept.FMAKER_NAME;
                            }
                        }


                        //增加选择工单带出订单信息---兆科
                        if (!string.IsNullOrWhiteSpace(workOrder.FSALE_ORDER_ID) && saleOrderResults != null)
                        {
                            var so = saleOrderResults.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == workOrder.FSALE_ORDER_ID);
                            if (so != null)
                            {
                                workOrder.FCUST_NAME = so.FCUST_NAME;
                                workOrder.FSALE_BILL_DATE = so.FBILL_DATE;
                                if (so.SaleOrderMaterials.Count > 0)
                                {
                                    workOrder.FTEXT_02 = so.SaleOrderMaterials.FirstOrDefault().FTEXT_02;
                                }

                            }
                        }

                        //订单
                        if (!string.IsNullOrWhiteSpace(workOrder.FSALE_ORDER_PRODUCT_ID))
                        {
                            if (soMaterialsResult != null)
                            {
                                var so = soMaterialsResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_MATERIAL_ID == workOrder.FSALE_ORDER_PRODUCT_ID);
                                if (so != null)
                                {
                                    workOrder.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                                    workOrder.FSOURCE_NO = so.FSALE_ORDER_NO;

                                    workOrder.FCUSTOM_PARM = so.FCUSTOM_PARM;   //定制参数

                                    workOrder.FPROJECT_ID = so.FPROJECT_ID;  //项目
                                    workOrder.FPROJECT_NAME = so.FPROJECT_NAME;
                                    workOrder.FPROJECT_CODE = so.FPROJECT_CODE;

                                }
                            }
                        }

                        else
                        {
                            if (!string.IsNullOrWhiteSpace(workOrder.FSALE_ORDER_ID) && saleOrderResult != null)
                            {
                                var so = saleOrderResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == workOrder.FSALE_ORDER_ID);
                                if (so != null)
                                {
                                    workOrder.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                                    workOrder.FSOURCE_NO = so.FSALE_ORDER_NO;

                                    workOrder.FPROJECT_ID = so.FPROJECT_ID;  //项目
                                    workOrder.FPROJECT_NAME = so.FPROJECT_NAME;
                                    workOrder.FPROJECT_CODE = so.FPROJECT_CODE;
                                }
                            }
                        }



                        //需求单编号
                        if (requireResult != null)
                        {
                            if (workOrder.FWORK_ORDER_TYPE == _RequireWO)
                            {
                                var require = requireResult.Entity.FirstOrDefault(p => p.FMRP_REQUIRE_ID == workOrder.FSOURCE_ID);
                                if (require != null)
                                {
                                    workOrder.FSOURCE_NO = require.FMRP_REQUIRE_NO;
                                }
                            }
                        }
                    });
                }

            }
            else
            {
                workOrders = new List<WorkOrderModel>();
            }
            //获取自定义存货
            var workMatIds = workOrders.Select(p => p.FMATERIAL_ID).ToList();
            var matCustoms = await GetMaterialCustomsAsync(workMatIds);//获取物料自定义信息
            var customFields = await GetMatCustomFormAsync();//获取物料自定义字段

            //增加null值 判断
            if (matCustoms.StatusCode == 200 && matCustoms.Entity != null && customFields.StatusCode == 200 && customFields.Entity != null)
            {
                workOrders.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
                {
                    var matCustom = matCustoms.Entity.Where(p => p.FMATERIAL_ID == item.FMATERIAL_ID).FirstOrDefault();

                    if (matCustom != null)
                    {
                        var matCustomDic = JsonConvert.DeserializeObject<Dictionary<string, string>>(JsonConvert.SerializeObject(matCustom));
                        foreach (var field in customFields.Entity.CustomFields)
                        {

                            if (matCustomDic.ContainsKey(field.FFIELD_NAME))
                            {
                                item.MaterialCustom.Add(field.FFIELD_NAME, matCustomDic[field.FFIELD_NAME]);
                            }
                        }
                    }
                });
            }


            var workBomIds = workOrders.Select(p => p.FMATERIAL_BOM_ID).ToList();
            var boms = await GetService<IMSD004MaterialBOMService>("MSD004MaterialBOM").GetBomVersionsByBomIdsAsync(workBomIds);
            workOrders.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var bom = boms.Entity.FirstOrDefault(p => p.FMATERIAL_BOM_ID == item.FMATERIAL_BOM_ID);
                if (bom != null)
                {
                    item.FBOM_VERSION = bom.FBOM_VERSION;
                }


            });





            var workIds = workOrders.Select(p => p.FWORK_ORDER_ID).ToList();
            var outOrdserver = GetService<IPUR011OutOrderService>("PUR011OutOrder");
            var outInfos = await outOrdserver.GetByIdsAsync(workIds);


            workOrders.AsParallel().WithDegreeOfParallelism(4).ForAll(async item =>
            {

                var outNum = outInfos.Entity.FirstOrDefault(p => p.WoMatId == item.FWORK_ORDER_ID);
                item.OutOrdCount = outNum.count;
            });






            DataResult<List<WorkOrderModel>> result = new DataResult<List<WorkOrderModel>>()
            {
                StatusCode = 200,
                Entity = workOrders,
                Pager = new PagerResult()
                {
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                    TotalRecords = totalSize.Value,
                },
            };
            return await OK(result);

        }

        /// <summary>
        /// 获取所有的生产工单
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WorkOrderModel>>> GetAllWorkOrderAsync(WorkQueryRequstModel model)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            //物料
            if (model.MaterialQRM != null)
            {
                //查询物料ids
                var matrpcServer = this.GetService<IModuleServices.MSD002_Material.IMSD002MaterialService>("MSD002Material");
                var matids = await matrpcServer.GetMaterialIDSAsync(model.MaterialQRM);
                if (matids.StatusCode != 200)
                {
                    ERROR(matids, 111111, "根据条件查询物料ID失败");
                }
                if (matids.Entity.Count > 0)
                {
                    if (model.WhereGroup.Groups == null)
                    {
                        model.WhereGroup.Groups = new List<QueryWhereGroupModel>();
                    }
                    if (model.WhereGroup.Groups.Count > 0)
                    {
                        // 如果Items有值时则加入否则创建新的Items
                        if (model.WhereGroup.Groups[0].Items != null && model.WhereGroup.Groups[0].Items.Count > 0)
                        {
                            model.WhereGroup.Groups[0].Items.Add(new QueryWhereItemModel()
                            {
                                FieldName = "a.FMATERIAL_ID",
                                OperatorType = EnumQuerySymbol.In,
                                Value = string.Join(',', matids.Entity)
                            });
                        }
                        else
                        {
                            model.WhereGroup.Groups[0].Items = new List<QueryWhereItemModel>()
                            {
                                new QueryWhereItemModel()
                                {
                                    FieldName = "a.FMATERIAL_ID",
                                    OperatorType = EnumQuerySymbol.In,
                                    Value = string.Join(',', matids.Entity)
                                }
                            };
                        }
                    }

                    //model.WhereGroup.Items.Add(new QueryWhereItemModel()
                    //{
                    //    FieldName = "a.FMATERIAL_ID",
                    //    OperatorType = EnumQuerySymbol.In,
                    //    Value = string.Join(',', matids.Entity)
                    //});
                }
            }
            //销售订单
            if (model.SaleQRM != null)
            {
                //查询物料ids
                var salerpcServer = this.GetService<IModuleServices.COP001_SaleOrder.ICOP001SaleOrderService>("COP001SaleOrder");
                var saleResult = await salerpcServer.GetAllAsync(model.SaleQRM);
                if (saleResult.StatusCode != 200)
                {
                    ERROR(saleResult, 111111, "根据条件查询销售订单失败");
                }
                model.PageIndex = model.SaleQRM.PageIndex;
                model.PageSize = model.SaleQRM.PageSize;
                if (saleResult.Entity.Count > 0)
                {
                    var saleIds = saleResult.Entity.Select(p => p.FSALE_ORDER_ID).Distinct().ToList();

                    if (model.WhereGroup.Groups == null)
                    {
                        model.WhereGroup.Groups = new List<QueryWhereGroupModel>();
                    }
                    if (model.WhereGroup.Groups.Count > 0)
                    {
                        if (model.WhereGroup.Groups[0].Items == null)
                        {
                            model.WhereGroup.Groups[0].Items = new List<QueryWhereItemModel>();
                        }

                        if (model.WhereGroup.Groups[0].Items.Count > 0)
                        {
                            model.WhereGroup.Groups[0].Items.Add(new QueryWhereItemModel()
                            {
                                FieldName = "a.FSALE_ORDER_ID",
                                OperatorType = EnumQuerySymbol.In,
                                Value = string.Join(',', saleIds)
                            });
                        }
                        else
                        {
                            model.WhereGroup.Groups[0].Items = new List<QueryWhereItemModel>()
                            {
                                new QueryWhereItemModel()
                                {
                                    FieldName = "a.FSALE_ORDER_ID",
                                    OperatorType = EnumQuerySymbol.In,
                                    Value = string.Join(',', saleIds)
                                }
                            };
                        }
                    }


                    // model.WhereGroup.Items.Add(new QueryWhereItemModel()
                    // {
                    //     FieldName = "a.FSALE_ORDER_ID",
                    //     OperatorType = EnumQuerySymbol.In,
                    //     Value = string.Join(',', saleIds)
                    // });
                }

            }
            //部门
            if (model.MarkQRM != null)
            {
                //查询部门ids
                var markrpcServer = this.GetService<IEmployeeService>("ADM024Employee");
                var markids = await markrpcServer.GetMarkIDSAsync(model.MarkQRM);
                if (markids.StatusCode != 200)
                {
                    ERROR(markids, markids.StatusCode, markids.Message);
                }

                model.WhereGroup.Items.Add(new QueryWhereItemModel()
                {
                    FieldName = "a.FDEPT_ID",
                    OperatorType = EnumQuerySymbol.In,
                    Value = string.Join(",", markids.Entity)
                });
            }

            //取出保存结果返回
            QueryRequestModel queryModel = new QueryRequestModel()
            {
                WhereGroup = new QueryWhereGroupModel(),
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
            };
            queryModel.WhereGroup = model.WhereGroup;
            var queryResult = await GetAllAsync(queryModel);   //返回本次保存的工单主表
            return queryResult;

        }

        /// <summary>
        /// 根据委外单 更新工单 结案状态
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<string>> UpdateWorkOrdByOutOrdAsync(List<WorkOrdInfo> model)
        {
            var result = new DataResult<string>
            {
                Entity = "1",
                StatusCode = 200,
            };
            var db = _isugar.DB;

            var workIds = model.Select(p => p.WoMatId).ToList();
            var workInfos = await db.Queryable<T_MESD_WORK_ORDER>().Where(p => workIds.Contains(p.FWORK_ORDER_ID)).ToListAsync();
            var workSttatusInfos = await db.Queryable<T_MESD_WORK_ORDER_STATUS>().Where(p => workIds.Contains(p.FWORK_ORDER_ID)).ToListAsync();

            var workStatus = new List<T_MESD_WORK_ORDER_STATUS>();

            foreach (var item in model)
            {
                var work = workInfos.FirstOrDefault(p => p.FWORK_ORDER_ID == item.WoMatId);
                var workstatus = workSttatusInfos.FirstOrDefault(p => p.FWORK_ORDER_ID == item.WoMatId);
                //if (work.FPRO_QTY==item.count)
                //{
                //    workstatus.FIF_CLOSE = 1;//自动结案
                //    workStatus.Add(workstatus);
                //}else
                //if (work.FPRO_QTY > item.count)
                //{
                //    if (workstatus.FIF_CLOSE==1)
                //    {
                //        workstatus.FIF_CLOSE = 2;//反结案
                //        workStatus.Add(workstatus);
                //    }
                //}else
                if (work.FPRO_QTY < item.count)
                {
                    result.Entity = $"工单 {work.FWORK_ORDER_NO} 数量:{work.FPRO_QTY}";//返回一个数量
                    return await OK(result);
                }
            }
            //if (workStatus.Count>0)
            //{
            //    await db.Updateable<T_MESD_WORK_ORDER_STATUS>(workStatus).UpdateColumns(p => new { p.FIF_CLOSE }).ExecuteCommandAsync();
            //}

            return await OK(result);
        }
        /// <summary>
        /// 根据工单ID获取简化的工单详情(用于质检等轻量化场景)
        /// </summary>
        /// <param name="ids">工单ID列表</param>
        /// <returns></returns>
        public async Task<DataResult<List<SimpleWorkOrderDetailModel>>> GetWorkOrderSimpleAsync(List<string> ids)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            if (ids == null || ids.Count == 0)
            {
                ERROR(null, 100010, _multiLang["工单ID列表不能为空"]);
            }

            // 查询工单基本信息
            var workOrders = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>
                ((wo, wos) => new JoinQueryInfos(JoinType.Left, wo.FWORK_ORDER_ID == wos.FWORK_ORDER_ID))
                .Where((wo, wos) => ids.Contains(wo.FWORK_ORDER_ID))
                .Select<SimpleWorkOrderDetailModel>()
                .ToListAsync();

            if (workOrders == null || workOrders.Count == 0)
            {
                ERROR(null, 100010, string.Format(_multiLang["工单ID {0} 不存在"], string.Join(",", ids)));
            }

            // 批量获取物料信息
            var materialIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FMATERIAL_ID))
                .Select(p => p.FMATERIAL_ID).Distinct().ToList();
            DataResult<List<SimpleMaterialModel>> materialResult = null;
            if (materialIds.Count > 0)
            {
                materialResult = await GetMaterialsAsync(materialIds);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }
            }

            // 批量获取生产单位信息
            var unitIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FPRO_UNIT_ID))
                .Select(p => p.FPRO_UNIT_ID).Distinct().ToList();
            DataResult<List<SimpleUnitModel>> unitResult = null;
            if (unitIds.Count > 0)
            {
                unitResult = await _businessService.GetUnitNameByIdsAsync(unitIds);
                if (unitResult.StatusCode != 200)
                {
                    ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                }
            }

            // 批量获取销售订单和客户信息
            var saleOrderIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID))
                .Select(p => p.FSALE_ORDER_ID).Distinct().ToList();
            DataResult<List<SaleOrderSimpleModel>> saleOrderResult = null;
            if (saleOrderIds.Count > 0)
            {
                saleOrderResult = await GetSaleOrdersAsync(saleOrderIds);
                if (saleOrderResult.StatusCode != 200)
                {
                    ERROR(saleOrderResult, saleOrderResult.StatusCode, saleOrderResult.Message);
                }
            }

            // 填充关联数据
            workOrders.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
            {
                // 填充物料信息
                if (!string.IsNullOrWhiteSpace(workOrder.FMATERIAL_ID) && materialResult != null)
                {
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == workOrder.FMATERIAL_ID);
                    if (material != null)
                    {
                        workOrder.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        workOrder.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        workOrder.FSPEC_DESC = material.FSPEC_DESC;
                        workOrder.FGOODS_MODEL = material.FGOODS_MODEL;
                    }
                }

                // 填充生产单位信息
                if (!string.IsNullOrWhiteSpace(workOrder.FPRO_UNIT_ID) && unitResult != null)
                {
                    var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrder.FPRO_UNIT_ID);
                    if (unit != null)
                    {
                        workOrder.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                    }
                }

                // 填充销售订单和客户信息
                if (!string.IsNullOrWhiteSpace(workOrder.FSALE_ORDER_ID) && saleOrderResult != null)
                {
                    var saleOrder = saleOrderResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == workOrder.FSALE_ORDER_ID);
                    if (saleOrder != null)
                    {
                        workOrder.FSALE_ORDER_NO = saleOrder.FSALE_ORDER_NO;
                        workOrder.FCUST_ID = saleOrder.FCUST_ID;
                        workOrder.FCUST_CODE = saleOrder.FCUST_CODE;
                        workOrder.FCUST_NAME = saleOrder.FCUST_NAME;
                    }
                }
            });

            DataResult<List<SimpleWorkOrderDetailModel>> result = new DataResult<List<SimpleWorkOrderDetailModel>>()
            {
                StatusCode = 200,
                Entity = workOrders,
            };
            return await OK(result);
        }

        /// <summary>
        /// 根据生产工单id返回数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<DataResult<WorkOrderModel>> GetByIdAsync(string id)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            WorkOrderModel workOrder = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID))
                .Select<WorkOrderModel>()
                .Where((a) => a.FWORK_ORDER_ID == id)
                .FirstAsync();




            if (workOrder != null)
            {
                // //iuiu自定义字段
                //var iuiucustom=   await db.Queryable<T_MESD_WORK_ORDER_MATERIAL_IUIU_CUSTOM>().Where(p => p.FWORK_ORDER_ID == id).FirstAsync();
                // if (iuiucustom!=null)
                // {
                //     workOrder.IUIUcustom = iuiucustom;
                // }
                //远程调用单位
                var unitids = new List<string>() { workOrder.FWEIGHT_UNIT_ID, workOrder.FUNIT_ID, workOrder.FPRO_UNIT_ID };
                var rpcServer = this.GetService<IMSD001UnitService>("MSD001Unit");
                var rpcrtn = await rpcServer.GetByIdsForBusinessAsync(unitids);
                if (rpcrtn.StatusCode == 200)
                {
                    var unitobj = rpcrtn.Entity.Where(p => p.FUNIT_ID == workOrder.FWEIGHT_UNIT_ID).FirstOrDefault();
                    if (unitobj != null)
                    {
                        workOrder.FWEIGHT_UNIT_NAME = unitobj.FUNIT_NAME;

                    }
                    unitobj = rpcrtn.Entity.Where(p => p.FUNIT_ID == workOrder.FUNIT_ID).FirstOrDefault();
                    if (unitobj != null)
                    {
                        workOrder.FUNIT_NAME = unitobj.FUNIT_NAME;
                        workOrder.FUNIT_CODE = unitobj.FUNIT_CODE;
                    }
                    unitobj = rpcrtn.Entity.Where(p => p.FUNIT_ID == workOrder.FPRO_UNIT_ID).FirstOrDefault();
                    if (unitobj != null)
                    {
                        workOrder.FPRO_UNIT_NAME = unitobj.FUNIT_NAME;

                    }
                }




                //取出父件物料信息
                DataResult<List<SimpleMaterialModel>> materialResult = await GetMaterialsAsync(new List<string>() { workOrder.FMATERIAL_ID });
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }

                var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == workOrder.FMATERIAL_ID);
                if (material != null)
                {
                    workOrder.FMATERIAL_CODE = material.FMATERIAL_CODE;
                    workOrder.FMATERIAL_NAME = material.FMATERIAL_NAME;
                    workOrder.FSPEC_DESC = material.FSPEC_DESC;
                    workOrder.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                    workOrder.FIF_3C = material.FIF_3C;
                    workOrder.FGOODS_MODEL = material.FGOODS_MODEL;
                    workOrder.FMADE_DEPT_ID = material.FMADE_DEPT_ID;
                    workOrder.FMADE_DEPT_CODE = material.FMADE_DEPT_CODE;
                    workOrder.FMADE_DEPT_NAME = material.FMADE_DEPT_NAME;
                }

                //取销售订单(有订单子表id值)
                if (!string.IsNullOrWhiteSpace(workOrder.FSALE_ORDER_PRODUCT_ID))
                {
                    var soMaterialsResult = await GetSaleOrderMaterialsAsync(new List<string> { workOrder.FSALE_ORDER_PRODUCT_ID });
                    if (soMaterialsResult.StatusCode != 200)
                    {
                        ERROR(soMaterialsResult, soMaterialsResult.StatusCode, soMaterialsResult.Message);
                    }

                    if (soMaterialsResult.Entity.Count > 0)
                    {
                        var so = soMaterialsResult.Entity[0];
                        workOrder.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                        workOrder.FCUSTOM_PARM = so.FCUSTOM_PARM;   //定制参数
                    }
                }

                //取销售订单(无订单子表id) 子工单：有订单主表id，无订单子表id
                if (!string.IsNullOrWhiteSpace(workOrder.FSALE_ORDER_ID) && string.IsNullOrWhiteSpace(workOrder.FSALE_ORDER_PRODUCT_ID))
                {

                    var saleOrderResult = await GetSaleOrdersAsync(new List<string> { workOrder.FSALE_ORDER_ID });
                    if (saleOrderResult.StatusCode != 200)
                    {
                        ERROR(saleOrderResult, saleOrderResult.StatusCode, saleOrderResult.Message);
                    }

                    var so = saleOrderResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == workOrder.FSALE_ORDER_ID);
                    if (so != null)
                    {
                        workOrder.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                        workOrder.FSOURCE_NO = so.FSALE_ORDER_NO;
                        workOrder.FPROJECT_ID = so.FPROJECT_ID;  //项目
                        workOrder.FPROJECT_NAME = so.FPROJECT_NAME;
                        workOrder.FPROJECT_CODE = so.FPROJECT_CODE;
                    }
                }

                //获取需求单编号
                if (workOrder.FWORK_ORDER_TYPE == _RequireWO)
                {
                    var requireResult = await GetRequiresAsync(new List<string> { workOrder.FSOURCE_ID });
                    if (requireResult.StatusCode != 200)
                    {
                        ERROR(requireResult, requireResult.StatusCode, requireResult.Message);
                    }
                    if (requireResult.Entity != null && requireResult.Entity.Count > 0)
                    {
                        workOrder.FSOURCE_NO = requireResult.Entity[0].FMRP_REQUIRE_NO;
                    }
                }

                //取员工
                if (!string.IsNullOrWhiteSpace(workOrder.FPLAN_EMP_ID))
                {
                    var empResults = await _businessService.GetEmployeeByIdsAsync(new List<string> { workOrder.FPLAN_EMP_ID });
                    if (empResults.StatusCode != 200)
                    {
                        ERROR(empResults, empResults.StatusCode, empResults.Message);
                    }
                    if (empResults.Entity != null && empResults.Entity.Count > 0)
                    {
                        var empEntity = empResults.Entity[0];
                        workOrder.FPLAN_EMP_CODE = empEntity.FEMP_CODE;
                        workOrder.FPLAN_EMP_NAME = empEntity.FEMP_NAME;
                    }
                }

                //取部门
                if (!string.IsNullOrWhiteSpace(workOrder.FDEPT_ID))
                {
                    var deptResults = await _businessService.GetDeptByIdsAsync(new List<string> { workOrder.FDEPT_ID });
                    if (deptResults.StatusCode != 200)
                    {
                        ERROR(deptResults, deptResults.StatusCode, deptResults.Message);
                    }
                    if (deptResults.Entity != null && deptResults.Entity.Count > 0)
                    {
                        var deptEntity = deptResults.Entity[0];
                        workOrder.FDEPT_NAME = deptEntity.FMAKER_NAME;
                    }
                }

                //取出子件列表
                workOrder.Materials = (await db.Queryable<T_MESD_WORK_ORDER_MATERIAL, T_MESD_WORK_ORDER_MATERIAL_STATUS>
                    ((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_MATERIAL_ID == b.FWORK_ORDER_MATERIAL_ID))
                    .Where((a, b) => a.FWORK_ORDER_ID == id)
                    .Select<WorkOrderMaterialModel>()
                    .ToListAsync()).OrderBy(p => p.FSHOW_SEQNO).ToList();


                //远程调用单位
                var unitmatids = workOrder.Materials.Select(p => p.FSUB_UNIT_ID).Distinct().ToList();
                var unitRpcServer = this.GetService<IMSD001UnitService>("MSD001Unit");
                var unitRpcrtn = await unitRpcServer.GetByIdsForBusinessAsync(unitmatids);

                //取出子件材料信息
                var subMaterialIds = workOrder.Materials.Where(p => !string.IsNullOrWhiteSpace(p.FSUB_MATERIAL_ID)).Select(p => p.FSUB_MATERIAL_ID).Distinct().ToList();
                if (subMaterialIds.Count > 0)
                {
                    materialResult = await GetMaterialsAsync(subMaterialIds);
                    if (materialResult.StatusCode != 200)
                    {
                        ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                    }

                    workOrder.Materials.AsParallel().WithDegreeOfParallelism(4).ForAll(woMaterial =>
                    {
                        var woUnit = unitRpcrtn.Entity.FirstOrDefault(p => woMaterial.FSUB_UNIT_ID == p.FUNIT_ID);
                        if (woUnit != null)
                        {
                            woMaterial.FSUB_UNIT_CODE = woUnit.FUNIT_CODE;
                            woMaterial.FSUB_UNIT_NAME = woUnit.FUNIT_NAME;
                        }
                        var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == woMaterial.FSUB_MATERIAL_ID);
                        if (material != null)
                        {
                            woMaterial.FSUB_MATERIAL_CODE = material.FMATERIAL_CODE;
                            woMaterial.FSUB_MATERIAL_NAME = material.FMATERIAL_NAME;
                            woMaterial.FSUB_SPEC_DESC = material.FSPEC_DESC;
                            woMaterial.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                            woMaterial.FSUB_GOODS_MODEL = material.FGOODS_MODEL;
                            woMaterial.FPRO_FETCH_LOT_QTY = material.FPRO_FETCH_LOT_QTY;
                        }
                    });
                }

                //子表自定义字段
                var daoMaterialCustoms = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL_CUSTOM>().Where(p => p.FWORK_ORDER_ID == id).ToListAsync();
                var dtoMaterialCustoms = daoMaterialCustoms.MapToDestObj<T_MESD_WORK_ORDER_MATERIAL_CUSTOM, WorkOrderMaterialCustomModel>();

                workOrder.Materials.AsParallel().WithDegreeOfParallelism(4).ForAll(woMaterial =>
                {
                    woMaterial.CustomModel = dtoMaterialCustoms.FirstOrDefault(p => p.FWORK_ORDER_MATERIAL_ID == woMaterial.FWORK_ORDER_MATERIAL_ID);
                });

                //取出工序列表
                workOrder.Crafts = (await db.Queryable<T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>
                                                ((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_CRAFT_ID == b.FWORK_ORDER_CRAFT_ID))
                    .Where((a, b) => a.FWORK_ORDER_ID == id)
                    .Select<WorkOrderCraftModel>()
                    .ToListAsync()).OrderBy(p => p.FSHOW_SEQNO).ToList();

                //取出工序信息
                var craftIds = workOrder.Crafts.Where(p => !string.IsNullOrWhiteSpace(p.FCRAFT_ID)).Select(p => p.FCRAFT_ID).Distinct().ToList();
                if (craftIds.Count > 0)
                {
                    var craftResult = await GetCraftsAsync(craftIds);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }

                    workOrder.Crafts.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                        if (craft != null)
                        {
                            woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                            woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                            woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                        }
                    });
                }

                //取出工位信息
                var stationIds = workOrder.Crafts.Where(p => !string.IsNullOrWhiteSpace(p.FSTATION_ID)).Select(p => p.FSTATION_ID).Distinct().ToList();
                if (stationIds.Count > 0)
                {
                    var stationResult = await GetStationListAsync(stationIds);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }

                    workOrder.Crafts.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        var station = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == woCraft.FSTATION_ID);
                        if (station != null)
                        {
                            woCraft.FSTATION_PATH = station.FSTATION_PATH;
                        }
                    });
                }


                //取出工单可供选择的bom版本
                await GetWOBomVersionsAsync(workOrder);

                //取出工单可供选择的工艺路线
                await GetWOCraftLinesAsync(workOrder);

                //取出工单可供选择的单位转换
                await GetWOMaterialUnitsAsync(workOrder);

                //获取自定义
                var daoWoCustom = await db.Queryable<T_MESD_WORK_ORDER_CUSTOM>().Where(p => p.FWORK_ORDER_ID == id).FirstAsync();
                if (daoWoCustom != null)
                {
                    workOrder.CustomModel = daoWoCustom.MapToDestObj<T_MESD_WORK_ORDER_CUSTOM, WorkOrderCustomModel>();
                }
            }

            DataResult<WorkOrderModel> result = new DataResult<WorkOrderModel>()
            {
                StatusCode = 200,
                Entity = workOrder,
            };
            return await OK(result);
        }

        /// <summary>
        /// 检查是否引用了需求单Id, 返回工单编号
        /// </summary>
        /// <param name="requireIds"></param>
        /// <returns></returns>
        public async Task<DataResult<List<string>>> CheckReferRequireIdsAsync(List<string> requireIds)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var prayNos = await
            db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((wo, status) => new JoinQueryInfos(
                    JoinType.Inner, wo.FWORK_ORDER_ID == status.FWORK_ORDER_ID
                )).Where((wo, status) => wo.FWORK_ORDER_TYPE == _RequireWO
                    && status.FIF_CANCEL == false
                    && requireIds.Contains(wo.FSOURCE_ID))
                .Select((wo, status) => wo.FWORK_ORDER_NO)
                .Distinct()
                .ToListAsync();

            DataResult<List<string>> dataResult = new DataResult<List<string>>
            {
                StatusCode = 200,
                Entity = prayNos,
            };

            return await OK(dataResult);
        }

        /// <summary>
        /// 检查需求分析采购建议id是否被工单引用,返回工单编号
        /// </summary>
        public async Task<DataResult<List<string>>> CheckReferRequireProIdsAsync(List<string> requireProIds)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var prayNos = await
            db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((wo, status) => new JoinQueryInfos(
                    JoinType.Inner, wo.FWORK_ORDER_ID == status.FWORK_ORDER_ID
                )).Where((wo, status) => wo.FWORK_ORDER_TYPE == _RequireWO
                    && status.FIF_CANCEL == false
                    && requireProIds.Contains(wo.FSOURCE_DETAIL_ID))
                .Select((wo, status) => wo.FWORK_ORDER_NO)
                .Distinct()
                .ToListAsync();

            DataResult<List<string>> dataResult = new DataResult<List<string>>
            {
                StatusCode = 200,
                Entity = prayNos,
            };

            return await OK(dataResult);
        }

        #region privates

        /// <summary>
        /// 返回物料需求单信息
        /// </summary>
        /// <param name="requireIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<MrpRequireModel>>> GetRequiresAsync(List<string> requireIds)
        {
            var rpcServer = this.GetService<IModuleServices.MRP001_Require.IMRP001RequireService>("MRP001Require");
            return await rpcServer.GetSimpleRequiresAsync(requireIds);
        }


        /// <summary>
        /// 返回销售订单列表
        /// </summary>
        /// <param name="saleOrderIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SaleOrderSimpleModel>>> GetSaleOrdersAsync(List<string> saleOrderIds)
        {

            //销售订单
            var saleOrdrpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
            return await saleOrdrpcServer.QuerySalesByIdsAsync(saleOrderIds);

        }

        /// <summary>
        /// 返回销售订单子表列表
        /// </summary>
        /// <param name="saleOrderIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleSaleOrderMaterialModel>>> GetSaleOrderMaterialsAsync(List<string> soMaterialIds)
        {

            //销售订单
            var saleOrdrpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
            return await saleOrdrpcServer.GetSaleOrderMaterialsAsync(soMaterialIds);

        }

        /// <summary>
        /// 根据工单ID获取质检表
        /// </summary>
        /// <param name="ordIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SaleMatCheckModel>>> GetCheckByWoIdsAsync(List<string> ordIds)
        {
            var rpcServer = this.GetService<IQCS018XFMatCheckService>("QCS018XFMatCheck");
            return await rpcServer.GetCheckByWoIdsAsync(ordIds);
        }


        /// <summary>
        /// 返回销售订单列表
        /// </summary>
        /// <param name="saleOrderIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SaleOrderModel>>> GetSaleOrderAllAsync(List<string> saleIds)
        {

            var queryModel = new QueryRequestModel()
            {
                WhereGroup = new QueryWhereGroupModel(),
                PageIndex = 1,
                PageSize = 100,
            };
            queryModel.WhereGroup.GroupType = EnumGroupType.AND;
            queryModel.WhereGroup.Items = new List<QueryWhereItemModel>();
            queryModel.WhereGroup.Items.Add(new QueryWhereItemModel()
            {
                FieldName = "saleOrder.FSALE_ORDER_ID",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.In,
                Value = string.Join(",", saleIds),
            });


            //销售订单
            var saleOrdrpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
            return await saleOrdrpcServer.GetAllAsync(queryModel);

        }

        /// <summary>
        /// 返回工位列表
        /// </summary>
        /// <param name="stationIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleStationModel>>> GetStationListAsync(List<string> stationIds)
        {
            var rpcServer = this.GetService<IMES001StationService>("MES001Station");
            return await rpcServer.GetForBusinessByIdsAsync(stationIds);
        }

        /// <summary>
        /// 取出工单物料可供选择的单位
        /// </summary>
        /// <param name="woModel"></param>
        /// <returns></returns>
        private async Task GetWOMaterialUnitsAsync(WorkOrderModel woModel)
        {
            List<string> materialIds = new List<string> { woModel.FMATERIAL_ID };
            materialIds = materialIds.Union(woModel.Materials.Select(p => p.FSUB_MATERIAL_ID).Distinct().ToList()).ToList();

            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            var unitResult = await rpcServer.GetUnitConvertsByIdsAsync(materialIds);
            if (unitResult.StatusCode != 200)
            {
                ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
            }

            if (unitResult.Entity.ContainsKey(woModel.FMATERIAL_ID))
            {
                woModel.UnitConverts = unitResult.Entity[woModel.FMATERIAL_ID];
            }
            else
            {
                woModel.UnitConverts = new List<MaterialUnitModel>();
            }

            woModel.Materials.AsParallel().WithDegreeOfParallelism(4).ForAll(material =>
            {
                if (unitResult.Entity.ContainsKey(material.FSUB_MATERIAL_ID))
                {
                    material.UnitConverts = unitResult.Entity[material.FSUB_MATERIAL_ID];
                }
                else
                {
                    material.UnitConverts = new List<MaterialUnitModel>();
                }
            });
        }

        /// <summary>
        /// 取出工单可供选择BOM版本
        /// </summary>
        /// <param name="woModel"></param>
        /// <returns></returns>
        private async Task GetWOBomVersionsAsync(WorkOrderModel woModel)
        {
            var rpcServer = this.GetService<IMSD004MaterialBOMService>("MSD004MaterialBOM");
            var bomVersionsResult = await rpcServer.GetUseBomVersionsByMaterialIdAsync(woModel.FMATERIAL_ID);
            if (bomVersionsResult.StatusCode != 200)
            {
                ERROR(bomVersionsResult, bomVersionsResult.StatusCode, bomVersionsResult.Message);
            }
            woModel.BomVersions = bomVersionsResult.Entity;
        }

        /// <summary>
        /// 取出工单可供选择工艺路线
        /// </summary>
        /// <param name="woModel"></param>
        /// <returns></returns>
        private async Task GetWOCraftLinesAsync(WorkOrderModel woModel)
        {
            var rpcServer = this.GetService<IMES002ProductCraftLineService>("MES002ProductCraftLine");
            var craftLineResult = await rpcServer.GetProductCraftLinesAsync(woModel.FMATERIAL_ID);
            if (craftLineResult.StatusCode != 200)
            {
                ERROR(craftLineResult, craftLineResult.StatusCode, craftLineResult.Message);
            }
            woModel.CraftLins = craftLineResult.Entity;
        }

        /// <summary>
        /// 返回指定id的全部子工单id
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        private async Task<List<string>> GetAllSubWOIdsAsync(List<string> ids)
        {
            var db = _isugar.DB;
            List<string> subWOIds = null;

            if (ids.Count > 0)
            {
                var fullWOIds = await db.Queryable<T_MESD_WORK_ORDER>().Where(t => ids.Contains(t.FWORK_ORDER_ID)).Select(t => t.FFULL_WORK_ORDER_ID).ToListAsync();
                return await GetFullSubWOIdsAsync(fullWOIds);
            }
            else
            {
                subWOIds = new List<string>();
            }
            return await Task.FromResult(subWOIds);
        }

        /// <summary>
        /// 返回指定fullId的全部子工单id
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        private async Task<List<string>> GetFullSubWOIdsAsync(List<string> fullIds)
        {
            var db = _isugar.DB;
            List<string> subWOIds = null;

            if (fullIds.Count > 0)
            {
                WhereCollections wheres = new WhereCollections();
                wheres.GroupType = WhereType.Or;
                wheres.ConditionalModelList = new List<IConditionalModel>();

                fullIds.ForEach(fullWOId =>
                {
                    wheres.ConditionalModelList.Add(new ConditionalModel()
                    {
                        ConditionalType = ConditionalType.LikeLeft,
                        FieldName = "FFULL_WORK_ORDER_ID",
                        FieldValue = string.Concat(fullWOId, "/"),
                    });
                });

                subWOIds = await db.Queryable<T_MESD_WORK_ORDER>().Where(wheres).Select(t => t.FWORK_ORDER_ID).ToListAsync();
            }
            else
            {
                subWOIds = new List<string>();
            }
            return await Task.FromResult(subWOIds);
        }


        /// <summary>
        /// 返回物料信息
        /// </summary>
        /// <param name="materialIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleMaterialModel>>> GetMaterialsAsync(List<string> materialIds)
        {
            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            return await rpcServer.GetForBusinessByIdsAsync(materialIds);
        }

        /// <summary>
        /// 返回物料自定义信息
        /// </summary>
        private async Task<DataResult<List<SimpleCustomFormModel>>> GetMaterialCustomsAsync(List<string> materialIds)
        {
            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            return await rpcServer.GetMaterialCustomByIdsAsync(materialIds);
        }


        /// <summary>
        /// 返回工序信息
        /// </summary>
        /// <param name="craftIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<HCloud.ERP.IModuleServices.MES002_Craft.Models.SimpleCraftModel>>> GetCraftsAsync(List<string> craftIds)
        {
            var rpcServer = this.GetService<IMES002CraftService>("MES002Craft");
            return await rpcServer.GetForBusinessByIdsAsync(craftIds);
        }

        /// <summary>
        /// 返回子BOM
        /// </summary>
        /// <returns></returns>
        private async Task<Dictionary<string, List<MaterialBomSubModel>>> GetSubBomsAsync(WorkOrderModel model)
        {
            ConcurrentDictionary<string, List<MaterialBomSubModel>> result = new ConcurrentDictionary<string, List<MaterialBomSubModel>>();
            if (model.NeedGenSubWO)
            {
                List<string> bomIds = model.Materials.Where(p => !string.IsNullOrWhiteSpace(p.FSUB_MATERIAL_BOM_ID))
                    .Select(p => p.FSUB_MATERIAL_BOM_ID).Distinct().ToList();
                if (bomIds.Count > 0)
                {
                    var rpcServer = this.GetService<IMSD004MaterialBOMService>("MSD004MaterialBOM");

                    foreach (var bomId in bomIds)
                    {
                        //取出子件bom（含层级下的全部子件)
                        var subResult = await rpcServer.GetSubTreeByIdAsync(bomId);
                        if (subResult.StatusCode != 200)
                        {
                            ERROR(subResult, subResult.StatusCode, subResult.Message);
                        }

                        result.TryAdd(bomId, subResult.Entity);

                    }
                    ;


                    /*
                    var tasks = bomIds.Select(async bomId =>
                     {
                         //取出子件bom（含层级下的全部子件)
                         var subResult = await rpcServer.GetSubTreeByIdAsync(bomId);
                         if (subResult.StatusCode != 200)
                         {
                             ERROR(subResult, subResult.StatusCode, subResult.Message);
                         }

                         result.TryAdd(bomId, subResult.Entity);

                     });

                    await Task.WhenAll(tasks);
                    */
                }
            }

            return await Task.FromResult(result.ToDictionary(x => x.Key, x => x.Value));
        }

        /// <summary>
        /// 返回员工姓名
        /// </summary>
        /// <param name="empIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<EmployeeModel>>> GetEmployeesAsync(List<string> empIds)
        {
            var rpcServer = this.GetService<IEmployeeService>("ADM024Employee");
            return await rpcServer.GetEmpsByIdsAsync(empIds);
        }

        /// <summary>
        /// 获取物料自定义字段
        /// </summary>
        /// <returns></returns>
        async Task<DataResult<CustomFormModel>> GetMatCustomFormAsync()
        {
            //自定义物料扩展字段
            var rpcServer = this.GetService<IEOS009CustomProgramService>("EOS009CustomProgram");
            var rpcrtn = await rpcServer.GetCustomFormAsync("MSD002_FORM");
            if (rpcrtn.StatusCode != 200)
            {
                ERROR(rpcrtn, 111111, "获取物料自定义字段失败");
            }
            return rpcrtn;
        }



        #endregion
        /// <summary>
        /// 查询条件获取工单ids列表
        /// </summary>

        public async Task<DataResult<List<string>>> GetWorkOrderIDSAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;

            //转换查询条件
            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);


            List<string> daomodel = await db.Queryable<T_MESD_WORK_ORDER>().Where(wheres)
                .Select(p => p.FWORK_ORDER_ID).ToListAsync();


            DataResult<List<string>> result = new DataResult<List<string>>()
            {
                Entity = daomodel,
                StatusCode = 200,
            };
            return await OK(result);
        }

        /// <summary>
        /// 获取需求分析数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<DataResult<List<RequireItem>>> GetRequireDataAsync(
                                                               RequireRequest request)
        {
            if (request == null || (request.ItemCode != "workorder" && request.ItemCode != "womaterialwait"))
            {
                ERROR(null, 100010, string.Format(_multiLang["无效的参数{0}, {1}值只能为{2}. "], "request", "ItemCode", "workorder/womaterialwait"));
            }
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            List<RequireItem> requires = null;

            if (request.ItemCode == "workorder")
            {
                var requires1 = await
                 db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((wo, wostatus) => new JoinQueryInfos(JoinType.Inner, wo.FWORK_ORDER_ID == wostatus.FWORK_ORDER_ID))
                 .Where((wo, wostatus) => request.MaterialIds.Contains(wo.FMATERIAL_ID)
                       && (wostatus.FIF_CLOSE == 0 || wostatus.FIF_CLOSE == 2)
                       && wostatus.FIF_CANCEL == false
                       && wo.FPRO_QTY > wostatus.FFINISH_QTY)
                 .WhereIF(!request.ContainUnApprove, (wo, wostatus) => wostatus.FCFLAG == 1)
                 .Select((wo, wostatus) => new
                 {
                     FBILL_ID = wo.FWORK_ORDER_ID,
                     FBILL_NO = wo.FWORK_ORDER_NO,
                     FMATERIAL_ID = wo.FMATERIAL_ID,

                     FREQUIRE_QTY = (wo.FPRO_QTY - wostatus.FFINISH_QTY),
                     FUNIT_PRO_CONVERT_SCALE = wo.FUNIT_PRO_CONVERT_SCALE,
                     FUNIT_ID = wo.FUNIT_ID,
                 })
                   .ToListAsync();

                requires = requires1.Select(p => new RequireItem
                {
                    FBILL_ID = p.FBILL_ID,
                    FBILL_NO = p.FBILL_NO,
                    FMATERIAL_ID = p.FMATERIAL_ID,

                    FREQUIRE_QTY = p.FREQUIRE_QTY * (p.FUNIT_PRO_CONVERT_SCALE == 0 ? 1 : p.FUNIT_PRO_CONVERT_SCALE),
                    FUNIT_ID = p.FUNIT_ID,

                }).ToList();


            }
            else if (request.ItemCode == "womaterialwait")
            {
                requires = await
                db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_MATERIAL, T_MESD_WORK_ORDER_MATERIAL_STATUS>
                ((wo, wostatus, womaterial, womaterialstatus) => new JoinQueryInfos(JoinType.Inner, wo.FWORK_ORDER_ID == wostatus.FWORK_ORDER_ID,
                    JoinType.Inner, wo.FWORK_ORDER_ID == womaterial.FWORK_ORDER_ID,
                    JoinType.Inner, womaterial.FWORK_ORDER_MATERIAL_ID == womaterialstatus.FWORK_ORDER_MATERIAL_ID))
                .Where((wo, wostatus, womaterial, womaterialstatus) => request.MaterialIds.Contains(wo.FMATERIAL_ID)
                      && (wostatus.FIF_CLOSE == 0 || wostatus.FIF_CLOSE == 2)
                      && wostatus.FIF_CANCEL == false
                      && womaterial.FUSE_QTY > womaterialstatus.FFETCH_QTY)
                .WhereIF(!request.ContainUnApprove, (wo, wostatus) => wostatus.FCFLAG == 1)
                .Select((wo, wostatus, womaterial, womaterialstatus) => new RequireItem
                {
                    FBILL_ID = wo.FWORK_ORDER_ID,
                    FBILL_NO = wo.FWORK_ORDER_NO,

                    FBILL_DETAIL_ID = womaterial.FWORK_ORDER_MATERIAL_ID,
                    FBILL_SHOW_SEQNO = womaterial.FSHOW_SEQNO,

                    FMATERIAL_ID = womaterial.FSUB_MATERIAL_ID,
                    FREQUIRE_QTY = womaterial.FUSE_QTY - womaterialstatus.FFETCH_QTY,

                    FUNIT_ID = wo.FUNIT_ID,
                })
                  .ToListAsync();
            }
            requires.AsParallel().WithDegreeOfParallelism(4).ForAll(require =>
            {
                require.FITEM_CODE = request.ItemCode;
            });

            DataResult<List<RequireItem>> dataResult = new DataResult<List<RequireItem>>
            {
                StatusCode = 200,
                Entity = requires,
            };

            return await OK(dataResult);

        }
        /// <summary>
        /// 根据销售物料Id列表获取已完工数量列表
        /// </summary>
        public async Task<DataResult<List<SaleMaterialFinishQtyModel>>> GetSaleMaterialsFinishQtyAsync(List<string> ids)
        {
            // 初始化
            var db = _isugar.DB;

            //查询订单明细的完工数
            var soDetailFinishItems = await
            db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((wo, wostatus) => new JoinQueryInfos(JoinType.Inner, wo.FWORK_ORDER_ID == wostatus.FWORK_ORDER_ID))
                .Where((wo, wostatus) => ids.Contains(wo.FSALE_ORDER_PRODUCT_ID))
                .Select((wo, wostatus) => new { wo.FSALE_ORDER_PRODUCT_ID, wostatus.FFINISH_QTY })
                .ToListAsync();

            var results = (
            soDetailFinishItems.GroupBy(p => p.FSALE_ORDER_PRODUCT_ID, (x, y) =>
            {

                SaleMaterialFinishQtyModel item = new SaleMaterialFinishQtyModel
                {
                    FSALE_ORDER_PRODUCT_ID = x,
                    FFINISH_QTY = y.Sum(x => x.FFINISH_QTY)
                };
                return item;
            })).ToList();

            /*

            var mwo = db.Queryable<T_MESD_WORK_ORDER>();
            var mwos = db.Queryable<T_MESD_WORK_ORDER_STATUS>();
            // 根据ids查询T_MESD_WORK_ORDER的FSALE_ORDER_PRODUCT_ID、FWORK_ORDER_ID数据
            var queryable = from a in mwo where ids.Contains(a.FSALE_ORDER_PRODUCT_ID) select new { a.FSALE_ORDER_PRODUCT_ID, a.FWORK_ORDER_ID };
            var lstMwo = await queryable.ToListAsync();
            // 查询T_MESD_WORK_ORDER_STATUS的FFINISH_QTY
            var lstOrderId = lstMwo.Select(a => a.FWORK_ORDER_ID).Distinct().ToList();
            var lstMwos = from s in mwos where lstOrderId.Contains(s.FWORK_ORDER_ID) select new { s.FFINISH_QTY, s.FWORK_ORDER_ID };
            // 整合查询并返回
            var lst = new List<SaleMaterialFinishQtyModel>();
            foreach (var id in ids)
            {
                var lstWorkOrderId = lstMwo.Where(a => a.FSALE_ORDER_PRODUCT_ID == id).Select(a => a.FWORK_ORDER_ID).Distinct().ToList();
                var totalQty = lstMwos.Where(a => lstWorkOrderId.Contains(a.FWORK_ORDER_ID)).Sum(a => a.FFINISH_QTY);
                lst.Add(new SaleMaterialFinishQtyModel
                {
                    FSALE_ORDER_PRODUCT_ID = id,
                    FFINISH_QTY = totalQty,
                });
            }
            */


            var result = new DataResult<List<SaleMaterialFinishQtyModel>>() { Entity = results, StatusCode = 200, };
            return await OK(result);
        }



        /// <summary>
        /// 取消排程
        /// </summary>
        public async Task<DataResult<List<WorkOrderModel>>> CancelScheduleAsync(List<string> ids)
        {
            var db = _isugar.DB;
            //查询工单
            var workOrdInfo = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID)).Where((a, b) => ids.Contains(a.FWORK_ORDER_ID)).Select((a, b) => new { a.FWORK_ORDER_NO, a.FWORK_ORDER_ID, b.FSCHEDULE_STATUS, b.FIF_CLOSE }).ToListAsync();
            //判断是否结案
            var workFclose = workOrdInfo.Where(p => p.FIF_CLOSE == 1 || p.FIF_CLOSE == 3).FirstOrDefault();
            if (workFclose != null)
            {
                ERROR(workFclose, 111111, $"生产工单 {workFclose.FWORK_ORDER_NO} 已结案");
            }
            //判断是否排程
            var noSchedule = workOrdInfo.Where(p => p.FSCHEDULE_STATUS == 0).FirstOrDefault();
            if (noSchedule != null)
            {
                ERROR(noSchedule, 111111, $"生产工单 {noSchedule.FWORK_ORDER_NO} 未排程");
            }

            //判断是否存在非取消加工 的信息
            var jobBookingInfo = await db.Queryable<IModuleServices.MES007_JobBooking.Models.T_MESD_CRAFT_JOB_BOOKING>().Where(p => ids.Contains(p.FWORK_ORDER_ID) && p.FWORK_STATUS != "cancel").FirstAsync();
            if (jobBookingInfo != null)
            {
                var workInfo = workOrdInfo.FirstOrDefault(p => p.FWORK_ORDER_ID == jobBookingInfo.FWORK_ORDER_ID);
                ERROR(jobBookingInfo, 111111, $"生产工单 {workInfo.FWORK_ORDER_NO} 存在报工数据");
            }
            //选择加工信息
            var jobBookIngIds = await db.Queryable<IModuleServices.MES007_JobBooking.Models.T_MESD_CRAFT_JOB_BOOKING>().Where(p => ids.Contains(p.FWORK_ORDER_ID)).Select(p => p.FCRAFT_JOB_BOOKING_ID).Distinct().ToListAsync();

            //选择工艺排程
            var scheduleIds = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().Where(p => ids.Contains(p.FWORK_ORDER_ID)).Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToListAsync();

            var scheduleStatu = await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_CRAFT_SCHEDULE>((a, b) => new JoinQueryInfos(JoinType.Left, a.FCRAFT_SCHEDULE_ID == b.FCRAFT_SCHEDULE_ID)).Where(a => scheduleIds.Contains(a.FCRAFT_SCHEDULE_ID) && a.FIS_OUT == 1).Select<WOCraftScheduleModel>().FirstAsync();
            if (scheduleStatu != null)
            {
                ERROR(jobBookingInfo, 111111, $"工艺排程 {scheduleStatu.FCRAFT_SCHEDULE_NO} 存在委外数据");
            }

            db.BeginTran();
            try
            {
                //更新工单状态
                await db.Updateable<T_MESD_WORK_ORDER_STATUS>().SetColumns(p => new T_MESD_WORK_ORDER_STATUS() { FSCHEDULE_STATUS = 0, }).Where(p => ids.Contains(p.FWORK_ORDER_ID)).ExecuteCommandAsync();
                //置0工单工艺已排及已下发数量
                await db.Updateable<T_MESD_WORK_ORDER_CRAFT_STATUS>().SetColumns(p => p.FPLAN_QTY == 0).SetColumns(p => p.FRELEASE_QTY == 0).Where(p => ids.Contains(p.FWORK_ORDER_ID)).ExecuteCommandAsync();


                //删除工艺排程数据
                await db.Deleteable<T_MESD_CRAFT_SCHEDULE>().Where(p => scheduleIds.Contains(p.FCRAFT_SCHEDULE_ID)).ExecuteCommandAsync();
                await db.Deleteable<T_MESD_CRAFT_SCHEDULE_STATUS>().Where(p => scheduleIds.Contains(p.FCRAFT_SCHEDULE_ID)).ExecuteCommandAsync();
                await db.Deleteable<T_MESD_CRAFT_SCHEDULE_PERSON>().Where(p => scheduleIds.Contains(p.FCRAFT_SCHEDULE_ID)).ExecuteCommandAsync();

                //删除加工信息
                if (jobBookIngIds.Count > 0)
                {
                    await db.Deleteable<IModuleServices.MES007_JobBooking.Models.T_MESD_CRAFT_JOB_BOOKING>().Where(p => jobBookIngIds.Contains(p.FCRAFT_JOB_BOOKING_ID)).ExecuteCommandAsync();
                    await db.Deleteable<T_MESD_CRAFT_JOB_BOOKING_LOG>().Where(p => jobBookIngIds.Contains(p.FCRAFT_JOB_BOOKING_ID)).ExecuteCommandAsync();
                }


                db.CommitTran();
            }
            catch (System.Exception)
            {
                db.RollbackTran();
                throw;
            }

            //取出结果返回
            QueryRequestModel queryModel = new QueryRequestModel()
            {
                WhereGroup = new QueryWhereGroupModel(),
                PageIndex = 1,
                PageSize = 99999999,
            };
            queryModel.WhereGroup.GroupType = EnumGroupType.AND;
            queryModel.WhereGroup.Items = new List<QueryWhereItemModel>();
            queryModel.WhereGroup.Items.Add(new QueryWhereItemModel()
            {
                FieldName = "a.FWORK_ORDER_ID",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.In,
                Value = string.Join(",", ids),
            });
            var queryResult = await GetAllAsync(queryModel);   //返回本次保存的工单主表
            return queryResult;

        }


        public async Task<DataResult<List<WorkOrderModel>>> GetListAsync(WorkOrderModel model)
        {

            var db = _isugar.DB;
            RefAsync<int> totalSize = new RefAsync<int>();
            var wordlist = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID))
                              //按建立日期倒序
                              .Where("EXISTS(SELECT wom.FUNIT_QTY as WOM_FUNIT_QTY,woms.FFETCH_QTY FROM T_MESD_WORK_ORDER_MATERIAL wom " +
                              "LEFT JOIN T_MESD_WORK_ORDER_MATERIAL_STATUS woms ON wom.FWORK_ORDER_MATERIAL_ID = woms.FWORK_ORDER_MATERIAL_ID " +
                              "WHERE (wom.FWORK_ORDER_ID=a.FWORK_ORDER_ID AND wom.FUSE_QTY>woms.FFETCH_QTY)) " +
                              "and b.FCFLAG=1 AND b.FIF_CLOSE in(0,2) AND b.FIF_CANCEL=0")
                              .OrderBy((a, b) => a.FCDATE, OrderByType.Desc)
                              .Select<WorkOrderModel>()
                              .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            List<string> unitId = wordlist.Select(p => p.FPRO_UNIT_ID).Distinct().ToList();
            //取出生产单位信息
            DataResult<List<SimpleUnitModel>> unitResult = await GetUnitAsync(unitId);
            //取产品名称
            List<string> materialIds = wordlist.Select(p => p.FMATERIAL_ID).Distinct().ToList();
            DataResult<List<SimpleMaterialModel>> materialResult = await GetMaterialsAsync(materialIds);
            //取销售订单(有订单子表id值)
            DataResult<List<SimpleSaleOrderMaterialModel>> soMaterialsResult = null;
            var soMaterialIds = wordlist.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_PRODUCT_ID))
                                .Select(p => p.FSALE_ORDER_PRODUCT_ID)
                                .Distinct()
                                .ToList();
            if (soMaterialIds.Count > 0)
            {
                soMaterialsResult = await GetSaleOrderMaterialsAsync(soMaterialIds);
                if (soMaterialsResult.StatusCode != 200)
                {
                    ERROR(soMaterialsResult, soMaterialsResult.StatusCode, soMaterialsResult.Message);
                }
            }
            // 取销售订单(排除订单子表id为空)，子工单: 有订单主表id，无订单子表id
            DataResult<List<SaleOrderSimpleModel>> saleOrderResult = null;
            var saleOrderIds = wordlist.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID) &&
                                string.IsNullOrWhiteSpace(p.FSALE_ORDER_PRODUCT_ID)).Select(p => p.FSALE_ORDER_ID).Distinct().ToList();
            if (saleOrderIds.Count > 0)
            {
                saleOrderResult = await GetSaleOrdersAsync(saleOrderIds);
                if (saleOrderResult.StatusCode != 200)
                {
                    ERROR(saleOrderResult, saleOrderResult.StatusCode, saleOrderResult.Message);
                }
            }

            wordlist.AsParallel().WithDegreeOfParallelism(4).ForAll(word =>
           {
               var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == word.FUNIT_ID);
               if (unit != null)
               {
                   word.FPRO_UNIT_NAME = unit.FUNIT_NAME;


               }
               var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == word.FMATERIAL_ID);
               if (material != null)
               {
                   word.FMATERIAL_CODE = material.FMATERIAL_CODE;
                   word.FMATERIAL_NAME = material.FMATERIAL_NAME;
                   word.FSPEC_DESC = material.FSPEC_DESC;
                   word.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
               }
               //订单
               if (!string.IsNullOrWhiteSpace(word.FSALE_ORDER_PRODUCT_ID))
               {
                   if (soMaterialsResult != null)
                   {
                       var so = soMaterialsResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_MATERIAL_ID == word.FSALE_ORDER_PRODUCT_ID);
                       if (so != null)
                       {
                           word.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                           word.FSOURCE_NO = so.FSALE_ORDER_NO;
                           word.FCUSTOM_PARM = so.FCUSTOM_PARM;   //定制参数
                       }
                   }
               }

               else
               {
                   if (!string.IsNullOrWhiteSpace(word.FSALE_ORDER_ID) && saleOrderResult != null)
                   {
                       var so = saleOrderResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == word.FSALE_ORDER_ID);
                       if (so != null)
                       {
                           word.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                           word.FSOURCE_NO = so.FSALE_ORDER_NO;
                       }

                   }
               }
           });

            List<WorkOrderModel> list = new List<WorkOrderModel>();
            int count = 0;
            var orderNo = model.FWORK_ORDER_NO;
            var saleNo = model.FSALE_ORDER_NO;
            var materialCode = model.FMATERIAL_CODE;
            var materialName = model.FMATERIAL_NAME;
            if (!string.IsNullOrWhiteSpace(saleNo))
            {
                list = wordlist.Where(p => p.FWORK_ORDER_NO.Contains(orderNo, StringComparison.OrdinalIgnoreCase) && p.FMATERIAL_CODE.Contains(materialCode, StringComparison.OrdinalIgnoreCase)
               && p.FMATERIAL_NAME.Contains(materialName, StringComparison.OrdinalIgnoreCase) && p.FSALE_ORDER_NO != null && p.FSALE_ORDER_NO.Contains(saleNo, StringComparison.OrdinalIgnoreCase)).ToList();
                count = list.Count;
            }
            else if (!string.IsNullOrWhiteSpace(orderNo) || !string.IsNullOrWhiteSpace(materialCode) || !string.IsNullOrWhiteSpace(materialName))
            {
                list = wordlist.Where(p => p.FWORK_ORDER_NO.Contains(orderNo, StringComparison.OrdinalIgnoreCase) && p.FMATERIAL_CODE.Contains(materialCode, StringComparison.OrdinalIgnoreCase)
                && p.FMATERIAL_NAME.Contains(materialName, StringComparison.OrdinalIgnoreCase)).ToList();
                count = list.Count;
            }

            else
            {
                list = wordlist;
                count = list.Count;
            }



            DataResult<List<WorkOrderModel>> result = new DataResult<List<WorkOrderModel>>()
            {
                StatusCode = 200,
                Entity = list,

                Pager = new PagerResult()
                {
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                    TotalRecords = totalSize,
                    PagesCount = count

                },
            };

            return await OK(result);
        }
        private async Task<DataResult<List<SimpleUnitModel>>> GetUnitAsync(List<string> FPRO_UNIT_ID)
        {
            var rpcServer = this.GetService<IMSD001UnitService>("MSD001Unit");
            return await rpcServer.GetByIdsForBusinessAsync(FPRO_UNIT_ID);
        }


        /// <summary>
        /// app 搜索生产工单
        /// </summary>

        public async Task<DataResult<List<WorkOrderModel>>> GetAppWorkOrdListAsync(WorkOrdQueryRequstModel model)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var saleOrdids = new List<string>();
            if (!string.IsNullOrEmpty(model.SaleOrdCode))
            {
                //销售订单
                var saleOrdrpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
                var saleordids = await saleOrdrpcServer.GetSaleOrdIdsByCodeAsync(model.SaleOrdCode);
                saleOrdids = saleordids.Entity;
            }

            var matIds = new List<string>();

            if (!string.IsNullOrEmpty(model.FMATERIAL_CODE) || !string.IsNullOrEmpty(model.FMATERIAL_NAME))
            {
                var simpquery = new SimpleMatQuery();
                simpquery.FMATERIAL_CODE = model.FMATERIAL_CODE;
                simpquery.FMATERIAL_NAME = model.FMATERIAL_NAME;
                //销售订单
                var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
                var matresult = await rpcServer.GetMatIdsByAsync(simpquery);
                matIds = matresult.Entity;
            }

            var wheres = WhereCollections.FromQueryRequestModel(model.WorkQRM);
            RefAsync<int> totalSize = new RefAsync<int>();


            //取出符合条件的id
            var workOrders = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID))
                               .WhereIF(matIds.Count > 0, (a, b) => matIds.Contains(a.FMATERIAL_ID)).WhereIF(saleOrdids.Count > 0, (a, b) => saleOrdids.Contains(a.FSALE_ORDER_ID))
                              .OrderBy((a, b) => a.FCDATE, OrderByType.Desc)   //按建立日期倒序
                             .Select<WorkOrderModel>()
                             .Where(wheres)
                             .ToPageListAsync(model.WorkQRM.PageIndex, model.WorkQRM.PageSize, totalSize);

            //获取销售订单
            var saleOrdIds = workOrders.Select(p => p.FSALE_ORDER_ID).ToList();
            var saleOrdInfos = await GetSaleOrdersAsync(saleOrdIds);

            //取出物料信息
            var matids = workOrders.Select(p => p.FMATERIAL_ID).ToList();
            var materialResult = await GetMaterialsAsync(matids);
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }

            var funit = workOrders.Select(p => p.FPRO_UNIT_ID).ToList();
            var fprounits = await GetUnitAsync(funit);


            workOrders.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var saleord = saleOrdInfos.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == item.FSALE_ORDER_ID);

                if (saleord != null)
                {
                    item.FSALE_ORDER_NO = saleord.FSALE_ORDER_NO;
                }

                var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                if (material != null)
                {
                    item.FMATERIAL_CODE = material.FMATERIAL_CODE;
                    item.FMATERIAL_NAME = material.FMATERIAL_NAME;
                    item.FSPEC_DESC = material.FSPEC_DESC;
                }
                var proUnit = fprounits.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FPRO_UNIT_ID);
                if (proUnit != null)
                {
                    item.FPRO_UNIT_NAME = proUnit.FUNIT_NAME;
                }
            });


            DataResult<List<WorkOrderModel>> result = new DataResult<List<WorkOrderModel>>()
            {
                StatusCode = 200,
                Entity = workOrders,
                Pager = new PagerResult()
                {
                    PageNum = model.WorkQRM.PageIndex,
                    PageSize = model.WorkQRM.PageSize,
                    TotalRecords = totalSize.Value,
                },
            };
            return await OK(result);

        }


        /// <summary>
        /// 根据工单物料ids 获取物料子表
        /// </summary>
        public async Task<DataResult<List<WorkOrderMaterialModel>>> GetWorkOrdMatsByIdsAsync(List<string> Ids)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var workMat = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => Ids.Contains(p.FWORK_ORDER_MATERIAL_ID)).Select<WorkOrderMaterialModel>().ToListAsync();

            DataResult<List<WorkOrderMaterialModel>> result = new DataResult<List<WorkOrderMaterialModel>>()
            {
                StatusCode = 200,
                Entity = workMat,
            };
            return await OK(result);
        }




        /// <summary>
        /// app  根据工单id 获取物料子表
        /// </summary>

        public async Task<DataResult<List<WorkOrderMaterialModel>>> GetWorkOrdMatsByIdAsync(string Id)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var workMat = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => p.FWORK_ORDER_ID == Id).Select<WorkOrderMaterialModel>().ToListAsync();


            //取出物料信息
            var matids = workMat.Select(p => p.FSUB_MATERIAL_ID).ToList();
            var materialResult = await GetMaterialsAsync(matids);
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }

            var funit = workMat.Select(p => p.FSUB_UNIT_ID).ToList();
            var fprounits = await GetUnitAsync(funit);


            workMat.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var matinfo = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FSUB_MATERIAL_ID);
                if (matinfo != null)
                {
                    item.FSUB_MATERIAL_CODE = matinfo.FMATERIAL_CODE;
                    item.FSUB_MATERIAL_NAME = matinfo.FMATERIAL_NAME;
                    item.FSUB_SPEC_DESC = matinfo.FSPEC_DESC;
                    item.FPIC_ATTACH_ID = matinfo.FPIC_ATTACH_ID;
                }

                var fpoUnit = fprounits.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FSUB_UNIT_ID);
                if (fpoUnit != null)
                {
                    item.FSUB_UNIT_NAME = fpoUnit.FUNIT_NAME;
                }
            });



            DataResult<List<WorkOrderMaterialModel>> result = new DataResult<List<WorkOrderMaterialModel>>()
            {
                StatusCode = 200,
                Entity = workMat,
            };
            return await OK(result);

        }



        /// <summary>
        /// app  根据工单编号 获取工单
        /// </summary>
        public async Task<DataResult<WorkOrderModel>> GetWorkOrdAndMatsByIdAsync(string workorderCode)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var workOrder = await db.Queryable<T_MESD_WORK_ORDER>().Where(p => p.FWORK_ORDER_NO == workorderCode).Select<WorkOrderModel>().FirstAsync();
            if (workOrder == null)
            {
                ERROR(workOrder, 111111, $"未找到编号 {workorderCode} 的生产工单");
            }
            //获取工单状态
            var workOrderStatus = await db.Queryable<T_MESD_WORK_ORDER_STATUS>().Where(p => p.FWORK_ORDER_ID == workOrder.FWORK_ORDER_ID).FirstAsync();
            if (workOrderStatus == null)
            {
                ERROR(workOrder, 111111, $"未找到工单 {workorderCode} 的状态信息");
            }

            if (workOrderStatus.FIF_CLOSE == 1 || workOrderStatus.FIF_CLOSE == 3)
            {
                ERROR(workOrder, 111111, $"工单 {workorderCode} 已结案");
            }
            if (workOrderStatus.FIF_CANCEL)
            {
                ERROR(workOrder, 111111, $"工单 {workorderCode} 已作废");
            }
            if (workOrderStatus.FCFLAG != 1)
            {
                ERROR(workOrder, 111111, $"工单 {workorderCode} 未审核");
            }
            //获取销售订单信息

            if (!string.IsNullOrEmpty(workOrder.FSALE_ORDER_ID))
            {
                var saleRpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
                var saleUpdata = await saleRpcServer.GetSaleOrdInfoByIdAsync(new List<string>() { workOrder.FSALE_ORDER_ID });
                if (saleUpdata.StatusCode != 200)
                {
                    ERROR(saleUpdata, 111111, "查询销售订单异常");
                }
                if (saleUpdata.Entity.Count > 0)
                {
                    workOrder.FSALE_ORDER_NO = saleUpdata.Entity[0].FSALE_ORDER_NO;
                }

            }



            var workMat = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => p.FWORK_ORDER_ID == workOrder.FWORK_ORDER_ID).Select<WorkOrderMaterialModel>().ToListAsync();


            //取出物料信息
            var matids = workMat.Select(p => p.FSUB_MATERIAL_ID).ToList();
            matids.Add(workOrder.FMATERIAL_ID);
            var materialResult = await GetMaterialsAsync(matids);
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }

            var workmat = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == workOrder.FMATERIAL_ID);
            if (workmat != null)
            {
                workOrder.FMATERIAL_CODE = workmat.FMATERIAL_CODE;
                workOrder.FMATERIAL_NAME = workmat.FMATERIAL_NAME;
                workOrder.FSPEC_DESC = workmat.FSPEC_DESC;
            }

            var funit = workMat.Select(p => p.FSUB_UNIT_ID).ToList();
            funit.Add(workOrder.FPRO_UNIT_ID);
            var fprounits = await GetUnitAsync(funit);
            var workUnit = fprounits.Entity.FirstOrDefault(p => p.FUNIT_ID == workOrder.FPRO_UNIT_ID);
            if (workUnit != null)
            {
                workOrder.FPRO_UNIT_NAME = workUnit.FUNIT_NAME;
            }
            workMat.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var matinfo = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FSUB_MATERIAL_ID);
                if (matinfo != null)
                {
                    item.FSUB_MATERIAL_CODE = matinfo.FMATERIAL_CODE;
                    item.FSUB_MATERIAL_NAME = matinfo.FMATERIAL_NAME;
                    item.FSUB_SPEC_DESC = matinfo.FSPEC_DESC;
                    item.FPIC_ATTACH_ID = matinfo.FPIC_ATTACH_ID;
                }

                var fpoUnit = fprounits.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FSUB_UNIT_ID);
                if (fpoUnit != null)
                {
                    item.FSUB_UNIT_NAME = fpoUnit.FUNIT_NAME;
                }
            });

            workOrder.Materials = workMat;


            DataResult<WorkOrderModel> result = new DataResult<WorkOrderModel>()
            {
                StatusCode = 200,
                Entity = workOrder,
            };
            return await OK(result);

        }

        /// <summary>
        /// 同步物料至金蝶
        /// </summary>
        /// <param name="Ids">需同步的ID</param>
        /// <returns></returns>
        public async Task<DataResult<List<string>>> PushKingDeeData(List<string> Ids)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;
            DataResult<List<string>> result = new DataResult<List<string>>()
            {
                Entity = Ids,
                StatusCode = 200,
            };
            //抛出同步数据任务
            this.Publish(new IModuleServices.SYS000_Common.Models.SyncData.PushDataArgs
            {
                ActionType = "save",
                DataIds = Ids,
                DataContent = "",
                DataName = "T_MESD_WORK_ORDER",
                UserAccount = user,
            });
            return await OK(result);
        }

        /// <summary>
        /// 根据工单物料ids 获取物料子表
        /// </summary>
        public async Task<DataResult<List<WorkOrderCraftModel>>> GetWorkOrdCraftByIdsAsync(List<string> Ids)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var workMat = await db.Queryable<T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER>
            ((craft, work) => new JoinQueryInfos(JoinType.Left, craft.FWORK_ORDER_ID == work.FWORK_ORDER_ID))
            .Where((craft, work) => Ids.Contains(craft.FWORK_ORDER_CRAFT_ID))
            .Select<WorkOrderCraftModel>()
            .ToListAsync();


            //取出工序信息
            var craftIds = workMat.Where(p => !string.IsNullOrWhiteSpace(p.FCRAFT_ID)).Select(p => p.FCRAFT_ID).Distinct().ToList();
            if (craftIds.Count > 0)
            {
                var craftResult = await GetCraftsAsync(craftIds);
                if (craftResult.StatusCode != 200)
                {
                    ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                }
                workMat.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                {
                    var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                    if (craft != null)
                    {
                        woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                        woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                        woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                    }
                });
            }

            DataResult<List<WorkOrderCraftModel>> result = new DataResult<List<WorkOrderCraftModel>>()
            {
                StatusCode = 200,
                Entity = workMat,
            };
            return await OK(result);
        }

        /// <summary>
        ///   根据工单id 获取物料子表
        /// </summary>

        public async Task<DataResult<List<WorkOrderMaterialModel>>> GetWorkOrdMatsByIdAsync(List<string> Ids)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;


            var workMat = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL, T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_MATERIAL_STATUS>
                ((a, b, c) => new JoinQueryInfos(JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID, JoinType.Left
                            , a.FWORK_ORDER_MATERIAL_ID == c.FWORK_ORDER_MATERIAL_ID))
                .OrderBy((a, b, c) => a.FWORK_ORDER_ID, OrderByType.Asc)
                .OrderBy((a, b, c) => a.FSHOW_SEQNO, OrderByType.Asc)
               .Where((a, b, c) => Ids.Contains(a.FWORK_ORDER_ID)).Select<WorkOrderMaterialModel>().ToListAsync();


            //取出物料信息
            var matids = workMat.Select(p => p.FSUB_MATERIAL_ID).ToList();
            matids.AddRange(workMat.Select(p => p.FMATERIAL_ID).ToList());
            var materialResult = await GetMaterialsAsync(matids);
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }

            var funit = workMat.Select(p => p.FSUB_UNIT_ID).ToList();
            var fprounits = await GetUnitAsync(funit);

            var fstkUnit = workMat.Select(p => p.FSUB_STK_UNIT_ID).ToList();
            var fstkunits = await GetUnitAsync(fstkUnit);

            //获取仓库 是否 货位管理
            var storeIds = materialResult.Entity.Select(p => p.FSTORE_ID).Distinct().ToList();
            var storeInfos = await _businessService.GetStoresAsync(storeIds);
            if (storeInfos.StatusCode != 200)
            {
                ERROR(storeInfos, storeInfos.StatusCode, storeInfos.Message);
            }


            workMat.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var matinfo = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FSUB_MATERIAL_ID);
                if (matinfo != null)
                {
                    item.FSUB_MATERIAL_CODE = matinfo.FMATERIAL_CODE;
                    item.FSUB_MATERIAL_NAME = matinfo.FMATERIAL_NAME;
                    item.FSUB_SPEC_DESC = matinfo.FSPEC_DESC;
                    item.FSUB_GOODS_MODEL = matinfo.FGOODS_MODEL;
                    item.FPIC_ATTACH_ID = matinfo.FPIC_ATTACH_ID;
                }
                var workMatInfo = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FMATERIAL_ID);
                if (workMatInfo != null)
                {
                    item.FMATERIAL_CODE = workMatInfo.FMATERIAL_CODE;
                    item.FMATERIAL_NAME = workMatInfo.FMATERIAL_NAME;
                    item.FSPEC_DESC = workMatInfo.FSPEC_DESC;
                    item.FSUB_GOODS_MODEL = workMatInfo.FGOODS_MODEL;
                    item.FSTORE_ID = workMatInfo.FSTORE_ID;
                    var storeInfo = storeInfos.Entity.FirstOrDefault(stores => workMatInfo.FSTORE_ID == stores.FSTORE_ID);
                    if (storeInfo != null)
                    {
                        item.FSTORE_NAME = storeInfo.FSTORE_NAME;
                        item.FSTORE_CODE = storeInfo.FSTORE_CODE;
                    }
                    item.FSTORE_PLACE_ID = workMatInfo.FSTORE_PLACE_ID;

                }
                var fpoUnit = fprounits.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FSUB_UNIT_ID);
                if (fpoUnit != null)
                {
                    item.FSUB_UNIT_NAME = fpoUnit.FUNIT_NAME;
                }
                var fstkUnit = fstkunits.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FSUB_STK_UNIT_ID);
                if (fstkUnit != null)
                {
                    item.FSUB_STK_UNIT_NAME = fstkUnit.FUNIT_NAME;
                }
            });


            DataResult<List<WorkOrderMaterialModel>> result = new DataResult<List<WorkOrderMaterialModel>>()
            {
                StatusCode = 200,
                Entity = workMat,
            };
            return await OK(result);

        }

        /// <summary>
        ///   根据工单id和工艺清单ID 获取物料子表和工艺
        /// </summary>

        public async Task<DataResult<List<WorkOrderMaterialModel>>> GetWorkOrdMatAndCraftByIdsAsync(List<string> OrdIds, List<string> CraftIds)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            var workMat = await db.Queryable<T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_MATERIAL>((craft, mats) =>
                new JoinQueryInfos(JoinType.Left, mats.FWORK_ORDER_ID == craft.FWORK_ORDER_ID))
                .OrderBy((craft, mats) => mats.FWORK_ORDER_ID, OrderByType.Asc)
                .OrderBy((craft, mats) => mats.FSHOW_SEQNO, OrderByType.Asc)
                .Where((craft, mats) => OrdIds.Contains(mats.FWORK_ORDER_ID) && CraftIds.Contains(craft.FWORK_ORDER_CRAFT_ID))
                .Select<WorkOrderMaterialModel>().ToListAsync();

            var workInfos = await db.Queryable<T_MESD_WORK_ORDER>().OrderBy(p => p.FWORK_ORDER_NO).Where(p => OrdIds.Contains(p.FWORK_ORDER_ID)).ToListAsync();

            //取出物料信息
            var matids = workMat.Select(p => p.FSUB_MATERIAL_ID).ToList();
            var workMatIds = workInfos.Select(p => p.FMATERIAL_ID).ToList();
            matids.AddRange(workMatIds);
            var materialResult = await GetMaterialsAsync(matids);
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }

            var funit = workMat.Select(p => p.FSUB_UNIT_ID).ToList();
            funit.AddRange(workMat.Select(p => p.FSUB_STK_UNIT_ID).ToList());
            var fprounits = await GetUnitAsync(funit);


            //取出工序信息
            var craftIds = workMat.Where(p => !string.IsNullOrWhiteSpace(p.FCRAFT_ID)).Select(p => p.FCRAFT_ID).Distinct().ToList();
            var craftResult = await GetCraftsAsync(craftIds);
            if (craftResult.StatusCode != 200)
            {
                ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
            }
            workMat.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var matinfo = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == item.FSUB_MATERIAL_ID);
                if (matinfo != null)
                {
                    item.FSUB_MATERIAL_CODE = matinfo.FMATERIAL_CODE;
                    item.FSUB_MATERIAL_NAME = matinfo.FMATERIAL_NAME;
                    item.FSUB_SPEC_DESC = matinfo.FSPEC_DESC;
                    item.FSUB_GOODS_MODEL = matinfo.FGOODS_MODEL;
                    item.FPIC_ATTACH_ID = matinfo.FPIC_ATTACH_ID;
                    item.FSTUFF_CODE = matinfo.FSTUFF_CODE;
                    item.FSTUFF_NAME = matinfo.FSTUFF_NAME;
                    item.FCOLOR_CODE = matinfo.FCOLOR_CODE;
                }
                var workInfo = workInfos.Where(p => p.FWORK_ORDER_ID == item.FWORK_ORDER_ID).FirstOrDefault();
                var workMatInfo = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == workInfo.FMATERIAL_ID);
                if (workMatInfo != null)
                {
                    item.FMATERIAL_CODE = workMatInfo.FMATERIAL_CODE;
                    item.FMATERIAL_NAME = workMatInfo.FMATERIAL_NAME;
                    item.FSPEC_DESC = workMatInfo.FSPEC_DESC;
                    item.FSUB_GOODS_MODEL = workMatInfo.FGOODS_MODEL;
                }
                var fpoUnit = fprounits.Entity.FirstOrDefault(p => p.FUNIT_ID == item.FSUB_UNIT_ID);
                if (fpoUnit != null)
                {
                    item.FSUB_UNIT_CODE = fpoUnit.FUNIT_CODE;
                    item.FSUB_UNIT_NAME = fpoUnit.FUNIT_NAME;
                }
                var unitStk = fprounits.Entity.FirstOrDefault(u => u.FUNIT_ID == item.FSUB_STK_UNIT_ID);
                if (unitStk != null)
                {
                    item.FSUB_STK_UNIT_CODE = unitStk.FUNIT_CODE;
                    item.FSUB_STK_UNIT_NAME = unitStk.FUNIT_NAME;
                }
                if (craftIds.Count > 0)
                {
                    var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == item.FCRAFT_ID);
                    if (craft != null)
                    {
                        item.FCRAFT_CODE = craft.FCRAFT_CODE;
                        item.FCRAFT_NAME = craft.FCRAFT_NAME;
                        item.FCRAFT_DESC = craft.FCRAFT_DESC;
                    }
                }
            });


            DataResult<List<WorkOrderMaterialModel>> result = new DataResult<List<WorkOrderMaterialModel>>()
            {
                StatusCode = 200,
                Entity = workMat,
            };
            return await OK(result);

        }


        /// <summary>
        /// 获取所有的生产工单===工单投料使用
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WorkOrderMaterialDetailModel>>> GetAllWorkOrderDetailAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;



            var wheres = WhereCollections.FromQueryRequestModel(model);
            RefAsync<int> totalSize = new RefAsync<int>();

            //取出符合条件的id
            var workTable = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL, T_MESD_WORK_ORDER_MATERIAL_STATUS,
                T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>(
                (mat, matStatus, work, workStatus) =>
                new JoinQueryInfos(JoinType.Inner, mat.FWORK_ORDER_MATERIAL_ID == matStatus.FWORK_ORDER_MATERIAL_ID
                                , JoinType.Inner, mat.FWORK_ORDER_ID == work.FWORK_ORDER_ID
                                , JoinType.Inner, work.FWORK_ORDER_ID == workStatus.FWORK_ORDER_ID))
                .OrderBy((mat, matStatus, work, workStatus) => work.FCDATE, OrderByType.Desc)
                .OrderBy((mat, matStatus, work, workStatus) => work.FWORK_ORDER_NO, OrderByType.Desc)
                .OrderBy((mat, matStatus, work, workStatus) => mat.FSHOW_SEQNO, OrderByType.Asc)
                .Where(wheres)
                .Select<WorkOrderMaterialDetailModel>()
                .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            var workidss = workTable.Select(p => p.FWORK_ORDER_ID).ToList();

            //获取销售订单信息
            var saleOrdIds = workTable.Select(p => p.FSALE_ORDER_ID).ToList();
            var saleRpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
            var saleUpdata = await saleRpcServer.GetSaleOrdInfoByIdAsync(saleOrdIds);
            if (saleUpdata.StatusCode != 200)
            {
                ERROR(saleUpdata, 111111, "查询销售订单异常");
            }

            var unitIds = workTable.SelectMany(p => { var ids = new List<string>() { p.FSUB_UNIT_ID, p.FSUB_STK_UNIT_ID }; return ids; }).ToList();
            var units = await _businessService.GetUnitNameByIdsAsync(unitIds);
            if (units.StatusCode != 200)
            {
                ERROR(units, 111111, $"获取计量单位失败");
            }

            //获取投料数据
            var workFetchIds = workTable.Select(p => p.FWORK_ORDER_MATERIAL_ID).ToList();
            var workFetchRpcServer = this.GetService<IMES004WorkFetchService>("MES004WorkFetch");
            var workFetchData = await db.Queryable<T_MESD_WORK_FETCH_MATERIAL, T_MESD_WORK_FETCH, T_MESD_WORK_FETCH_STATUS>
                ((mat, wfetch, status) => new JoinQueryInfos(JoinType.Left, mat.FWORK_FETCH_ID == wfetch.FWORK_FETCH_ID,
                                        JoinType.Left, wfetch.FWORK_FETCH_ID == status.FWORK_FETCH_ID))
                .OrderBy((mat, wfetch, status) => wfetch.FCDATE, OrderByType.Desc)
                .Where((mat, wfetch, status) => wfetch.FBUS_TYPE == "WO03" && workFetchIds.Contains(mat.FSOURCE_BILL_DETAIL_ID))
                .Select<FetchMaterialStoreDetailModel>()
                .ToListAsync();


            var craftids = workTable.Select(p => p.FWORK_ORDER_CRAFT_ID).ToList();
            var ordercraft = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>().Where(p => craftids.Contains(p.FWORK_ORDER_CRAFT_ID)).ToListAsync();

            var orderbumid = workTable.Select(p => p.FMATERIAL_ID).ToList();
            var materialIds = workTable.Select(p => p.FSUB_MATERIAL_ID).ToList();
            materialIds.AddRange(orderbumid);

            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            var updata = await rpcServer.GetMaterialUpAsync(materialIds);
            if (updata.StatusCode != 200)
            {
                ERROR(updata, 111111, "查询材料单价异常");
            }
            DataResult<List<SimpleMaterialModel>> materialResult = await GetMaterialsAsync(materialIds);
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }
            //获取仓库 是否 货位管理
            var storeIds = materialResult.Entity.Select(p => p.FSTORE_ID).Distinct().ToList();
            var storeInfos = await _businessService.GetStoresAsync(storeIds);
            if (storeInfos.StatusCode != 200)
            {
                ERROR(storeInfos, storeInfos.StatusCode, storeInfos.Message);
            }

            workTable.AsParallel().WithDegreeOfParallelism(4).ForAll(p =>
            {
                var unit = units.Entity.FirstOrDefault(u => u.FUNIT_ID == p.FSUB_UNIT_ID);
                if (unit != null)
                {
                    p.FSUB_UNIT_CODE = unit.FUNIT_CODE;
                    p.FSUB_UNIT_NAME = unit.FUNIT_NAME;
                }
                var unitStk = units.Entity.FirstOrDefault(u => u.FUNIT_ID == p.FSUB_STK_UNIT_ID);
                if (unitStk != null)
                {
                    p.FSUB_STK_UNIT_CODE = unitStk.FUNIT_CODE;
                    p.FSUB_STK_UNIT_NAME = unitStk.FUNIT_NAME;
                }
                var materialup = updata.Entity.FirstOrDefault(m => m.FMATERIAL_ID == p.FSUB_MATERIAL_ID);
                if (materialup != null)
                {
                    p.FREFER_COST_UP = materialup.FREFER_COST_UP;
                    p.FMOVE_AVG_UP = materialup.FMOVE_AVG_UP;
                }
                var material = materialResult.Entity.FirstOrDefault(m => m.FMATERIAL_ID == p.FSUB_MATERIAL_ID);
                if (material != null)
                {
                    p.FSUB_MATERIAL_CODE = material.FMATERIAL_CODE;
                    p.FSUB_MATERIAL_NAME = material.FMATERIAL_NAME;
                    p.FSUB_SPEC_DESC = material.FSPEC_DESC;
                    p.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                    p.FSUB_GOODS_MODEL = material.FGOODS_MODEL;
                    p.FSTORE_ID = material.FSTORE_ID;
                    var storeInfo = storeInfos.Entity.FirstOrDefault(stores => material.FSTORE_ID == stores.FSTORE_ID);
                    if (storeInfo != null)
                    {
                        p.FSTORE_NAME = storeInfo.FSTORE_NAME;
                        p.FSTORE_CODE = storeInfo.FSTORE_CODE;
                    }
                    p.FSTORE_PLACE_ID = material.FSTORE_PLACE_ID;
                    if (!string.IsNullOrEmpty(p.FSTORE_ID))
                    {
                        var store = storeInfos.Entity.FirstOrDefault(store => store.FSTORE_ID == p.FSTORE_ID);
                        if (store != null)
                        {
                            p.FIF_ENABLE_PLACE = store.FIF_ENABLE_PLACE;
                        }
                    }
                }
                var ordermapInfo = workTable.FirstOrDefault(m => m.FWORK_ORDER_ID == p.FWORK_ORDER_ID);
                var materialOrd = materialResult.Entity.FirstOrDefault(m => m.FMATERIAL_ID == ordermapInfo.FMATERIAL_ID);
                if (materialOrd != null)
                {
                    p.FMATERIAL_CODE = materialOrd.FMATERIAL_CODE;
                    p.FMATERIAL_NAME = materialOrd.FMATERIAL_NAME;
                    p.FSPEC_DESC = materialOrd.FSPEC_DESC;
                    p.FGOODS_MODEL = materialOrd.FGOODS_MODEL;
                }
                var craftdata = ordercraft.FirstOrDefault(c => c.FWORK_ORDER_CRAFT_ID == p.FWORK_ORDER_CRAFT_ID);
                if (craftdata != null)
                {
                    p.FSTATION_ID = craftdata.FSTATION_ID;
                }
                var saleOrd = saleUpdata.Entity.FirstOrDefault(s => s.FSALE_ORDER_ID == p.FSALE_ORDER_ID);
                if (saleOrd != null)
                {
                    p.FSALE_ORDER_NO = saleOrd.FSALE_ORDER_NO;
                }
                var workFetchQty = workFetchData.Where(a => a.FSOURCE_BILL_DETAIL_ID == p.FWORK_ORDER_MATERIAL_ID).Sum(a => a.FSTK_UNIT_QTY);
                p.FFETCH_FEED_QTY = workFetchQty;
            });

            DataResult<List<WorkOrderMaterialDetailModel>> result = new DataResult<List<WorkOrderMaterialDetailModel>>()
            {
                StatusCode = 200,
                Entity = workTable,
                Pager = new PagerResult()
                {
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                    TotalRecords = totalSize.Value,
                },
            };
            return await OK(result);

        }

        /// <summary>
        /// 获取工单参数
        /// </summary>
        /// <param name="OrdId"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WorkOrderCrfatParams>>> GetWorkOrdCraftParamByIdsAsync(string OrdId)
        {

            var db = _isugar.DB;

            var daomodel = (await db.Queryable<T_MESD_WORK_ORDER_CRAFT_PARAM>()
                .Where(p => OrdId == p.FWORK_ORDER_ID).
                Select<WorkOrderCrfatParams>()
                .ToListAsync()).OrderBy(p => p.FSHOW_SEQNO).ToList();

            DataResult<List<WorkOrderCrfatParams>> result = new DataResult<List<WorkOrderCrfatParams>>()
            {
                StatusCode = 200,
                Entity = daomodel,
            };
            return await OK(result);
        }




        /// <summary>
        /// 获取当前工单物料清单下 消耗品分类的物料
        /// </summary>
        /// <param name="woId"></param>
        /// <returns></returns>
        public async Task<DataResult<SimpleMatZKModel>> GetWoMatConsumablesByWoId(string woId)
        {
            var db = _isugar.DB;

            var woMatIds = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>()
                .Where(p => p.FWORK_ORDER_ID == woId)
                .Select(p => p.FSUB_MATERIAL_ID).ToListAsync();


            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            var mats = await rpcServer.GetMatByID(woMatIds);
            if (mats.StatusCode != 200)
            {
                ERROR(mats, 111111, "获取物料异常");
            }

            var matCateConsumables = await GetSysParamValue(_MSD_MatCateConsumables);
            DataResult<SimpleMatZKModel> result = new DataResult<SimpleMatZKModel>()
            {
                StatusCode = 200,
                Entity = new SimpleMatZKModel()
            };
            if (string.IsNullOrWhiteSpace(matCateConsumables))
            {
                return result;
            }
            else
            {
                var mat = mats.Entity.Where(p => p.FMATERIAL_CATE_CODE == matCateConsumables).FirstOrDefault();

                if (mat != null)
                {
                    result.Entity = mat;
                }

            }


            return await OK(result);
        }

        /// <summary>
        /// 获取系统参数值
        /// </summary>
        /// <returns></returns>
        private async Task<string> GetSysParamValue(string parmCode)
        {

            EntParmModel parmModel = new EntParmModel
            {
                ProgName = ContextUtils.PageId,
                ServiceName = ContextUtils.ServiceKey,
                SysParmCode = parmCode,
            };
            return await _commonDataProvider.GetEntParmValueAsync(parmModel);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<List<WoOrderXFQCCheckModel>>> GetXFWoMatCheckAllAsync(WorkQueryRequstModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            //物料
            if (model.MaterialQRM != null)
            {
                //查询物料ids
                var matrpcServer = this.GetService<IModuleServices.MSD002_Material.IMSD002MaterialService>("MSD002Material");
                var matids = await matrpcServer.GetMaterialIDSAsync(model.MaterialQRM);
                if (matids.StatusCode != 200)
                {
                    ERROR(matids, 111111, "根据条件查询物料ID失败");
                }
                if (matids.Entity.Count > 0)
                {
                    if (model.WhereGroup.Groups == null)
                    {
                        model.WhereGroup.Groups = new List<QueryWhereGroupModel>();
                    }
                    if (model.WhereGroup.Groups.Count > 0)
                    {
                        // 如果Items有值时则加入否则创建新的Items
                        if (model.WhereGroup.Groups[0].Items != null && model.WhereGroup.Groups[0].Items.Count > 0)
                        {
                            model.WhereGroup.Groups[0].Items.Add(new QueryWhereItemModel()
                            {
                                FieldName = "mt.FMATERIAL_ID",
                                OperatorType = EnumQuerySymbol.In,
                                Value = string.Join(',', matids.Entity)
                            });
                        }
                        else
                        {
                            model.WhereGroup.Groups[0].Items = new List<QueryWhereItemModel>()
                            {
                                new QueryWhereItemModel()
                                {
                                    FieldName = "mt.FMATERIAL_ID",
                                    OperatorType = EnumQuerySymbol.In,
                                    Value = string.Join(',', matids.Entity)
                                }
                            };
                        }
                    }

                }
            }


            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);
            var db = _isugar.DB;
            RefAsync<int> totalCount = new RefAsync<int>();

            List<WoOrderXFQCCheckModel> workOrders = null;
            DataResult<List<SaleMatCheckModel>> globalXfqcCheckResult = null; // 全局质检数据，避免重复获取

            // 检查SOXFCheck参数，根据维护状态进行筛选
            if (!string.IsNullOrEmpty(model.SOXFCheck))
            {
                if (model.SOXFCheck == "finished" || model.SOXFCheck == "wait")
                {
                    // 先获取所有质检数据（数据量较少，性能更好）
                    globalXfqcCheckResult = await GetCheckByWoIdsAsync(new List<string>());
                    if (globalXfqcCheckResult.StatusCode != 200)
                    {
                        ERROR(globalXfqcCheckResult, globalXfqcCheckResult.StatusCode, globalXfqcCheckResult.Message);
                    }

                    List<string> targetWorkOrderIds = new List<string>();

                    if (model.SOXFCheck == "finished")
                    {
                        // 获取已维护的工单ID列表（存在质检记录）
                        targetWorkOrderIds = globalXfqcCheckResult?.Entity?.Select(x => x.FSOURCE_ID).Distinct().ToList() ?? new List<string>();
                    }
                    else if (model.SOXFCheck == "wait")
                    {
                        // 对于未维护的工单，需要先查询所有符合条件的工单ID，然后排除已维护的
                        var allWorkOrderIds = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>
                            ((mt, mts) => new JoinQueryInfos(JoinType.Left, mt.FWORK_ORDER_ID == mts.FWORK_ORDER_ID))
                            .Where((mt, mts) =>
                                        mts.FCFLAG == 1 &&
                                        mts.FIF_CANCEL == false)
                            .Select((mt, mts) => mt.FWORK_ORDER_ID)
                            .Where(wheres)
                            .ToListAsync();

                        var maintainedOrderIds = globalXfqcCheckResult?.Entity?.Select(x => x.FSOURCE_ID).ToHashSet() ?? new HashSet<string>();
                        targetWorkOrderIds = allWorkOrderIds.Where(id => !maintainedOrderIds.Contains(id)).ToList();
                    }

                    if (targetWorkOrderIds.Count > 0)
                    {
                        // 根据筛选出的工单ID查询工单详情
                        var filteredWorkOrders = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>
                            ((mt, mts) => new JoinQueryInfos(JoinType.Left, mt.FWORK_ORDER_ID == mts.FWORK_ORDER_ID))
                            .Where((mt, mts) =>
                                        mts.FCFLAG == 1 &&
                                        mts.FIF_CANCEL == false &&
                                        targetWorkOrderIds.Contains(mt.FWORK_ORDER_ID))
                            .OrderBy((mt, mts) => mt.FCDATE, OrderByType.Desc)  //按建立日期倒序
                            .Select<WoOrderXFQCCheckModel>()
                            .ToListAsync();

                        // 手动实现分页
                        totalCount.Value = filteredWorkOrders.Count;
                        var skipCount = (model.PageIndex - 1) * model.PageSize;
                        workOrders = filteredWorkOrders.Skip(skipCount).Take(model.PageSize).ToList();
                    }
                    else
                    {
                        workOrders = new List<WoOrderXFQCCheckModel>();
                        totalCount.Value = 0;
                    }
                }
                else
                {
                    // SOXFCheck参数值无效，返回所有工单
                    workOrders = await GetAllWorkOrdersWithPagingAsync(db, wheres, model.PageIndex, model.PageSize, totalCount);
                }
            }
            else
            {
                // SOXFCheck参数不存在，保持原有逻辑
                workOrders = await GetAllWorkOrdersWithPagingAsync(db, wheres, model.PageIndex, model.PageSize, totalCount);
            }

            if (workOrders.Count > 0)
            {
                //获取物料信息
                var materialIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FMATERIAL_ID)).Select(p => p.FMATERIAL_ID).Distinct().ToList();
                DataResult<List<SimpleMaterialModel>> materialResult = null;
                if (materialIds.Count > 0)
                {
                    materialResult = await GetMaterialsAsync(materialIds);
                    if (materialResult.StatusCode != 200)
                    {
                        ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                    }
                }

                //获取销售订单信息
                var saleOrderIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FSALE_ORDER_ID)).Select(p => p.FSALE_ORDER_ID).Distinct().ToList();
                DataResult<List<SaleOrderSimpleModel>> saleOrderResult = null;
                if (saleOrderIds.Count > 0)
                {
                    saleOrderResult = await GetSaleOrdersAsync(saleOrderIds);
                    if (saleOrderResult.StatusCode != 200)
                    {
                        ERROR(saleOrderResult, saleOrderResult.StatusCode, saleOrderResult.Message);
                    }
                }

                // 获取质检表数据（如果之前没有获取过）
                var ordIds = workOrders.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();
                DataResult<List<SaleMatCheckModel>> xfqcCheckResult = null;
                if (ordIds.Count > 0)
                {
                    // 如果没有使用SOXFCheck筛选，或者SOXFCheck参数无效，则需要重新获取质检数据
                    if (string.IsNullOrEmpty(model.SOXFCheck) ||
                        (model.SOXFCheck != "finished" && model.SOXFCheck != "wait"))
                    {
                        xfqcCheckResult = await GetCheckByWoIdsAsync(ordIds);
                        if (xfqcCheckResult.StatusCode != 200)
                        {
                            ERROR(xfqcCheckResult, xfqcCheckResult.StatusCode, xfqcCheckResult.Message);
                        }
                    }
                    else
                    {
                        // 如果使用了SOXFCheck筛选，则从之前获取的全量质检数据中筛选当前页工单的数据
                        // 避免重复的RPC调用，提高性能
                        xfqcCheckResult = new DataResult<List<SaleMatCheckModel>>
                        {
                            StatusCode = 200,
                            Entity = globalXfqcCheckResult?.Entity?.Where(x => ordIds.Contains(x.FSOURCE_ID)).ToList() ?? new List<SaleMatCheckModel>()
                        };
                    }
                }


                //获取员工信息
                var empIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                DataResult<List<EmployeeModel>> empResult = null;
                if (empIds.Count > 0)
                {
                    empResult = await GetEmployeesAsync(empIds);
                    if (empResult.StatusCode != 200)
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }

                //获取部门信息
                var deptIds = workOrders.Where(p => !string.IsNullOrWhiteSpace(p.FDEPT_ID)).Select(p => p.FDEPT_ID).Distinct().ToList();
                DataResult<List<MakerModel>> deptResult = null;
                if (deptIds.Count > 0)
                {
                    deptResult = await _businessService.GetDeptByIdsAsync(deptIds);
                    if (deptResult.StatusCode != 200)
                    {
                        ERROR(deptResult, deptResult.StatusCode, deptResult.Message);
                    }
                }



                workOrders.AsParallel().WithDegreeOfParallelism(4).ForAll(workOrder =>
                {
                    //填充物料信息
                    if (materialResult != null)
                    {
                        var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == workOrder.FMATERIAL_ID);
                        if (material != null)
                        {
                            workOrder.FMATERIAL_CODE = material.FMATERIAL_CODE;
                            workOrder.FMATERIAL_NAME = material.FMATERIAL_NAME;
                            workOrder.FSPEC_DESC = material.FSPEC_DESC;
                            workOrder.FGOODS_MODEL = material.FGOODS_MODEL;
                        }
                    }



                    if (!string.IsNullOrWhiteSpace(workOrder.FSALE_ORDER_ID) && saleOrderResult != null)
                    {
                        var so = saleOrderResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == workOrder.FSALE_ORDER_ID);
                        if (so != null)
                        {
                            workOrder.FSALE_ORDER_NO = so.FSALE_ORDER_NO;
                            workOrder.FCUST_CODE = so.FCUST_CODE;
                            workOrder.FCUST_NAME = so.FCUST_NAME;
                        }
                    }

                    //填充计划员信息
                    if (!string.IsNullOrWhiteSpace(workOrder.FPLAN_EMP_ID) && empResult != null)
                    {
                        var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == workOrder.FPLAN_EMP_ID);
                        if (emp != null)
                        {
                            workOrder.FPLAN_EMP_CODE = emp.FEMP_CODE;
                            workOrder.FPLAN_EMP_NAME = emp.FEMP_NAME;
                        }
                    }

                    //填充部门信息
                    if (!string.IsNullOrWhiteSpace(workOrder.FDEPT_ID) && deptResult != null)
                    {
                        var dept = deptResult.Entity.FirstOrDefault(p => p.FMAKER_ID == workOrder.FDEPT_ID);
                        if (dept != null)
                        {
                            workOrder.FDEPT_NAME = dept.FMAKER_NAME;
                        }
                    }

                    //填充质检表信息
                    if (!string.IsNullOrWhiteSpace(workOrder.FWORK_ORDER_ID) && xfqcCheckResult != null)
                    {
                        var xfqcCheck = xfqcCheckResult.Entity.FirstOrDefault(p => p.FSOURCE_ID == workOrder.FWORK_ORDER_ID);

                        if (xfqcCheck != null)
                        {
                            // 存在则给入 wait-未维护, finished-已维护
                            workOrder.FXF_CHECK_DATA_STATUS = "finished";
                        }
                        else
                        {
                            workOrder.FXF_CHECK_DATA_STATUS = "wait";
                        }
                    }

                });
            }

            DataResult<List<WoOrderXFQCCheckModel>> dataResult = new DataResult<List<WoOrderXFQCCheckModel>>()
            {
                Entity = workOrders,
                StatusCode = 200,
                Pager = new PagerResult()
                {
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                    TotalRecords = totalCount.Value,
                },
            };

            return await OK(dataResult);
        }







        /// <summary>
        /// 获取所有工单（带分页）
        /// </summary>
        /// <param name="db">数据库上下文</param>
        /// <param name="wheres">查询条件</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="totalCount">总记录数</param>
        /// <returns></returns>
        private async Task<List<WoOrderXFQCCheckModel>> GetAllWorkOrdersWithPagingAsync(
            ISqlSugarClient db,
            WhereCollections wheres,
            int pageIndex,
            int pageSize,
            RefAsync<int> totalCount)
        {
            return await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>
                ((mt, mts) => new JoinQueryInfos(JoinType.Left, mt.FWORK_ORDER_ID == mts.FWORK_ORDER_ID))
                .Where((mt, mts) =>
                            mts.FCFLAG == 1 &&
                            mts.FIF_CANCEL == false)
                .OrderBy((mt, mts) => mt.FCDATE, OrderByType.Desc)  //按建立日期倒序
                .Where(wheres)
                .Select<WoOrderXFQCCheckModel>()
                .ToPageListAsync(pageIndex, pageSize, totalCount);
        }

    }
}
