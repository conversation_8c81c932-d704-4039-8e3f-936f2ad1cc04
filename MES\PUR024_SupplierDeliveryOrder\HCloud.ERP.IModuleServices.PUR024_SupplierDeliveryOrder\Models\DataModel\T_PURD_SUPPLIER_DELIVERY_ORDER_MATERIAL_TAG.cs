﻿using HCloud.Core.HCPlatform.Sugar;
using HCloud.Core.ProxyGenerator.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HCloud.ERP.IModuleServices.PUR024_SupplierDeliveryOrder.Models.DataModel
{
    /// <summary>
    /// 加工送货单物料标签表(供应商)
    /// </summary>
    [SugarTable("T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL_TAG")]
    public class T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL_TAG : MstDBEntityBase
    {
        /// <summary>
        /// id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string FSUPPLIER_DELIVERY_ORDER_MATERIAL_TAG_ID { get; set; }

        /// <summary>
        /// 加工任务id
        /// </summary>
        public string FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID { get; set; }
        public string FSUPPLIER_DELIVERY_ORDER_ID { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string FMATERIAL_ID { get; set; }

        /// <summary>
        /// 产品数量
        /// </summary>
        public decimal? FPRODUCT_NUM { get; set; }

        /// <summary>
        /// 重量
        /// </summary>
        public decimal? FPRODUCT_WEIGHT { get; set; }

        /// <summary>
        /// 条码号 实际生成的完整条码编号
        /// </summary>
        public string FBARCODE_NO { get; set; }



        /// <summary>
        /// 接头
        /// </summary>
        public string FTEXT1
        {
            get;
            set;
        }
        /// <summary>
        /// 模号
        /// </summary>
        public string FTEXT2
        {
            get;
            set;
        }
        /// <summary>
        /// 班别
        /// </summary>
        public string FTEXT3
        {
            get;
            set;
        }
        /// <summary>
        /// 盘类
        /// </summary>
        public string FTEXT4
        {
            get;
            set;
        }
        /// <summary>
        /// 素材批号
        /// </summary>
        public string FTEXT5
        {
            get;
            set;
        }
        /// <summary>
        /// 作业员
        /// </summary>
        public string FTEXT6
        {
            get;
            set;
        }
        /// <summary>
        /// 检验员
        /// </summary>
        public string FTEXT7
        {
            get;
            set;
        }
        /// <summary>
        /// 机台号
        /// </summary>
        public string FTEXT8
        {
            get;
            set;
        }
        /// <summary>
        /// 电镀批号
        /// </summary>
        public string FTEXT9
        {
            get;
            set;
        }
        /// <summary>
        /// FSHOW_SEQNO字段
        /// </summary>
        public string FSHOW_SEQNO { get; set; }

    }
}