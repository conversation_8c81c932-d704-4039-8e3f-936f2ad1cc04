﻿using System;
using System.Collections.Generic;

namespace HCloud.ERP.IModuleServices.MES007_JobBooking.Models
{
    /// <summary>
    /// 报工操作提交的数据对象
    /// </summary>
    [MessagePack.MessagePackObject]
    public class JobBookingOperateModel
    {
        /// <summary>
        /// 执行操作
        /// </summary>
        public OperateType OperateType { get; set; }

        /// <summary>
        /// 排程任务Id
        /// </summary>
        public string FCRAFT_SCHEDULE_ID
        {
            get; set;
        }

        /// <summary>
        /// 加工任务id
        /// </summary>
        public string FCRAFT_JOB_BOOKING_ID
        {
            get; set;
        }       

        /// <summary>
        /// 本次合格数量
        /// </summary>
        public decimal FPASS_QTY
        {
            get; set;
        }

        /// <summary>
        /// 本次不良数量
        /// </summary>
        public decimal FNG_QTY
        {
            get; set;
        }


        /// <summary>
        /// 原工位Id
        /// </summary>
        public string FOLD_STATION_ID
        {
            get; set;
        }

        /// <summary>
        /// 班别
        /// </summary>
        public string F_CUSTOM_FIELD2
        {
            get; set;
        }

        /// <summary>
        /// 字码/模号
        /// </summary>
        public string F_CUSTOM_FIELD3
        {
            get; set;
        }

        /// <summary>
        /// 流水号
        /// </summary>
        public string F_CUSTOM_FIELD4
        {
            get; set;
        }


        /// <summary>
        /// 工位Id
        /// </summary>
        public string FSTATION_ID
        {
            get; set;
        }


        /// <summary>
        /// 本次合格重量
        /// </summary>
        public decimal FPASS_WEIGHT
        {
            get; set;
        }

        /// <summary>
        /// 本次不良重量
        /// </summary>
        public decimal FNG_WEIGHT
        {
            get; set;
        }
        /// <summary>
        /// 不良原因ID
        /// </summary>
        public string FBAD_REASON_ID
        {
            get;
            set;
        }
        /// <summary>
        /// 检验人员工ID
        /// </summary>
        public string FCHECK_PERSON_ID
        {
            get;
            set;
        }
        #region 附加属性
        /// <summary>
        /// 不良原因名称
        /// </summary>
        public string FBAD_REASON_NAME
        {
            get;
            set;
        }
        /// <summary>
        /// 检验人员工名称
        /// </summary>
        public string FCHECK_PERSON_NAME
        {
            get;
            set;
        }
        #endregion

        /// <summary>
        /// 是否远程调用
        /// </summary>
        public bool FEXTERNAL_QUOTE
        {
            get;
            set;
        }

        /// <summary>
        /// 报工人id
        /// </summary>
        public string FEMP_ID { get; set; }
        /// <summary>
        /// 报工人名称
        /// </summary>
        public string FEMP_NAME { get; set; }


        ///<summary>
        ///排版数
        ///<summary>
        public int? FLAYOUT_NUM
        {
            get;
            set;
        }
        ///<summary>
        ///张数
        ///<summary>
        public int? FSHEET_NUM
        {
            get;
            set;
        }
        ///<summary>
        ///合格张数
        ///<summary>
        public int? FPASS_SHEET_NUM
        {
            get;
            set;
        }
        ///<summary>
        ///不合格张数
        ///<summary>
        public int? FNG_SHEET_NUM
        {
            get;
            set;
        }

        /// <summary>
        /// 完工质检不良数据
        /// </summary>
        public List<CraftJobBookingQcBadModel> BadList { get; set; }


        /// <summary>
        /// 工艺加工质检任务主表id
        /// </summary>
        public string FCRAFT_JOB_BOOKING_QC_MT_ID
        {
            get;
            set;
        }
        /// <summary>
        /// 工艺加工质检任务主表单号
        /// </summary>
        public string FCRAFT_JOB_BOOKING_QC_MT_NO
        {
            get;
            set;
        }


        /// <summary>
        /// 检验单类型 内定数据字典:QMSChkBillType: mr-工序检验单; patrol-巡检单; stir-抽检单; first-首检单; full-全检单;
        /// </summary>
        public string FCHK_TYPE
        {
            get;
            set;
        }

        /// <summary>
        /// 建立人ID
        /// </summary>
        public string FCREATOR_ID
        {
            get;
            set;
        }
        /// <summary>
        /// 建立人
        /// </summary>
        public string FCREATOR
        {
            get;
            set;
        }
        /// <summary>
        /// 建立日期
        /// </summary>
        public DateTime? FCDATE
        {
            get;
            set;
        }


        /// <summary>
        /// 订单id
        /// </summary>
        public string FSALE_ORDER_ID
        {
            get;
            set;
        }
        /// <summary>
        /// 工单id
        /// </summary>
        public string FWORK_ORDER_ID
        {
            get;
            set;
        }


        /// <summary>
        /// 工艺id
        /// </summary>
        public string FCRAFT_ID
        {
            get;
            set;
        }
        /// <summary>
        /// 产品id
        /// </summary>
        public string FMATERIAL_ID
        {
            get;
            set;
        }
      /// <summary>
      /// 排程号
      /// </summary>
        public string FCRAFT_SCHEDULE_NO { get; set; }
        /// <summary>
        /// 产品代号
        /// </summary>
        public string FMATERIAL_CODE
        {
            get;
            set;
        }

        /// <summary>
        /// 产品id
        /// </summary>
        public List<TagItem> tagItems { get; set; }


        /// <summary>
        /// 是否在完工时拆分批次并流转到下一工序
        /// </summary>
        public bool FSPLIT_AND_FLOW { get; set; } = false;

        /// <summary>
        /// 拆分模式：
        /// - "use_pass_qty": 使用本次良品数量作为拆分数量（默认，推荐）
        /// - "specify_qty": 使用指定的拆分数量
        /// - "total_finished": 拆分所有已完工数量（包含历史完工）
        /// </summary>
        public string FSPLIT_MODE { get; set; } = "use_pass_qty";

        /// <summary>
        /// 指定拆分数量 - 仅当 FSPLIT_MODE = "specify_qty" 时有效
        /// 如果不指定或为0，则根据拆分模式自动计算
        /// </summary>
        public decimal FSPLIT_FINISH_QTY { get; set; } = 0;

        /// <summary>
        /// 是否强制拆分 - 即使数量不匹配也执行拆分
        /// </summary>
        public bool FFORCE_SPLIT { get; set; } = false;
    }

    public class TagItem {

        public string FCRAFT_JOB_BOOKING_TAG_ID
        {
            get; set;
        }
        /// <summary>
        /// 报工记录id
        /// </summary>
        public string FCRAFT_JOB_BOOKING_ID
        {
            get; set;
        }
        /// <summary>
        /// 任务Id
        /// </summary>
        public string FCRAFT_SCHEDULE_ID
        {
            get; set;
        }

        /// <summary>
        ///数量
        /// </summary>
        public decimal? FPRODUCT_NUM { get; set; }
        /// <summary>
        /// 重量
        /// </summary>
        public decimal? FPRODUCT_WEIGHT { get; set; }
        /// <summary>
        /// 毛重
        /// </summary>
        public decimal? FGROSS_WEIGHT { get; set; }
        /// <summary>
        /// 材料批号
        /// </summary>
        public string FSTUFFLOT_NO
        {
            get; set;
        }
        /// <summary>
        /// 条码号
        /// </summary>
        public string FBARCODE_NO { get; set; }
        public string FTEXT1 { get; set; }
        public string FTEXT2 { get; set; }
        public string FTEXT3 { get; set; }
        public string FTEXT4 { get; set; }
        public string FTEXT5 { get; set; }
        public string FSHOW_SEQNO { get; set; }

    }
}
