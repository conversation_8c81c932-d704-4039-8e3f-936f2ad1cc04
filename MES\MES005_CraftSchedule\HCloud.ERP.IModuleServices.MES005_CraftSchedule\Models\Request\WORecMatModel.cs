﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models.Request
{
    /// <summary>
    /// 工单领料 领料清单
    /// </summary>
    [MessagePack.MessagePackObject()]
    public class WORecMatModel
    {
        /// <summary>
        /// 物料ID
        /// </summary>
        public string FSUB_MATERIAL_ID { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        public string FSUB_MATERIAL_CODE { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string FSUB_MATERIAL_NAME { get; set; }

        /// <summary>
        /// 规格描述
        /// </summary>
        public string FSUB_SPEC_DESC { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string FSUB_UNIT_ID { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string FSUB_UNIT_CODE { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string FSUB_UNIT_NAME { get; set; }

        /// <summary>
        /// 单位用量
        /// </summary>
        public decimal FUNIT_QTY { get; set; }

        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal FLOSS_RATE { get; set; }

        /// <summary>
        /// 总用量
        /// </summary>
        public decimal FUSE_QTY { get; set; }

        /// <summary>
        /// 已领用数量
        /// </summary>
        public decimal FFETCH_QTY { get; set; }

        /// <summary>
        /// 可领用数量
        /// </summary>
        public decimal FCAN_FETCH_QTY  { get; set; }

        /// <summary>
        /// 库存单位
        /// </summary>
        public string FSUB_STK_UNIT_ID { get; set; }
        /// <summary>
        /// 库存单位
        /// </summary>
        public string FSUB_STK_UNIT_CODE { get; set; }
        /// <summary>
        /// 库存单位
        /// </summary>
        public string FSUB_STK_UNIT_NAME { get; set; }

        /// <summary>
        /// 库存单位总用量
        /// </summary>
        public decimal FSTK_USE_QTY
        {
            get;
            set;
        }
        /// <summary>
        /// 库存单位单位用量
        /// </summary>
        public decimal FSTK_UNIT_QTY
        {
            get;
            set;
        }
        /// <summary>
        /// 库存单位换算比率
        /// </summary>
        public decimal FUNIT_STK_CONVERT_SCALE
        {
            get;
            set;
        }

        /// <summary>
        /// 生产工单工艺清单Id
        /// </summary>
        public string FWORK_ORDER_CRAFT_ID
        {
            get;
            set;
        }

        /// <summary>
        /// 工单细表Id
        /// </summary>
        public string FWORK_ORDER_MATERIAL_ID
        {
            get;
            set;
        }
        /// <summary>
        /// 工单Id
        /// </summary>
        public string FWORK_ORDER_ID
        {
            get;
            set;
        }
        public string FSTORE_ID { get; set; }
        public string FSTORE_PLACE_ID { get; set; }
    }
}
