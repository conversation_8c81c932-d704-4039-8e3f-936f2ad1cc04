﻿using HCloud.Core.Common;
using HCloud.Core.HCPlatform.Auth.Internal;
using HCloud.Core.HCPlatform.CommonData;
using HCloud.Core.HCPlatform.Ioc;
using HCloud.Core.HCPlatform.Multilingual;
using HCloud.Core.HCPlatform.Serialization;
using HCloud.Core.HCPlatform.ThirdParty;
using HCloud.Core.HCPlatform.Utilities;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.ProxyGenerator.Models;
using HCloud.Core.Sugar;
using HCloud.ERP.IModuleServices.ADM024_Employee.Models;
using HCloud.ERP.IModuleServices.COP001_SaleOrder;
using HCloud.ERP.IModuleServices.COP001_SaleOrder.Models;
using HCloud.ERP.IModuleServices.COP002_SaleDlv.Models;
using HCloud.ERP.IModuleServices.MES003_WorkOrder.Models;
using HCloud.ERP.IModuleServices.MES016_WoRec.Models;
using HCloud.ERP.IModuleServices.MES017_TAG;
using HCloud.ERP.IModuleServices.MES017_TAG.Models.DataModel;
using HCloud.ERP.IModuleServices.MES017_TAG.Models.Request;
using HCloud.ERP.IModuleServices.MES017_TAG.Models.Response;
using HCloud.ERP.IModuleServices.MSD001_Unit.Models;
using HCloud.ERP.IModuleServices.MSD002_Material.Models;
using HCloud.ERP.IModuleServices.PUR011_OutOrder.Models;
using HCloud.ERP.IModuleServices.PUR015_OutCraftRecv.Models;
using HCloud.ERP.IModuleServices.PUR024_SupplierDeliveryOrder.Models.DataModel;
using HCloud.ERP.IModuleServices.PUR021_OutOrderStock.Models.DataModel;
using HCloud.ERP.IModuleServices.STK001_Store.Models;
using HCloud.ERP.IModuleServices.STK002_StorePlace.Models;
using HCloud.ERP.IModuleServices.STK005_Stock.Models;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.Extensions.Logging;
using MongoDB.Driver.Core.Clusters;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using Org.BouncyCastle.Crypto;
using QRCoder;
using Spire.Pdf.Exporting.XPS.Schema;
using System;
using System.Buffers.Text;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using static NPOI.HSSF.Util.HSSFColor;
namespace HCloud.ERP.ModuleServices.MES017_TAG
{
    public partial class MES017TagService : ProxyServiceBase, IMES017TagService
    {



        /// <summary>
        /// 根据标签ID和物料ID来自动生成对应的标签条码
        /// </summary>
        /// <returns></returns>
        public async Task<DataResult<List<T_MESD_TAG_GENERATION_LOG>>> GenerateCodeList(TagGenModel model)
        {
            try
            {
                var db = _isugar.DB;
                var _TagGenList = new List<T_MESD_TAG_GENERATION_LOG>();
                if (string.IsNullOrWhiteSpace(model.GEN_NO))
                {

                    ERROR(null, 100700, _multiLang["生成失败，对应的生成编码不能为空！"]);

                }
                if (string.IsNullOrWhiteSpace(model.FTAG_ID) && string.IsNullOrWhiteSpace((model.FTAG_BELONG)))
                {

                    ERROR(null, 100700, _multiLang["生成失败，对应的生成标签FTAG_ID和标签归属不能同为空！"]);
                }

                var _CheckTag = await db.Queryable<T_MESD_TAG, T_MESD_TAG_STATUS>
                 ((mt, mts) => new JoinQueryInfos(JoinType.Left, mt.FTAG_ID == mts.FTAG_ID))
                 .Where(((mt, mts) => mt.FTAG_ID == model.FTAG_ID || (mt.FTAG_BELONG == model.FTAG_BELONG && mt.FTAG_CODE_GENERATE == model.FTAG_CODE_GENERATE)))
                 .Select<TagRkModel>().ToListAsync();
                if (_CheckTag == null)
                {
                    ERROR(null, 100700, _multiLang["生成失败，无法检索到对应的标签条码数据！"]);
                }
                if (_CheckTag.Count > 1)
                {

                    ERROR(null, 100700, _multiLang["生成失败，传入的FTAG_ID和对应的标签归属不匹配，可以使用FTAG_ID或者FTAG_BELONG和FTAG_CODE_GENERATE！"]);
                }
                //判断是否唯一码
                if (_CheckTag[0].FTAG_CODE_GENERATE.ToUpper() == "ONE")
                {
                    var checkTag = await db.Queryable<T_MESD_TAG_GENERATION_LOG>().FirstAsync(n => n.FBARCODE_NO == model.GEN_NO && n.FTAG_TYPE == _CheckTag[0].FTAG_BELONG);
                    if (checkTag != null)
                    {
                        _TagGenList.Add(checkTag);
                        return await OK(new DataResult<List<T_MESD_TAG_GENERATION_LOG>>
                        {
                            StatusCode = 200,
                            Message = _multiLang[$"已存在该{_CheckTag[0].FTAG_BELONG}标签唯一码，无需再生成!"],
                            Entity = _TagGenList,
                            IsSucceed = true
                        });
                    }
                    var AddView = new T_MESD_TAG_GENERATION_LOG();
                    AddView.FCDATE = model.GEN_TIME;
                    AddView.FBARCODE_NO = $"{model.GEN_NO}";
                    AddView.FTAG_ID = _CheckTag[0].FTAG_ID;
                    AddView.FBARCODE_TYPE = _CheckTag[0].FTAG_CODE_TYPE;
                    AddView.FTAG_CODE = _CheckTag[0].FTAG_CODE;
                    AddView.FUSE_STATUS = "UNUSED";
                    AddView.FTAG_TYPE = _CheckTag[0].FTAG_BELONG;
                    AddView.FPRINT_COUNT = 0;
                    AddView.FGEN_SOURCE_NO = model.GEN_NO;
                    AddView.FBILL_SOURCE_NO = model.FBILL_SOURCE_NO;
                    AddView.FBILL_TYPE = model.FBILL_TYPE;
                    AddView.FSTK_QTY = model.FSTK_QTY;
                    if (model.GEN_TIME != null)
                    {
                        AddView.FGEN_TIME = model.GEN_TIME;
                    }
                    else
                    {
                        AddView.FGEN_TIME = DateTime.Now;
                    }
                    _TagGenList.Add(AddView);


                }
                else
                {
                    //根据流水号走

                    var _FindGenLog = await db.Queryable<T_MESD_TAG_GENERATION_LOG>().Where(n => n.FBARCODE_NO.Contains($"{model.GEN_NO}.")).OrderBy(n => n.FSERIAL_NO, OrderByType.Desc).FirstAsync();
                    var _SerialNoList = new List<string>();
                    if (_FindGenLog != null)
                    {
                        _SerialNoList = GenSerialNo(_FindGenLog.FSERIAL_NO, (int)model.GEN_NUM);
                    }
                    else
                    {
                        var G_FSERIAL_NO = "";
                        for (var i = 1; i <= _CheckTag[0].FTAG_CODE_SERIAL; i++)
                        {

                            G_FSERIAL_NO += "0";
                        }

                        _SerialNoList = GenSerialNo(G_FSERIAL_NO, (int)model.GEN_NUM);
                    }

                    foreach (var item in _SerialNoList)
                    {
                        var AddView = new T_MESD_TAG_GENERATION_LOG();
                        AddView.FSERIAL_NO = item;
                        AddView.FCDATE = model.GEN_TIME;
                        AddView.FBARCODE_NO = $"{model.GEN_NO}.{item}";
                        AddView.FTAG_ID = _CheckTag[0].FTAG_ID;
                        AddView.FTAG_TYPE = _CheckTag[0].FTAG_BELONG;
                        AddView.FBARCODE_TYPE = _CheckTag[0].FTAG_CODE_TYPE;
                        AddView.FTAG_CODE = _CheckTag[0].FTAG_CODE;
                        AddView.FUSE_STATUS = "UNUSED";
                        AddView.FPRINT_COUNT = 0;
                        AddView.FGEN_SOURCE_NO = model.GEN_NO;
                        AddView.FBILL_SOURCE_NO = model.FBILL_SOURCE_NO;
                        AddView.FBILL_TYPE = model.FBILL_TYPE;
                        AddView.FSTK_QTY = model.FSTK_QTY;
                        if (model.GEN_TIME != null)
                        {
                            AddView.FGEN_TIME = model.GEN_TIME;
                        }
                        else
                        {
                            AddView.FGEN_TIME = DateTime.Now;
                        }
                        _TagGenList.Add(AddView);
                    }

                }
                _TagGenList.ForEach(x => x.FTAG_GEN_LOG_ID = GuidHelper.NewGuid());
                var result = await db.Insertable<T_MESD_TAG_GENERATION_LOG>(_TagGenList).ExecuteCommandAsync();
                //添加成功返回
                return await OK(new DataResult<List<T_MESD_TAG_GENERATION_LOG>>
                {
                    StatusCode = 200,
                    Message = _multiLang["生成成功"],
                    Entity = _TagGenList,
                    IsSucceed = true
                });

            }
            catch (Exception)
            {

                throw;
            }
        }


        /// <summary>
        /// 根据单据和对应的明细生成对应的物料流水标签
        /// </summary>
        /// <returns></returns>
        //public async Task<DataResult<List<T_MESD_TAG_GENERATION_LOG>>> GenerateOrderCodeList(List<TagGenModel> models)
        //{
        //    try
        //    {
        //        var db = _isugar.DB;
        //        var _TagGenList = new List<T_MESD_TAG_GENERATION_LOG>();
        //        if (models.Any(n => string.IsNullOrWhiteSpace(n.GEN_NO)))
        //        {

        //            ERROR(null, 100700, _multiLang["生成失败，对应的生成编码不能为空！"]);

        //        }

        //        var FGENNOS = models.Select(n => n.GEN_NO).Distinct().ToList();
        //        var FBILL_SOURCE_NOS = models.Select(n => n.FBILL_SOURCE_NO).Distinct().ToList();
        //        var _CheckTag = await db.Queryable<T_MESD_TAG, T_MESD_TAG_STATUS>
        //         ((mt, mts) => new JoinQueryInfos(JoinType.Left, mt.FTAG_ID == mts.FTAG_ID))
        //         .Where((mt, mts) =>mt.FTAG_BELONG== "FMATERIAL"&&mt.FTAG_CODE_GENERATE=="SERIAL")
        //         .Select<TagRkModel>().FirstAsync();
        //        if (_CheckTag == null)
        //        {
        //            ERROR(null, 100700, _multiLang["生成失败，无法检索到对应的标签条码数据！"]);
        //        }

        //        //根据流水号走
        //        var latestLogs = await db.Queryable<T_MESD_TAG_GENERATION_LOG>()
        //                              .Where(n => FGENNOS.Contains( n.FGEN_SOURCE_NO))
        //                               .OrderBy(n => n.FSERIAL_NO, OrderByType.Desc)
        //                               .ToListAsync();


        //        var _SerialNoList = new List<GenSerialModel>();
        //        if (latestLogs != null&& latestLogs.Count>0)
        //        {
        //                foreach (var item in FGENNOS)
        //                {
        //                    var _findGenNO = latestLogs.OrderByDescending(n=>n.FSERIAL_NO).FirstOrDefault(N => N.FGEN_SOURCE_NO == item);
        //                    if (_findGenNO != null)
        //                    {
        //                        var _nolist = GenBillSerialNo(_findGenNO.FSERIAL_NO, item,null, models.Count(n => n.GEN_NO == item));
        //                    _SerialNoList.AddRange(_nolist);

        //                }
        //                else
        //                    {
        //                        var G_FSERIAL_NO = "";
        //                        for (var i = 1; i <= _CheckTag.FTAG_CODE_SERIAL; i++)
        //                        {
        //                            G_FSERIAL_NO += "0";
        //                        }
        //                        var _nolist = GenBillSerialNo(G_FSERIAL_NO,item, null,models.Count(n => n.GEN_NO == item));
        //                        _SerialNoList.AddRange(_nolist);
        //                    }

        //                }

        //        }
        //        else
        //        {
        //            foreach (var item in FGENNOS)
        //            {
        //                    var G_FSERIAL_NO = "";
        //                    for (var i = 1; i <= _CheckTag.FTAG_CODE_SERIAL; i++)
        //                    {
        //                        G_FSERIAL_NO += "0";
        //                    }
        //                    var _nolist = GenBillSerialNo(G_FSERIAL_NO, item,null,models.Count(n => n.GEN_NO == item));
        //                    _SerialNoList.AddRange(_nolist);
        //            }
        //        }

        //        // 统计 (FBILL_SOURCE_NO, GEN_NO) 分组
        //        var comboGroups = models
        //            .GroupBy(m => new { m.FBILL_SOURCE_NO, m.GEN_NO })
        //            .ToDictionary(g => (g.Key.FBILL_SOURCE_NO, g.Key.GEN_NO), g => g.Count());


        //        // 按顺序从 _SerialNoList 中分配序列号，并写入 FBILL_SOURCE_NO
        //        int serialIndex = 0;
        //        foreach (var combo in comboGroups)
        //        {
        //            var count = combo.Value;
        //            var serials = _SerialNoList.Skip(serialIndex).Take(count).ToList();

        //            // 在这里写入 FBILL_SOURCE_NO
        //            foreach (var serial in serials)
        //            {
        //                serial.billno = combo.Key.FBILL_SOURCE_NO;  // 即：combo.Key.FBILL_SOURCE_NO
        //            serial.itemid=combo.Key.FBILL_SOURCE_NO;
        //            }

        //            serialIndex += count;
        //        }

        //            foreach (var item in _SerialNoList) {

        //                var AddView = new T_MESD_TAG_GENERATION_LOG();
        //                AddView.FSERIAL_NO = item.serialno;
        //                AddView.FCDATE =DateTime.Now;
        //                AddView.FBARCODE_NO = $"{item.barno}.{item.serialno}";
        //                AddView.FTAG_ID = _CheckTag.FTAG_ID;
        //                AddView.FTAG_TYPE = _CheckTag.FTAG_BELONG;
        //                AddView.FBARCODE_TYPE = _CheckTag.FTAG_CODE_TYPE;
        //                AddView.FTAG_CODE = _CheckTag.FTAG_CODE;
        //                AddView.FUSE_STATUS = "UNUSED";
        //                AddView.FPRINT_COUNT = 0;
        //                AddView.FGEN_SOURCE_NO = item.barno;
        //                AddView.FBILL_SOURCE_NO = item.billno;
        //                AddView.FBILL_TYPE = models.FirstOrDefault(n => n.FBILL_SOURCE_NO == item.billno)?.FBILL_TYPE;
        //                AddView.FGEN_TIME = DateTime.Now;
        //            AddView.FBILL_SOURCE_ITEM_ID = item.itemid;
        //                _TagGenList.Add(AddView);
        //            }
        //        _TagGenList.ForEach(x => x.FTAG_GEN_LOG_ID = GuidHelper.NewGuid());
        //        var result = await db.Insertable<T_MESD_TAG_GENERATION_LOG>(_TagGenList).ExecuteCommandAsync();
        //        //添加成功返回
        //        return await OK(new DataResult<List<T_MESD_TAG_GENERATION_LOG>>
        //        {
        //            StatusCode = 200,
        //            Message = _multiLang["生成成功"],
        //            Entity = _TagGenList,
        //            IsSucceed = true
        //        });

        //    }
        //    catch (Exception)
        //    {

        //        throw;
        //    }
        //}
        public async Task<DataResult<List<T_MESD_TAG_GENERATION_LOG>>> GenerateOrderCodeList(List<TagGenModel> models)
        {
            var db = _isugar.DB;
            var tagGenList = new List<T_MESD_TAG_GENERATION_LOG>();

            if (models.Any(n => string.IsNullOrWhiteSpace(n.GEN_NO)))
            {
                ERROR(null, 100700, _multiLang["生成失败，对应的生成编码不能为空！"]);
            }

            var genNos = models.Select(n => n.GEN_NO).Distinct().ToList();

            var tagConfig = await db.Queryable<T_MESD_TAG, T_MESD_TAG_STATUS>((mt, mts) =>
                    new JoinQueryInfos(JoinType.Left, mt.FTAG_ID == mts.FTAG_ID))
                .Where((mt, mts) => mt.FTAG_BELONG == "FMATERIAL" && mt.FTAG_CODE_GENERATE == "SERIAL")
                .Select<TagRkModel>().FirstAsync();

            if (tagConfig == null)
            {
                ERROR(null, 100700, _multiLang["生成失败，无法检索到对应的标签条码数据！"]);
            }

            int serialLength = (int)tagConfig.FTAG_CODE_SERIAL;

            // 防止并发重试最多3次
            int retryCount = 0;
            const int maxRetries = 3;
            while (retryCount < maxRetries)
            {
                tagGenList.Clear();
                var latestLogs = await db.Queryable<T_MESD_TAG_GENERATION_LOG>()
                    .Where(n => genNos.Contains(n.FGEN_SOURCE_NO))
                    .OrderBy(n => n.FSERIAL_NO, OrderByType.Desc)
                    .ToListAsync();

                var serialDict = new Dictionary<string, int>();
                foreach (var genNo in genNos)
                {
                    var maxSerial = latestLogs
                        .Where(n => n.FGEN_SOURCE_NO == genNo)
                        .OrderByDescending(n => n.FSERIAL_NO)
                        .Select(n => n.FSERIAL_NO)
                        .FirstOrDefault();

                    int current = 0;
                    if (!string.IsNullOrEmpty(maxSerial))
                        int.TryParse(maxSerial, out current);

                    serialDict[genNo] = current;
                }

                foreach (var model in models)
                {
                    var genNo = model.GEN_NO;
                    var billNo = model.FBILL_SOURCE_NO;
                    var itemId = model.FBILL_SOURCE_ITEM_ID;
                    var billType = model.FBILL_TYPE;
                    var stkqty = model.FSTK_QTY;
                    int current = serialDict[genNo] + 1;
                    serialDict[genNo] = current;

                    string serialNo = current.ToString($"D{serialLength}");

                    var log = new T_MESD_TAG_GENERATION_LOG
                    {
                        FTAG_GEN_LOG_ID = GuidHelper.NewGuid(),
                        FSERIAL_NO = serialNo,
                        FCDATE = DateTime.Now,
                        FBARCODE_NO = $"{genNo}.{serialNo}",
                        FTAG_ID = tagConfig.FTAG_ID,
                        FTAG_TYPE = tagConfig.FTAG_BELONG,
                        FBARCODE_TYPE = tagConfig.FTAG_CODE_TYPE,
                        FTAG_CODE = tagConfig.FTAG_CODE,
                        FUSE_STATUS = "UNUSED",
                        FPRINT_COUNT = 0,
                        FGEN_SOURCE_NO = genNo,
                        FBILL_SOURCE_NO = billNo,
                        FBILL_TYPE = billType,
                        FGEN_TIME = DateTime.Now,
                        FBILL_SOURCE_ITEM_ID = itemId,
                        FSTK_QTY = stkqty
                    };

                    tagGenList.Add(log);
                }

                // 再次查询数据库，确认没有重复序列号
                var exists = await db.Queryable<T_MESD_TAG_GENERATION_LOG>()
                    .Where(t => tagGenList.Select(g => g.FBARCODE_NO).Contains(t.FBARCODE_NO))
                    .CountAsync();

                if (exists > 0)
                {
                    retryCount++;
                    continue; // 重试
                }

                // 插入并返回
                await db.Insertable(tagGenList).ExecuteCommandAsync();
                return await OK(new DataResult<List<T_MESD_TAG_GENERATION_LOG>>
                {
                    StatusCode = 200,
                    Message = _multiLang["生成成功"],
                    Entity = tagGenList,
                    IsSucceed = true
                });
            }

            // 如果重试仍冲突
            ERROR(null, 100701, _multiLang["生成失败，系统繁忙，请稍后再试！"]);
            return null; // unreachable
        }



        /// <summary>
        /// 生成二维码
        /// </summary>
        /// <returns></returns>

        private string GenCode(string no)
        {
            QRCodeGenerator qRCodeGenerator = new QRCodeGenerator();
            QRCodeData qRCodeData = qRCodeGenerator.CreateQrCode(no, QRCodeGenerator.ECCLevel.Q);
            QRCode qRCode = new QRCode(qRCodeData);
            Bitmap qrCodeImage = qRCode.GetGraphic(20); // 20是二维码的大小，可以根据需要调整
            MemoryStream ms = new MemoryStream();
            qrCodeImage.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
            byte[] arr = new byte[ms.Length];
            ms.Position = 0;
            ms.Read(arr, 0, (int)ms.Length);
            ms.Close();
            String strbaser64 = Convert.ToBase64String(arr);
            return strbaser64;

        }

        /// <summary>
        /// 初始流水号,构建数
        /// </summary>
        /// <param name="no"></param>
        ///  /// <param name="GenNum">构建数</param>
        /// <returns></returns>
        private List<string> GenSerialNo(string no, int GenNum)
        {

            int number = int.Parse(no);
            int length = no.Length;
            var _SerialNoList = new List<string>();
            for (int i = 0; i < GenNum; i++)
            {
                number++;
                string incremented = number.ToString($"D{length}");
                _SerialNoList.Add(incremented);
            }
            return _SerialNoList;
        }


        /// <summary>
        /// 初始流水号,构建数,返回来源单据号
        /// </summary>
        /// <param name="no"></param>
        ///  /// <param name="GenNum">构建数</param>
        /// <returns></returns>
        private List<GenSerialModel> GenBillSerialNo(string no, string barno, string billno, int GenNum)
        {

            int number = int.Parse(no);
            int length = no.Length;
            var _SerialNoList = new List<GenSerialModel>();
            for (int i = 0; i < GenNum; i++)
            {
                number++;
                string incremented = number.ToString($"D{length}");
                _SerialNoList.Add(new GenSerialModel { barno = barno, billno = billno, serialno = incremented });
            }
            return _SerialNoList;
        }


        /// <summary>
        /// 获取标签生成列表
        /// </summary>
        /// <param name="unitCate"></param>
        /// <returns></returns>
        public async Task<DataResult<List<TagCodeRkModel>>> GetAllGenListAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;

            //转换查询条件
            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);

            //查询数据  dao
            RefAsync<int> totalSize = new RefAsync<int>();
            List<TagCodeRkModel> datas = await db.Queryable<T_MESD_TAG_GENERATION_LOG, T_MESD_TAG_SCAN_LOG>
                ((mt, mts) => new JoinQueryInfos(JoinType.Left, mt.FBARCODE_NO == mts.FBARCODE_NO))
                .Where(wheres)
                .Select<TagCodeRkModel>()
                .OrderBy(mt => mt.FGEN_TIME, OrderByType.Desc)
                .OrderBy(mt => mt.FBARCODE_NO, OrderByType.Desc)
                .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);
            datas.ForEach(x => x.FBASE_URL = GenCode(x.FBARCODE_NO));
            DataResult<List<TagCodeRkModel>> result = new DataResult<List<TagCodeRkModel>>()
            {
                Entity = datas,
                Pager = new PagerResult()
                {
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                    TotalRecords = totalSize.Value,
                },
                StatusCode = 200,
            };
            return await OK(result);
        }


        /// <summary>
        /// 标签扫描
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public async Task<DataResult<object>> ScanCodeAsync(TagScanModel model)
        {

            try
            {
                var db = _isugar.DB;
                DataResult<object> result = new DataResult<object>();
                var _mainDB = _isugar.GetDB("MAIN");

                //盘点入库
                if (model.PROGRAM_NAME.ToUpper() == ScanAppType.FMScan.ToString().ToUpper())
                {
                    return await FMScanApp(model);
                }
                //委外出库
                else if (model.PROGRAM_NAME.ToUpper() == ScanAppType.OutOrderOutScan.ToString().ToUpper() || model.PROGRAM_NAME.ToUpper() == ScanAppType.RecOutScan.ToString().ToUpper())
                {
                    return await OrderOutScanApp(model);
                }

                else
                {
                    result = new DataResult<object>()
                    {
                        Message = "执行失败，未匹配到对应的扫码执行类型!",
                        StatusCode = 400,
                    };
                }

                return await OK(result);
            }
            catch (Exception)
            {
                throw;
            }



        }


        /// <summary>
        /// 根据条码获取物料信息和仓库信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> ScanMaterialGetStockInfo(TagScanModel model)
        {

            try
            {
                var _mstDB = _isugar.GetDB("MAIN");
                var _stkDB = _isugar.GetDB("STOCK");
                var _mesDB = _isugar.GetDB("MES");
                if (string.IsNullOrWhiteSpace(model.FBARCODE_NO))
                {
                    ERROR(null, 100100, _multiLang["扫描失败,扫描条码不能为空！"]);
                }
                var SERIALNO = model.FBARCODE_NO.Split(".")[0];
                var _MaterialView = await _mstDB.Queryable<T_MSDM_MATERIAL, T_MSDM_MATERIAL_STORE>((a, b) => new JoinQueryInfos(JoinType.Left, a.FMATERIAL_ID == b.FMATERIAL_ID))
                 .Where((a, b) => a.FMATERIAL_CODE == SERIALNO)
                    .Select((a, b) => new MaterialAndStockModel
                    {
                        FMATERIAL_CODE = a.FMATERIAL_CODE,
                        FSTORE_ID = b.FSTORE_ID,
                        FSTORE_PLACE_ID = b.FSTORE_PLACE_ID,
                        FMATERIAL_NAME = a.FMATERIAL_NAME,
                        FTAG_CODE_TYPE = b.FTAG_CODE_TYPE

                    }).FirstAsync();
                if (_MaterialView == null)
                {
                    ERROR(null, 100100, _multiLang["扫描失败,不存在对应的物料编号！"]);
                }
                if (string.IsNullOrWhiteSpace(_MaterialView.FTAG_CODE_TYPE))
                {
                    ERROR(null, 100100, _multiLang["扫描失败,未配置对应的库存物料标签类型！"]);
                }

                //查询编码
                var FindGenCodeInfo = await _mesDB.Queryable<T_MESD_TAG_GENERATION_LOG>().Where(n => n.FBARCODE_NO == model.FBARCODE_NO).FirstAsync();
                if (FindGenCodeInfo == null)
                {
                    ERROR(null, 100100, _multiLang["扫描失败,不存在对应的物料标签！"]);
                }
                else
                {
                    _MaterialView.FSTK_QTY = FindGenCodeInfo.FSTK_QTY;
                }


                var stockdata = await _stkDB.Queryable<T_STKM_STORE, T_STKM_STORE_PLACE>((a, b) => new JoinQueryInfos(JoinType.Left, a.FSTORE_ID == b.FSTORE_ID))
                    .Where((a, b) => a.FSTORE_ID == _MaterialView.FSTORE_ID).WhereIF(!string.IsNullOrWhiteSpace(_MaterialView.FSTORE_PLACE_ID), (a, b) => b.FSTORE_PLACE_ID == _MaterialView.FSTORE_PLACE_ID)
                   .Select((a, b) => new { a.FSTORE_CODE, b.FSTORE_ID, b.FSTORE_PLACE_ID, b.FSTORE_PLACE_CODE }).FirstAsync();
                if (stockdata != null)
                {
                    _MaterialView.FSTORE_CODE = stockdata.FSTORE_CODE;
                    _MaterialView.FSTORE_PLACE_CODE = stockdata.FSTORE_PLACE_CODE;
                }
                else
                {

                    ERROR(null, 100100, _multiLang["扫描失败,该物料未配置仓库，无法进行盘点！"]);
                }
                DataResult<object> result = new DataResult<object>()
                {
                    Entity = _MaterialView,
                    StatusCode = 200,
                };
                return await OK(result);
            }
            catch (Exception ex)
            {

                DataResult<object> result = new DataResult<object>()
                {
                    Entity = null,
                    StatusCode = 400,
                    Message = ex.Message
                };
                return await OK(result);
            }


        }


        /// <summary>
        /// 工单扫码获取物料信息
        /// </summary>
        /// <returns></returns>
        public async Task<DataResult<object>> GeneratePredictBomCodeList(TagOrderScanModel model)
        {

            try
            {
                var db = _isugar.DB;
                var _mainDB = _isugar.GetDB("MAIN");
                var _purpDB = _isugar.GetDB("PUR");
                var _mesDB = _isugar.GetDB("MES");
                var _stkdb = _isugar.GetDB("STOCK");
                var _copdb = _isugar.GetDB("COP");
                var result = new DataResult<object>();
                if (string.IsNullOrWhiteSpace(model.FBILL_SOURCE_NO))
                {
                    ERROR(null, 100100, _multiLang["扫描失败,扫描条码不能为空！"]);
                }
                if (string.IsNullOrWhiteSpace(model.FBILL_TYPE))
                {
                    ERROR(null, 100100, _multiLang["扫描失败,单据类型不能为空！"]);
                }

                var _TagBillList = new List<MaterialTagModel>();

                if (model.FBILL_TYPE.ToUpper() == ScanAppType.OutOrderInScan.ToString().ToUpper())
                {
                    //查询对应的委外订单数据
                    _TagBillList = await _purpDB.Queryable<T_PURD_OUT_CRAFT_RECV, T_PURD_OUT_CRAFT_RECV_MATERIAL>((a, b) => new JoinQueryInfos(JoinType.Left, a.FOUT_CRAFT_RECV_ID == b.FOUT_CRAFT_RECV_ID))
                        .Where((a, b) => a.FOUT_CRAFT_RECV_NO == model.FBILL_SOURCE_NO)
                        .Select((a, b) => new MaterialTagModel { FBILL_SOURCE_NO = a.FOUT_CRAFT_RECV_NO, FBILL_SOURCE_ITEM_ID = b.FOUT_CRAFT_RECV_MATERIAL_ID, MATERIAL_ID = b.FMATERIAL_ID, FNUM = b.FPRO_UNIT_QTY, FSTK_UNIT_ID = b.FPRO_UNIT_ID }).ToListAsync();
                    if (_TagBillList == null || _TagBillList.Count == 0)
                    {
                        ERROR(null, 100100, _multiLang["扫描失败,对应的委外单据查不到相关的数据！"]);
                    }

                }
                else if (model.FBILL_TYPE.ToUpper() == ScanAppType.OutOrderOutScan.ToString().ToUpper())
                {
                    //查询对应的委外订单数据
                    _TagBillList = await _purpDB.Queryable<T_PURD_OUT_ORDER_STOCK, T_PURD_OUT_ORDER_STOCK_STATUS, T_PURD_OUT_ORDER_STOCK_MATERIAL,T_PURD_OUT_ORDER_MATERIAL>((a, b,c,d) => new JoinQueryInfos(JoinType.Left, a.FOUT_ORDER_STOCK_ID == b.FOUT_ORDER_STOCK_ID,JoinType.Left,c.FOUT_ORDER_STOCK_ID==a.FOUT_ORDER_STOCK_ID, JoinType.Left,d.FOUT_ORDER_MATERIAL_ID==c.FOUT_ORDER_MATERIAL_ID))
                        .Where((a, b,c,d) => a.FOUT_ORDER_STOCK_NO == model.FBILL_SOURCE_NO)
                        .Select((a, b,c,d) => new MaterialTagModel { FBILL_SOURCE_NO = a.FOUT_ORDER_STOCK_NO, FBILL_SOURCE_ITEM_ID = c.FOUT_ORDER_STOCK_MATERIAL_ID, MATERIAL_ID = d.FMATERIAL_ID, FNUM = c.FQTY, FSTK_UNIT_ID = c.FPRO_UNIT_ID }).ToListAsync();
                    if (_TagBillList == null || _TagBillList.Count == 0)
                    {
                        ERROR(null, 100100, _multiLang["扫描失败,对应的委外单据查不到相关的数据！"]);
                    }
                }
                else if (model.FBILL_TYPE.ToUpper() == ScanAppType.RecOutScan.ToString().ToUpper())
                {
                    //查询对应的领料订单数据
                    _TagBillList = await _mesDB.Queryable<T_MESD_WORK_REC, T_MESD_WORK_REC_MAT>((a, b) => new JoinQueryInfos(JoinType.Left, a.FWORK_REC_ID == b.FWORK_REC_ID))
                        .Where((a, b) => a.FWORK_REC_NO == model.FBILL_SOURCE_NO)
                        .Select((a, b) => new MaterialTagModel { FBILL_SOURCE_NO = a.FWORK_REC_NO, FBILL_SOURCE_ITEM_ID = b.FWORK_REC_MATERIAL_ID, MATERIAL_ID = b.FSUB_MATERIAL_ID, FSTK_UNIT_ID = b.FSTK_UNIT_ID, FNUM = b.FSTK_UNIT_QTY })
                        .ToListAsync();
                    if (_TagBillList == null || _TagBillList.Count == 0)
                    {
                        ERROR(null, 100100, _multiLang["扫描失败,对应的领料单据查不到相关的数据！"]);
                    }

                }
                else if (model.FBILL_TYPE.ToUpper() == ScanAppType.OrderInScan.ToString().ToUpper())
                {
                    //查询生产订单数据
                    _TagBillList = await _mesDB.Queryable<T_MESD_WORK_ORDER>()
                        .Where(a => a.FWORK_ORDER_NO == model.FBILL_SOURCE_NO)
                        .Select(a => new MaterialTagModel { FBILL_SOURCE_NO = a.FWORK_ORDER_NO, FBILL_SOURCE_ITEM_ID = a.FWORK_ORDER_ID, MATERIAL_ID = a.FMATERIAL_ID, FSTK_UNIT_ID = a.FUNIT_ID, FNUM = a.FPRO_QTY })
                        .ToListAsync();
                    if (_TagBillList == null || _TagBillList.Count == 0)
                    {
                        ERROR(null, 100100, _multiLang["扫描失败,对应的领料单据查不到相关的数据！"]);
                    }

                }
                else if (model.FBILL_TYPE.ToUpper() == ScanAppType.InvOutScan.ToString().ToUpper())
                {

                    //查询生产订单数据
                    _TagBillList = await _copdb.Queryable<T_COPD_SALE_DLV, T_COPD_SALE_DLV_MATERIAL>((a, b) => new JoinQueryInfos(JoinType.Left, a.FSALE_DLV_ID == b.FSALE_DLV_ID))
                        .Where((a, b) => a.FSALE_DLV_NO == model.FBILL_SOURCE_NO)
                        .Select((a, b) => new MaterialTagModel { FBILL_SOURCE_NO = a.FSALE_DLV_NO, FBILL_SOURCE_ITEM_ID = b.FSALE_DLV_MATERIAL_ID, MATERIAL_ID = b.FMATERIAL_ID, FSTK_UNIT_ID = b.FSTK_UNIT_ID, FNUM = b.FSTK_UNIT_QTY })
                        .ToListAsync();
                    if (_TagBillList == null || _TagBillList.Count == 0)
                    {
                        ERROR(null, 100100, _multiLang["扫描失败,对应的领料单据查不到相关的数据！"]);
                    }

                }
                else if (model.FBILL_TYPE.ToUpper() == ScanAppType.SupDecvScan.ToString().ToUpper())
                {
                    // 查询已审核未作废
                    var list = await _purpDB.Queryable<T_PURD_SUPPLIER_DELIVERY_ORDER, T_PURD_SUPPLIER_DELIVERY_ORDER_STATUS, T_PURD_SUPPLIER_DELIVERY_ORDER_MATERIAL>
                    ((a, b, c) => new JoinQueryInfos(
                        JoinType.Left, a.FSUPPLIER_DELIVERY_ORDER_ID == b.FSUPPLIER_DELIVERY_ORDER_ID,
                        JoinType.Left, a.FSUPPLIER_DELIVERY_ORDER_ID == c.FSUPPLIER_DELIVERY_ORDER_ID))
                     .Where((a, b, c) => a.FSUPPLIER_DELIVERY_ORDER_NO == model.FBILL_SOURCE_NO)
                     .Select((a, b, c) => new
                     {
                         FBILL_SOURCE_NO = a.FSUPPLIER_DELIVERY_ORDER_NO,
                         FBILL_SOURCE_ITEM_ID = c.FSUPPLIER_DELIVERY_ORDER_MATERIAL_ID,
                         MATERIAL_ID = c.FMATERIAL_ID,
                         FSTK_UNIT_ID = c.FPRO_UNIT_ID,
                         FNUM = c.FDELIVERY_QTY,
                         b.FCFLAG,
                         b.FIF_CANCEL
                     })
                     .ToListAsync();
                    if (list == null || list.Count == 0)
                    {
                        ERROR(null, 100100, _multiLang["扫描失败,对应的供应商发货单据查不到相关的数据！"]);
                    }
                    //判断是否未审核
                    if (list.Any(n => n.FCFLAG == 0))
                    {
                        ERROR(null, 100100, _multiLang["扫描失败,对应的供应商发货单据未审核！"]);
                    }
                    if (list.Any(n => n.FIF_CANCEL))
                    {
                        ERROR(null, 100100, _multiLang["扫描失败,对应的供应商发货单据已作废！"]);
                    }

                    _TagBillList = list.Select(n => new MaterialTagModel
                    {
                        FBILL_SOURCE_NO = n.FBILL_SOURCE_NO,
                        FBILL_SOURCE_ITEM_ID = n.FBILL_SOURCE_ITEM_ID,
                        MATERIAL_ID = n.MATERIAL_ID,
                        FSTK_UNIT_ID = n.FSTK_UNIT_ID,
                        FNUM = n.FNUM
                    }).ToList();
                }
                //判断



                //if (_TagBillList.Count > 0)
                //{

                //    //发货出库待补
                //    _TagBillList.ForEach(x =>
                //    {
                //        x.FTAG_BILL_ID = GuidHelper.NewGuid();
                //        x.FBILL_TYPE = model.FBILL_TYPE.ToUpper();
                //        x.FSCAN_TYPE = "UNSCAN";
                //        x.FTAG_CODE_TYPE = _MaterialList.FirstOrDefault(n => n.FMATERIAL_ID == x.MATERIAL_ID)?.FTAG_CODE_TYPE;
                //    });
                //    ////判断物料标签类型是否设置

                //    //foreach (var item in _TagBillList)
                //    //{

                //    //    if (string.IsNullOrWhiteSpace(item.FTAG_CODE_TYPE))
                //    //    {
                //    //        var _mck = _MaterialList.FirstOrDefault(n => n.FMATERIAL_ID == item.MATERIAL_ID);
                //    //        ERROR(null, 100100, _multiLang[$"扫描失败,对应的物料{_mck.FMATERIAL_CODE}:{_mck.FMATERIAL_NAME}未设置对应的库存标签条码类型！"]);
                //    //    }
                //    //}
                //    //await db.Insertable(_TagBillList).ExecuteCommandAsync();
                //    _taglist = await db.Queryable<T_MESD_TAG_BILL>().Where(n => n.FBILL_TYPE == model.FBILL_TYPE.ToUpper() && n.FBILL_SOURCE_NO == model.FBILL_SOURCE_NO).Select<OrderTagRkModel>().ToListAsync();

                //    //判断是否委外入库和工单入库,在标签记录添加数据

                //    //if (model.FBILL_TYPE.ToUpper() == ScanAppType.OrderInScan.ToString().ToUpper() || model.FBILL_TYPE.ToUpper() == ScanAppType.OutOrderInScan.ToString().ToUpper())
                //    //{
                //    //    //根据物料类型产生对应的标签记录
                //    //    foreach (var item in _taglist)
                //    //    {
                //    //        var _mck = _MaterialList.FirstOrDefault(n => n.FMATERIAL_ID == item.MATERIAL_ID);

                //    //        await GenerateCodeList(new TagGenModel
                //    //        {
                //    //            GEN_NO = _mck.FMATERIAL_CODE,
                //    //            FTAG_BELONG = "FMATERIAL",
                //    //            FTAG_CODE_GENERATE = _mck.FTAG_CODE_TYPE,
                //    //            GEN_TIME = DateTime.Now,
                //    //            FBILL_TYPE = model.FBILL_TYPE.ToUpper(),
                //    //            FBILL_SOURCE_NO = item.FBILL_SOURCE_NO,
                //    //            GEN_NUM = _mck.FTAG_CODE_TYPE.ToUpper() == "ONE" ? 1 : (int)item.FNUM
                //    //        });
                //    //    }

                //    //}
                //}
                var _matids = _TagBillList.Select(n => n.MATERIAL_ID).Distinct().ToList();

                var _MaterialList = await _mainDB.Queryable<T_MSDM_MATERIAL, T_MSDM_MATERIAL_STORE, T_MSDM_UNIT>((a, b, c) => new JoinQueryInfos(JoinType.Left, a.FMATERIAL_ID == b.FMATERIAL_ID, JoinType.Left, c.FUNIT_ID == a.FUNIT_ID))
               .Where((a, b) => _matids.Contains(a.FMATERIAL_ID))
                  .Select((a, b, c) => new MaterialAndStockModel
                  {
                      FMATERIAL_ID = a.FMATERIAL_ID,
                      FMATERIAL_CODE = a.FMATERIAL_CODE,
                      FSTORE_ID = b.FSTORE_ID,
                      FSTORE_PLACE_ID = b.FSTORE_PLACE_ID,
                      FMATERIAL_NAME = a.FMATERIAL_NAME,
                      FTAG_CODE_TYPE = b.FTAG_CODE_TYPE,
                      FSPEC_DESC = a.FSPEC_DESC,
                      FSTK_UNIT_NAME = c.FUNIT_NAME

                  }).ToListAsync();
                var storeid = _MaterialList.Select(n => n.FSTORE_ID).Distinct().ToList();
                var placeid = _MaterialList.Where(n => !string.IsNullOrWhiteSpace(n.FSTORE_PLACE_ID)).Select(n => n.FSTORE_PLACE_ID).Distinct().ToList();
                //获取仓库基本信息
                var stockdata = await _stkdb.Queryable<T_STKM_STORE, T_STKM_STORE_PLACE>((a, b) => new JoinQueryInfos(JoinType.Left, a.FSTORE_ID == b.FSTORE_ID))
                   .Where((a, b) => storeid.Contains(a.FSTORE_ID)).WhereIF(placeid != null && placeid.Count() > 0, (a, b) => placeid.Contains(b.FSTORE_PLACE_ID))
                  .Select((a, b) => new { a.FSTORE_CODE, a.FSTORE_ID, a.FSTORE_NAME, b.FSTORE_PLACE_ID, b.FSTORE_PLACE_CODE, b.FSTORE_PLACE_NAME }).ToListAsync();
                //if (stockdata != null)
                //{
                //    _MaterialView.FSTORE_CODE = stockdata.FSTORE_CODE;
                //    _MaterialView.FSTORE_PLACE_CODE = stockdata.FSTORE_PLACE_CODE;
                //}

                _TagBillList.ForEach(x =>
                {
                    var _Materialinfo = _MaterialList.FirstOrDefault(n => n.FMATERIAL_ID == x.MATERIAL_ID);
                    var _stkinfo =!string.IsNullOrWhiteSpace(_Materialinfo.FSTORE_ID)? stockdata.FirstOrDefault(n => n.FSTORE_ID == _Materialinfo.FSTORE_ID):null;
                    x.FMATERIAL_NAME = _Materialinfo?.FMATERIAL_NAME;
                    x.FMATERIAL_CODE = _Materialinfo?.FMATERIAL_CODE;
                    x.FSTK_UNIT_NAME = _Materialinfo?.FSTK_UNIT_NAME;
                    x.FSPEC_DESC = _Materialinfo?.FSPEC_DESC;
                    x.FSTORE_ID = _Materialinfo?.FSTORE_ID;
                    x.FSTORE_PLACE_ID = _Materialinfo?.FSTORE_PLACE_ID;
                    x.FSTORE_NAME = _stkinfo?.FSTORE_NAME;
                    x.FSTORE_PLACE_NAME = _stkinfo?.FSTORE_PLACE_NAME;
                    x.FTAG_CODE_TYPE = _Materialinfo?.FTAG_CODE_TYPE;
                    x.FBILL_TYPE = model.FBILL_TYPE.ToUpper();
                    x.FSTK_QTY = 0;
                });
                var _taglist = await db.Queryable<T_MESD_TAG_GENERATION_LOG>()
                  .Where(n => n.FBILL_TYPE == model.FBILL_TYPE.ToUpper() && n.FBILL_SOURCE_NO == model.FBILL_SOURCE_NO).OrderBy(n => n.FBILL_SOURCE_ITEM_ID)
                  .Select<OrderTagRkModel>().ToListAsync();
                _taglist.ForEach(x =>
                {
                    var iteminfo = _TagBillList.FirstOrDefault(n => n.FBILL_SOURCE_ITEM_ID == x.FBILL_SOURCE_ITEM_ID);
                    var _stkinfo =!string.IsNullOrWhiteSpace(iteminfo.FSTORE_ID)? stockdata.FirstOrDefault(n => n.FSTORE_ID == iteminfo.FSTORE_ID):null;
                    x.FMATERIAL_NAME = iteminfo?.FMATERIAL_NAME;
                    x.FMATERIAL_CODE = iteminfo?.FMATERIAL_CODE;
                    x.FSTK_UNIT_NAME = iteminfo?.FSTK_UNIT_NAME;
                    x.FSPEC_DESC = iteminfo?.FSPEC_DESC;
                    x.FSTORE_ID = iteminfo?.FSTORE_ID;
                    x.FSTORE_PLACE_ID = iteminfo?.FSTORE_PLACE_ID;
                    x.FSTORE_NAME = _stkinfo?.FSTORE_NAME;
                    x.FSTORE_PLACE_NAME = _stkinfo?.FSTORE_PLACE_NAME;
                    x.FTAG_CODE_TYPE = iteminfo?.FTAG_CODE_TYPE;

                });
                if (_taglist == null || _taglist.Count == 0)
                {
                    _TagBillList.ForEach(x =>
                    {
                        x.TagNum = 0;
                    });
                }
                else
                {
                    _TagBillList.ForEach(x =>
                    {
                        x.TagNum = _taglist.Count(n => n.FBILL_SOURCE_ITEM_ID == x.FBILL_SOURCE_ITEM_ID);
                        x.FSCAN_NUM = _taglist.Where(n => n.FBILL_SOURCE_ITEM_ID == x.FBILL_SOURCE_ITEM_ID).Sum(n => n.FSTK_QTY);
                    });
                    _taglist.ForEach(x =>
                    {
                        x.FSCAN_NUM = _taglist.Where(n => n.FBILL_SOURCE_ITEM_ID == x.FBILL_SOURCE_ITEM_ID).Sum(n => n.FSTK_QTY);
                        x.FNUM = _TagBillList.FirstOrDefault(n => n.FBILL_SOURCE_ITEM_ID == x.FBILL_SOURCE_ITEM_ID).FNUM;
                    });
                }


                result = new DataResult<object>()
                {
                    Entity = new { TagBillList = _TagBillList, TagScanLog = _taglist },
                    StatusCode = 200,
                };
                return await OK(result);
            }
            catch (Exception ex)
            {

                DataResult<object> result = new DataResult<object>()
                {
                    Entity = null,
                    StatusCode = 400,
                    Message = ex.Message
                };
                return await OK(result);
            }
        }








        /// <summary>
        ///根据工单生成入库条码
        /// </summary>
        /// <param name="orderTags"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> GenrateOrderInsertAsync(List<OrderTagRkModel> models)
        {

            try
            {
                var model = models.FirstOrDefault();
                var db = _isugar.DB;
                var _mainDB = _isugar.GetDB("MAIN");
                var _purpDB = _isugar.GetDB("PUR");
                var _mesDB = _isugar.GetDB("MES");
                var _stkdb = _isugar.GetDB("STOCK");
                var _copdb = _isugar.GetDB("COP");
                var result = new DataResult<object>();
                if (models.Any(n => n.FSTK_QTY == 0))
                {
                    ERROR(null, 100100, _multiLang["入库失败,对应的标签入库数不能为0！"]);
                }

                //判断入库数是否大于总数
                var _taglist = await db.Queryable<T_MESD_TAG_GENERATION_LOG>()
                  .Where(n => n.FBILL_TYPE == model.FBILL_TYPE.ToUpper() && n.FBILL_SOURCE_NO == model.FBILL_SOURCE_NO).OrderBy(n => n.FBILL_SOURCE_ITEM_ID)
                  .Select<OrderTagRkModel>().ToListAsync();
                var modelitem = models.Select(n => n.FBILL_SOURCE_ITEM_ID).Distinct().ToList();
                foreach (var item in modelitem)
                {
                    var itemmodel = models.FirstOrDefault(n => n.FBILL_SOURCE_ITEM_ID == item);
                    //统计原有的数据
                    var stk_qty = models.Where(n => n.FBILL_SOURCE_ITEM_ID == itemmodel.FBILL_SOURCE_ITEM_ID).Sum(n => n.FSTK_QTY);
                    //取出原有的数据
                    var itemstkqty = _taglist.Where(n => n.FBILL_SOURCE_ITEM_ID == itemmodel.FBILL_SOURCE_ITEM_ID).Sum(n => n.FSTK_QTY);
                    var stk_qty2 = models.Where(n => n.FBILL_SOURCE_ITEM_ID == itemmodel.FBILL_SOURCE_ITEM_ID && n.FTAG_GEN_LOG_ID == null).Sum(n => n.FSTK_QTY);
                    if (stk_qty > itemmodel.FNUM || itemstkqty + stk_qty2 > itemmodel.FNUM)
                    {
                        ERROR(null, 100100, _multiLang["入库失败,对应的标签入库数不能大于物料入库总数！"]);
                    }

                }
                var AddList = models.Where(n => n.FTAG_GEN_LOG_ID == null).ToList();
                var _genlist = new List<TagGenModel>();
                foreach (var item in AddList)
                {
                    _genlist.Add(new TagGenModel { GEN_NO = item.FMATERIAL_CODE, FBILL_SOURCE_ITEM_ID = item.FBILL_SOURCE_ITEM_ID, FBILL_SOURCE_NO = item.FBILL_SOURCE_NO, FSTK_QTY = item.FSTK_QTY, FBILL_TYPE = "OutOrderInScan".ToUpper() });
                }
                await GenerateOrderCodeList(_genlist);
                result = new DataResult<object>()
                {

                    StatusCode = 200,
                };
                return await OK(result);
            }
            catch (Exception ex)
            {

                DataResult<object> result = new DataResult<object>()
                {
                    Entity = null,
                    StatusCode = 400,
                    Message = ex.Message
                };
                return await OK(result);
            }




        }


        /// <summary>
        /// 盘点入库扫描
        /// </summary>
        /// <returns></returns>
        private async Task<DataResult<object>> FMScanApp(TagScanModel model)
        {

            var db = _isugar.DB;
            var _mainDB = _isugar.GetDB("MAIN");
            var result = new DataResult<object>();
            var _chkTag = await db.Queryable<T_MESD_TAG, T_MESD_TAG_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FTAG_ID == b.FTAG_ID))
            .Where((a, b) => a.FTAG_BELONG == "FMATERIAL" && a.FTAG_CODE_GENERATE.ToUpper() == model.FTAG_CODE_GENERATE.ToUpper())
           .AnyAsync();

            if (!_chkTag)
            {

                ERROR(null, 100700, _multiLang["扫描失败,未配置物料标签管理！"]);
            }

            if (model.FTAG_CODE_GENERATE.ToUpper() == "SERIAL")
            {
                //判断是否存在物料流水码
                var _FindFMATERIALView = await db.Queryable<T_MESD_TAG, T_MESD_TAG_STATUS, T_MESD_TAG_GENERATION_LOG, T_MESD_TAG_SCAN_LOG>((a, b, c, d) => new JoinQueryInfos(JoinType.Left, a.FTAG_ID == b.FTAG_ID, JoinType.Left, c.FTAG_ID == a.FTAG_ID, JoinType.Left, d.FTAG_GEN_LOG_ID == c.FTAG_GEN_LOG_ID))
            .Where((a, b, c, d) => c.FBARCODE_NO == model.FBARCODE_NO && a.FTAG_CODE_GENERATE.ToUpper() == model.FTAG_CODE_GENERATE.ToUpper())
            .Select<TagCodeRkModel>().ToListAsync();
                if (_FindFMATERIALView == null || _FindFMATERIALView.Count == 0)
                {
                    ERROR(null, 100700, _multiLang["扫描失败不存在对应的物料流水编码！"]);
                }
                else if (_FindFMATERIALView.Count > 1)
                {

                    ERROR(null, 100800, _multiLang["扫描失败检索到相同的两个物料流水编码！"]);
                }
                else if (_FindFMATERIALView[0].FSCAN_TYPE == 2)
                {
                    ERROR(null, 100900, _multiLang["扫描失败,该物料已经入库！"]);
                }
                else
                {
                    //添加到扫描表
                    var ScanAdd = new T_MESD_TAG_SCAN_LOG
                    {
                        FTAG_SCAN_LOG_ID = GuidHelper.NewGuid(), // 通常使用 GUID 作为主键
                        FTAG_ID = _FindFMATERIALView[0].FTAG_ID,
                        FTAG_CODE = _FindFMATERIALView[0].FTAG_CODE,
                        FTAG_TYPE = _FindFMATERIALView[0].FTAG_BELONG,
                        FBARCODE_NO = _FindFMATERIALView[0].FBARCODE_NO,
                        FBARCODE_TYPE = _FindFMATERIALView[0].FBARCODE_TYPE,
                        FUSE_STATUS = "SCANNED",
                        FSCAN_TIME = DateTime.Now,
                        FSCAN_SOURCE = "APP",
                        FSCAN_RESULT = "处理成功",
                        FREMARK = "扫码盘点记录",
                        FSCAN_TYPE = 2,
                        FSCAN_SOURCE_NO = _FindFMATERIALView[0].FGEN_SOURCE_NO,
                        FTAG_GEN_LOG_ID = _FindFMATERIALView[0].FTAG_GEN_LOG_ID,
                        FSTORE_ID = model.FSTORE_ID,
                        FSTORE_PLACE_ID = model.FSTORE_PLACE_ID,
                        FSTORE_TYPE = 1,
                        FSTORE_NUM = model.FSTORE_NUM,

                    };
                    await db.Insertable<T_MESD_TAG_SCAN_LOG>(ScanAdd).ExecuteCommandAsync();
                    var _matinfo = await _mainDB.Queryable<T_MSDM_MATERIAL, T_MSDM_MATERIAL_UNIT, T_MSDM_UNIT>((a, b, c) => new JoinQueryInfos(JoinType.Left, b.FMATERIAL_ID == a.FMATERIAL_ID, JoinType.Left, c.FUNIT_ID == a.FUNIT_ID))
                           .Where((a, b, c) => a.FMATERIAL_CODE == _FindFMATERIALView[0].FGEN_SOURCE_NO && b.FUNIT_TYPE == "STK")
                           .Select((a, b, c) => new { a.FMATERIAL_ID, b.FCONVERT_UNIT_ID, b.FUNIT_QTY, b.FCONVERT_UNIT_QTY, c.FUNIT_ID })

                           .FirstAsync();
                    //更新库存
                    var _stkdb = _isugar.GetDB("STOCK");
                    await _stkdb.Insertable<T_STKD_STOCK>(new T_STKD_STOCK
                    {
                        FSTOCK_ID = GuidHelper.NewGuid(),         // 示例：新建唯一ID
                        FSTORE_ID = model.FSTORE_ID,
                        FSTORE_PLACE_ID = model.FSTORE_PLACE_ID,
                        FTYPE = "otherin",
                        FMATERIAL_ID = _matinfo.FMATERIAL_ID,
                        FSTK_UNIT_ID = _matinfo.FCONVERT_UNIT_ID,
                        FSTK_UNIT_QTY = (decimal)model.FSTORE_NUM * _matinfo.FUNIT_QTY * (_matinfo.FUNIT_QTY > 0 ? _matinfo.FCONVERT_UNIT_QTY / _matinfo.FUNIT_QTY : 0),
                        FUNIT_STK_CONVERT_SCALE = _matinfo.FUNIT_QTY > 0 ? (decimal)model.FSTORE_NUM * _matinfo.FCONVERT_UNIT_QTY / _matinfo.FUNIT_QTY : 0,
                        FPRICE = 0m,
                        FAMT = 0m,
                        FUNIT_ID = _matinfo.FUNIT_ID,
                        FUNIT_QTY = 1m,
                        FREMARK = "盘点入库"
                    }).ExecuteCommandAsync();

                }
                result = new DataResult<object>()
                {
                    Message = "物料入库完成!",
                    StatusCode = 200,
                };
            }
            else
            {
                //判断是否存在物料流水码
                var _FindFMATERIALView = await db.Queryable<T_MESD_TAG, T_MESD_TAG_STATUS, T_MESD_TAG_GENERATION_LOG>((a, b, c) => new JoinQueryInfos(JoinType.Left, a.FTAG_ID == b.FTAG_ID, JoinType.Left, c.FTAG_ID == a.FTAG_ID))
            .Where((a, b, c) => c.FBARCODE_NO == model.FBARCODE_NO && a.FTAG_CODE_GENERATE.ToUpper() == model.FTAG_CODE_GENERATE.ToUpper())
            .Select<TagCodeRkModel>().FirstAsync();
                var MaterimalLog = new T_MESD_TAG_GENERATION_LOG();
                //判断是否存在物料流水码
                if (_FindFMATERIALView == null)
                {


                    var res = await GenerateCodeList(new TagGenModel
                    {
                        GEN_NO = model.FBARCODE_NO,
                        FTAG_BELONG = "FMATERIAL",
                        FTAG_CODE_GENERATE = "ONE",
                        GEN_TIME = DateTime.Now,
                    });
                    MaterimalLog = res.Entity[0];

                }
                else
                {
                    MaterimalLog = _FindFMATERIALView.MapToDestObj<TagCodeRkModel, T_MESD_TAG_GENERATION_LOG>();
                }
                //添加到扫描表
                var ScanAdd = new T_MESD_TAG_SCAN_LOG
                {
                    FTAG_SCAN_LOG_ID = GuidHelper.NewGuid(), // 通常使用 GUID 作为主键
                    FTAG_ID = MaterimalLog.FTAG_ID,
                    FTAG_CODE = MaterimalLog.FTAG_CODE,
                    FTAG_TYPE = "FMATERIAL",
                    FBARCODE_NO = MaterimalLog.FBARCODE_NO,
                    FBARCODE_TYPE = MaterimalLog.FBARCODE_TYPE,
                    FUSE_STATUS = "SCANNED",
                    FSCAN_TIME = DateTime.Now,
                    FSCAN_SOURCE = "APP",
                    FSCAN_RESULT = "处理成功",
                    FREMARK = "扫码盘点记录",
                    FSCAN_TYPE = 2,
                    FSCAN_SOURCE_NO = MaterimalLog.FGEN_SOURCE_NO,
                    FTAG_GEN_LOG_ID = MaterimalLog.FTAG_GEN_LOG_ID,
                    FSTORE_ID = model.FSTORE_ID,
                    FSTORE_PLACE_ID = model.FSTORE_PLACE_ID,
                    FSTORE_TYPE = 1,
                    FSTORE_NUM = model.FSTORE_NUM,

                };
                await db.Insertable<T_MESD_TAG_SCAN_LOG>(ScanAdd).ExecuteCommandAsync();
                var _matinfo = await _mainDB.Queryable<T_MSDM_MATERIAL, T_MSDM_MATERIAL_UNIT, T_MSDM_UNIT>((a, b, c) => new JoinQueryInfos(JoinType.Left, b.FMATERIAL_ID == a.FMATERIAL_ID, JoinType.Left, c.FUNIT_ID == a.FUNIT_ID))
                       .Where((a, b, c) => a.FMATERIAL_CODE == MaterimalLog.FGEN_SOURCE_NO && b.FUNIT_TYPE == "STK")
                       .Select((a, b, c) => new { a.FMATERIAL_ID, b.FCONVERT_UNIT_ID, b.FUNIT_QTY, b.FCONVERT_UNIT_QTY, c.FUNIT_ID })

                       .FirstAsync();
                //更新库存
                var _stkdb = _isugar.GetDB("STOCK");
                await _stkdb.Insertable<T_STKD_STOCK>(new T_STKD_STOCK
                {
                    FSTOCK_ID = GuidHelper.NewGuid(),         // 示例：新建唯一ID
                    FSTORE_ID = model.FSTORE_ID,
                    FSTORE_PLACE_ID = model.FSTORE_PLACE_ID,
                    FTYPE = "otherin",
                    FMATERIAL_ID = _matinfo.FMATERIAL_ID,
                    FSTK_UNIT_ID = _matinfo.FCONVERT_UNIT_ID,
                    FSTK_UNIT_QTY = _matinfo.FUNIT_QTY * (_matinfo.FUNIT_QTY > 0 ? _matinfo.FCONVERT_UNIT_QTY / _matinfo.FUNIT_QTY : 0),
                    FUNIT_STK_CONVERT_SCALE = _matinfo.FUNIT_QTY > 0 ? _matinfo.FCONVERT_UNIT_QTY / _matinfo.FUNIT_QTY : 0,
                    FPRICE = 0m,
                    FAMT = 0m,
                    FUNIT_ID = _matinfo.FUNIT_ID,
                    FUNIT_QTY = 1m,
                    FREMARK = "盘点入库"
                }).ExecuteCommandAsync();

            }
            result = new DataResult<object>()
            {
                Message = "物料入库完成!",
                StatusCode = 200,
            };
            return result;
        }



        /// <summary>
        /// 出库扫描
        /// </summary>
        /// <returns></returns>
        private async Task<DataResult<object>> OrderOutScanApp(TagScanModel model)
        {

            var db = _isugar.DB;
            var _mainDB = _isugar.GetDB("MAIN");
            var result = new DataResult<object>();






            var _chkTag = await db.Queryable<T_MESD_TAG, T_MESD_TAG_STATUS>((a, b) => new JoinQueryInfos(JoinType.Left, a.FTAG_ID == b.FTAG_ID))
            .Where((a, b) => a.FTAG_BELONG == "FMATERIAL" && a.FTAG_CODE_GENERATE.ToUpper() == model.FTAG_CODE_GENERATE.ToUpper())
           .AnyAsync();

            if (!_chkTag)
            {

                ERROR(null, 100700, _multiLang["扫描失败,未配置物料标签管理！"]);
            }

            if (model.FTAG_CODE_GENERATE.ToUpper() == "SERIAL")
            {
                //判断是否存在物料流水码
                var _FindFMATERIALView = await db.Queryable<T_MESD_TAG, T_MESD_TAG_STATUS, T_MESD_TAG_GENERATION_LOG, T_MESD_TAG_SCAN_LOG>((a, b, c, d) => new JoinQueryInfos(JoinType.Left, a.FTAG_ID == b.FTAG_ID, JoinType.Left, c.FTAG_ID == a.FTAG_ID, JoinType.Left, d.FTAG_GEN_LOG_ID == c.FTAG_GEN_LOG_ID))
            .Where((a, b, c, d) => c.FBARCODE_NO == model.FBARCODE_NO && a.FTAG_CODE_GENERATE.ToUpper() == model.FTAG_CODE_GENERATE.ToUpper())
            .Select<TagCodeRkModel>().ToListAsync();
                if (_FindFMATERIALView == null || _FindFMATERIALView.Count == 0)
                {
                    ERROR(null, 100700, _multiLang["扫描失败不存在对应的物料流水编码！"]);
                }
                else if (_FindFMATERIALView.Count > 1)
                {

                    ERROR(null, 100800, _multiLang["扫描失败检索到相同的两个物料流水编码！"]);
                }
                else if (_FindFMATERIALView[0].FSCAN_TYPE == 2)
                {
                    ERROR(null, 100900, _multiLang["扫描失败,该物料已经入库！"]);
                }
                else
                {
                    //添加到扫描表
                    var ScanAdd = new T_MESD_TAG_SCAN_LOG
                    {
                        FTAG_SCAN_LOG_ID = GuidHelper.NewGuid(), // 通常使用 GUID 作为主键
                        FTAG_ID = _FindFMATERIALView[0].FTAG_ID,
                        FTAG_CODE = _FindFMATERIALView[0].FTAG_CODE,
                        FTAG_TYPE = _FindFMATERIALView[0].FTAG_BELONG,
                        FBARCODE_NO = _FindFMATERIALView[0].FBARCODE_NO,
                        FBARCODE_TYPE = _FindFMATERIALView[0].FBARCODE_TYPE,
                        FUSE_STATUS = "SCANNED",
                        FSCAN_TIME = DateTime.Now,
                        FSCAN_SOURCE = "APP",
                        FSCAN_RESULT = "处理成功",
                        FREMARK = "扫码盘点记录",
                        FSCAN_TYPE = 2,
                        FSCAN_SOURCE_NO = _FindFMATERIALView[0].FGEN_SOURCE_NO,
                        FTAG_GEN_LOG_ID = _FindFMATERIALView[0].FTAG_GEN_LOG_ID,
                        FSTORE_ID = model.FSTORE_ID,
                        FSTORE_PLACE_ID = model.FSTORE_PLACE_ID,
                        FSTORE_TYPE = 1,
                        FSTORE_NUM = model.FSTORE_NUM,

                    };
                    await db.Insertable<T_MESD_TAG_SCAN_LOG>(ScanAdd).ExecuteCommandAsync();
                    var _matinfo = await _mainDB.Queryable<T_MSDM_MATERIAL, T_MSDM_MATERIAL_UNIT, T_MSDM_UNIT>((a, b, c) => new JoinQueryInfos(JoinType.Left, b.FMATERIAL_ID == a.FMATERIAL_ID, JoinType.Left, c.FUNIT_ID == a.FUNIT_ID))
                           .Where((a, b, c) => a.FMATERIAL_CODE == _FindFMATERIALView[0].FGEN_SOURCE_NO && b.FUNIT_TYPE == "STK")
                           .Select((a, b, c) => new { a.FMATERIAL_ID, b.FCONVERT_UNIT_ID, b.FUNIT_QTY, b.FCONVERT_UNIT_QTY, c.FUNIT_ID })

                           .FirstAsync();
                    //更新库存
                    var _stkdb = _isugar.GetDB("STOCK");
                    await _stkdb.Insertable<T_STKD_STOCK>(new T_STKD_STOCK
                    {
                        FSTOCK_ID = GuidHelper.NewGuid(),         // 示例：新建唯一ID
                        FSTORE_ID = model.FSTORE_ID,
                        FSTORE_PLACE_ID = model.FSTORE_PLACE_ID,
                        FTYPE = "otherin",
                        FMATERIAL_ID = _matinfo.FMATERIAL_ID,
                        FSTK_UNIT_ID = _matinfo.FCONVERT_UNIT_ID,
                        FSTK_UNIT_QTY = (decimal)model.FSTORE_NUM * _matinfo.FUNIT_QTY * (_matinfo.FUNIT_QTY > 0 ? _matinfo.FCONVERT_UNIT_QTY / _matinfo.FUNIT_QTY : 0),
                        FUNIT_STK_CONVERT_SCALE = _matinfo.FUNIT_QTY > 0 ? (decimal)model.FSTORE_NUM * _matinfo.FCONVERT_UNIT_QTY / _matinfo.FUNIT_QTY : 0,
                        FPRICE = 0m,
                        FAMT = 0m,
                        FUNIT_ID = _matinfo.FUNIT_ID,
                        FUNIT_QTY = 1m,
                        FREMARK = "盘点入库"
                    }).ExecuteCommandAsync();

                }
                result = new DataResult<object>()
                {
                    Message = "物料入库完成!",
                    StatusCode = 200,
                };
            }
            else
            {
                //判断是否存在物料流水码
                var _FindFMATERIALView = await db.Queryable<T_MESD_TAG, T_MESD_TAG_STATUS, T_MESD_TAG_GENERATION_LOG>((a, b, c) => new JoinQueryInfos(JoinType.Left, a.FTAG_ID == b.FTAG_ID, JoinType.Left, c.FTAG_ID == a.FTAG_ID))
            .Where((a, b, c) => c.FBARCODE_NO == model.FBARCODE_NO && a.FTAG_CODE_GENERATE.ToUpper() == model.FTAG_CODE_GENERATE.ToUpper())
            .Select<TagCodeRkModel>().FirstAsync();
                var MaterimalLog = new T_MESD_TAG_GENERATION_LOG();
                //判断是否存在物料流水码
                if (_FindFMATERIALView == null)
                {


                    var res = await GenerateCodeList(new TagGenModel
                    {
                        GEN_NO = model.FBARCODE_NO,
                        FTAG_BELONG = "FMATERIAL",
                        FTAG_CODE_GENERATE = "ONE",
                        GEN_TIME = DateTime.Now,
                    });
                    MaterimalLog = res.Entity[0];

                }
                else
                {
                    MaterimalLog = _FindFMATERIALView.MapToDestObj<TagCodeRkModel, T_MESD_TAG_GENERATION_LOG>();
                }
                //添加到扫描表
                var ScanAdd = new T_MESD_TAG_SCAN_LOG
                {
                    FTAG_SCAN_LOG_ID = GuidHelper.NewGuid(), // 通常使用 GUID 作为主键
                    FTAG_ID = MaterimalLog.FTAG_ID,
                    FTAG_CODE = MaterimalLog.FTAG_CODE,
                    FTAG_TYPE = "FMATERIAL",
                    FBARCODE_NO = MaterimalLog.FBARCODE_NO,
                    FBARCODE_TYPE = MaterimalLog.FBARCODE_TYPE,
                    FUSE_STATUS = "SCANNED",
                    FSCAN_TIME = DateTime.Now,
                    FSCAN_SOURCE = "APP",
                    FSCAN_RESULT = "处理成功",
                    FREMARK = "扫码盘点记录",
                    FSCAN_TYPE = 2,
                    FSCAN_SOURCE_NO = MaterimalLog.FGEN_SOURCE_NO,
                    FTAG_GEN_LOG_ID = MaterimalLog.FTAG_GEN_LOG_ID,
                    FSTORE_ID = model.FSTORE_ID,
                    FSTORE_PLACE_ID = model.FSTORE_PLACE_ID,
                    FSTORE_TYPE = 1,
                    FSTORE_NUM = model.FSTORE_NUM,

                };
                await db.Insertable<T_MESD_TAG_SCAN_LOG>(ScanAdd).ExecuteCommandAsync();
                var _matinfo = await _mainDB.Queryable<T_MSDM_MATERIAL, T_MSDM_MATERIAL_UNIT, T_MSDM_UNIT>((a, b, c) => new JoinQueryInfos(JoinType.Left, b.FMATERIAL_ID == a.FMATERIAL_ID, JoinType.Left, c.FUNIT_ID == a.FUNIT_ID))
                       .Where((a, b, c) => a.FMATERIAL_CODE == MaterimalLog.FGEN_SOURCE_NO && b.FUNIT_TYPE == "STK")
                       .Select((a, b, c) => new { a.FMATERIAL_ID, b.FCONVERT_UNIT_ID, b.FUNIT_QTY, b.FCONVERT_UNIT_QTY, c.FUNIT_ID })

                       .FirstAsync();
                //更新库存
                var _stkdb = _isugar.GetDB("STOCK");
                await _stkdb.Insertable<T_STKD_STOCK>(new T_STKD_STOCK
                {
                    FSTOCK_ID = GuidHelper.NewGuid(),         // 示例：新建唯一ID
                    FSTORE_ID = model.FSTORE_ID,
                    FSTORE_PLACE_ID = model.FSTORE_PLACE_ID,
                    FTYPE = "otherin",
                    FMATERIAL_ID = _matinfo.FMATERIAL_ID,
                    FSTK_UNIT_ID = _matinfo.FCONVERT_UNIT_ID,
                    FSTK_UNIT_QTY = _matinfo.FUNIT_QTY * (_matinfo.FUNIT_QTY > 0 ? _matinfo.FCONVERT_UNIT_QTY / _matinfo.FUNIT_QTY : 0),
                    FUNIT_STK_CONVERT_SCALE = _matinfo.FUNIT_QTY > 0 ? _matinfo.FCONVERT_UNIT_QTY / _matinfo.FUNIT_QTY : 0,
                    FPRICE = 0m,
                    FAMT = 0m,
                    FUNIT_ID = _matinfo.FUNIT_ID,
                    FUNIT_QTY = 1m,
                    FREMARK = "盘点入库"
                }).ExecuteCommandAsync();

            }
            result = new DataResult<object>()
            {
                Message = "物料入库完成!",
                StatusCode = 200,
            };
            return result;
        }


        /// <summary>
        /// 工单实际扫描出入库
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //public async Task<DataResult<object>> OrderInStk(List<OrderTagRkModel> model)
        //{
        //    try
        //    {
        //        var db = _isugar.DB;
        //        var _mainDB = _isugar.GetDB("MAIN");
        //        var _purpDB = _isugar.GetDB("PUR");
        //        var _mesDB = _isugar.GetDB("MES");
        //        var _stkdb = _isugar.GetDB("STOCK");
        //        var _copdb = _isugar.GetDB("COP");


        //        //判断工单类型





        //    }
        //    catch (Exception)
        //    {

        //        throw;
        //    }
        //}




    }
}
