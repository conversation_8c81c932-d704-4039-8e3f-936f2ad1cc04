﻿using HCloud.Core.ProxyGenerator.Models;
using System;
using System.Collections.Generic;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models;

namespace HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models
{
    /// <summary>
    /// 待排程工单工艺
    /// </summary>
    [MessagePack.MessagePackObject()]
    public class WOCraftScheduleModel : BillStatusModel
    {
        /// <summary>
        /// Id
        /// </summary>
        public string FWORK_ORDER_ID
        {
            get; set;
        }
        /// <summary>
        /// 工单编号
        /// </summary>
        public string FWORK_ORDER_NO
        {
            get; set;
        }
        /// <summary>
        /// 订单Id
        /// </summary>
        public string FSALE_ORDER_ID
        {
            get; set;
        }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string FSALE_ORDER_NO
        {
            get; set;
        }

        /// <summary>
        /// 订单产品行Id
        /// </summary>
        public string FSALE_ORDER_PRODUCT_ID
        {
            get; set;
        }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string FMATERIAL_ID
        {
            get; set;
        }

        /// <summary>
        /// 产品编号
        /// </summary>
        public string FMATERIAL_CODE { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string FMATERIAL_NAME { get; set; }

        /// <summary>
        /// 简图附件id
        /// </summary>
        public string FPIC_ATTACH_ID { get; set; }

        /// <summary>
        /// 规格描述
        /// </summary>
        public string FSPEC_DESC { get; set; }

        /// <summary>
        /// 工单层级
        /// </summary>
        public int FLEVEL
        {
            get; set;
        }

        /// <summary>
        /// 上级工单ID
        /// </summary>
        public string FPARENT_WORK_ORDER_ID
        {
            get; set;
        }
        /// <summary>
        /// 生产数量
        /// </summary>
        public decimal FPRO_QTY
        {
            get; set;
        }
        /// <summary>
        /// 计量单位ID
        /// </summary>
        public string FPRO_UNIT_ID
        {
            get; set;
        }


        /// <summary>
        /// 计量单位代号
        /// </summary>
        public string FPRO_UNIT_CODE
        {
            get; set;
        }

        /// <summary>
        /// 计量单位名称
        /// </summary>
        public string FPRO_UNIT_NAME
        {
            get; set;
        }

        /// <summary>
        /// 批号
        /// </summary>
        public string FLOT_NO
        {
            get; set;
        }
        /// <summary>
        /// 批号颜色
        /// </summary>
        public string FLOT_NO_COLOR
        {
            get; set;
        }
        /// <summary>
        /// 计划员员工Id
        /// </summary>
        public string FPLAN_EMP_ID
        {
            get; set;
        }
        /// <summary>
        /// 计划员员工工号
        /// </summary>
        public string FPLAN_EMP_NAME
        {
            get; set;
        }
        /// <summary>
        /// 工单类型 数据字典:WorkOrderType
        /// </summary>
        public string FWORK_ORDER_TYPE
        {
            get; set;
        }

        /// <summary>
        /// 企业ID
        /// </summary>
        public string FECODE
        {
            get; set;
        }

        /// <summary>
        /// Id
        /// </summary>
        public string FWORK_ORDER_CRAFT_ID
        {
            get; set;
        }

        /// <summary>
        /// 顺序
        /// </summary>
        public int FSHOW_SEQNO
        {
            get; set;
        } 
        /// <summary>
        /// 工艺Id
        /// </summary>
        public string FCRAFT_ID
        {
            get; set;
        }

        /// <summary>
        /// 工艺编号
        /// </summary>
        public string FCRAFT_CODE
        {
            get; set;
        }

        /// <summary>
        /// 工艺名称
        /// </summary>
        public string FCRAFT_NAME
        {
            get; set;
        }


        /// <summary>
        /// 加工说明
        /// </summary>
        public string FCRAFT_DESC
        {
            get; set;
        }

        /// <summary>
        /// 投入物料
        /// </summary>
        public string FSUB_MATERIAL { get; set; }

        /// <summary>
        /// 产出率(%)
        /// </summary>
        public decimal FGEN_RATE
        {
            get; set;
        }
        /// <summary>
        /// 加工费
        /// </summary>
        public decimal FPROCESS_FEE
        {
            get; set;
        }

        /// <summary>
        /// 工艺开工时间
        /// </summary>
        public DateTime? FPLAN_ST_DATE { get; set; }

        /// <summary>
        /// 工艺完工时间
        /// </summary>
        public DateTime? FPLAN_ED_DATE { get; set; }

        /// <summary>
        /// 计划工时(小时)
        /// </summary>
        public decimal? FPLAN_USE_HOUR { get; set; }

        /// <summary>
        /// 工位Id
        /// </summary>
        public string FSTATION_ID { get; set; }

        /// <summary>
        /// 工位代号
        /// </summary>
        public string FSTATION_CODE { get; set; }

        /// <summary>
        /// 工位名称
        /// </summary>
        public string FSTATION_NAME { get; set; }

        /// <summary>
        /// 待排数量
        /// </summary>
        public decimal FWAIT_QTY { get; set; }
        /// <summary>
        /// 待排重量
        /// </summary>
        public decimal FWAIT_WEIGHT
        {
            get
            {

                return Math.Round(FUNIT_WEIGHT * FWAIT_QTY, 6);
            }
        }

        /// <summary>
        /// 计划产出数量
        /// </summary>
        public decimal FPLAN_QTY { get; set; }

        /// <summary>
        /// 原下发任务
        /// </summary>
        public decimal FORI_PLAN_QTY { get; set; }

        /// <summary>
        /// 任务Id
        /// </summary>
        public string FCRAFT_SCHEDULE_ID { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        public string FCRAFT_SCHEDULE_NO { get; set; }

        /// <summary>
        /// 二维码
        /// </summary>
        public string FQRCODE { get; set; }

        /// <summary>
        /// 人员列表
        /// </summary>
        public List<WOCraftSchedulePersonModel> Persons { get; set; }

        /// <summary>
        /// 排程批次Id
        /// </summary>
        public string FSCHEDULE_BATCH_ID { get; set; }

        /// <summary>
        /// 任务状态表Id
        /// </summary>
        public string FCRAFT_SCHEDULE_STATUS_ID { get; set; }

        /// <summary>
        /// 下发状态
        /// </summary>
        public bool FRELEASE_STATUS { get; set; }


        /// <summary>
        /// 下发人ID
        /// </summary>
        public string FRELEASER_ID
        {
            get; set;
        }
        /// <summary>
        /// 下发人
        /// </summary>
        public string FRELEASER
        {
            get; set;
        }
        /// <summary>
        /// 下发日期
        /// </summary>
        public DateTime? FRELEASE_DATE
        {
            get; set;
        }

        /// <summary>
        /// 工艺状态表的已下发数
        /// </summary>
        public decimal FRELEASE_QTY
        {
            get; set;
        }

        /// <summary>
        /// 工艺状态表的已完工数
        /// </summary>
        public decimal FFINISH_QTY
        {
            get; set;
        }

        /// <summary>
        /// 工艺状态表的已接收数
        /// </summary>
        public decimal FRECV_QTY
        {
            get; set;
        }

        /// <summary>
        /// 执行人(姓名合并内容)
        /// </summary>
        public string FEMP { get; set; }

        public string FCREATOR_ID { get; set; }
        public string FCREATOR { get; set; }
        public DateTime? FCDATE { get; set; }
        public string FMODIFIER_ID { get; set; }
        public string FMODIFIER { get; set; }
        public DateTime? FMODIDATE { get; set; }

        /// <summary>
        /// 实际开工时间
        /// </summary>
        public DateTime? FACT_ST_DATE
        {
            get; set;
        }
        /// <summary>
        /// 实际完工时间
        /// </summary>
        public DateTime? FACT_ED_DATE
        {
            get; set;
        }

        /// <summary>
        /// 实际工时(小时)
        /// </summary>
        public decimal FACT_USE_HOUR { get; set; }


        /// <summary>
        /// 排程任务合格数量
        /// </summary>
        public decimal FPASS_QTY_SCHEDULE
        {
            get; set;
        }
        /// <summary>
        /// 排程任务不良数量
        /// </summary>
        public decimal FNG_QTY_SCHEDULE
        {
            get; set;
        }

        /// <summary>
        /// 排程任务已完工数
        /// </summary>
        public decimal FFINISH_QTY_SCHEDULE
        {
            get; set;
        }

        /// <summary>
        /// 单位重量 从生产工单带入冗余
        /// </summary>
        public decimal FUNIT_WEIGHT
        {
            get; set;
        }
        /// <summary>
        /// 生产总重量
        /// </summary>
        public decimal FPRO_WEIGHT
        {
            get; set;
        }
        /// <summary>
        /// 计划产出重量 
        /// </summary>
        public decimal FPLAN_WEIGHT
        {
            get; set;
        }
        /// <summary>
        /// 重量单位ID 从生产工单带入冗余
        /// </summary>
        public string FWEIGHT_UNIT_ID
        {
            get; set;
        }
        /// <summary>
        /// 重量单位名称
        /// </summary>
        public string FWEIGHT_UNIT_NAME
        {
            get; set;
        }
        /// <summary>
        /// 重量单位编号
        /// </summary>
        public string FWEIGHT_UNIT_CODE
        {
            get; set;
        }


        /// <summary>
        /// 完工重量
        /// </summary>
        public decimal FFINISH_WEIGHT
        {
            get; set;
        }
        /// <summary>
        /// 合格重量
        /// </summary>
        public decimal FPASS_WEIGHT
        {
            get; set;
        }
        /// <summary>
        /// 不合格重量
        /// </summary>
        public decimal FNG_WEIGHT
        {
            get; set;
        }
        /// <summary>
        /// 加工状态
        /// </summary>
        public string FCRAFT_STATUS { get; set; }


        /// <summary>
        /// 备注
        /// </summary>
        public string FREMARK { get; set; }
        /// <summary>
        /// 产品型号
        /// </summary>
        public string FGOODS_MODEL
        {
            get;
            set;
        }
        /// <summary>
        /// 工位属性
        /// </summary>
        public string FSTATION_PROPERTY
        {
            get; set;
        }

        /// <summary>
        /// 已转委外申请单数量
        /// </summary>
        public decimal OutApplyCount
        {
            get; set;
        }
        /// <summary>
        /// 是否转委外 转委外申请单后自动改为1，委外工单转完后自动下发
        /// </summary>
        public int FIS_OUT
        {
            get; set;
        }
        /// <summary>
        /// 报工方式 内定数据字典: wocraft-按工单工艺报工  sobook-按订单报工。默认为按工单工艺报工， 若物料为自制件，此字段不能为空.
        /// </summary>
        public string FBOOKING_TYPE
        {
            get; set;
        }
     
        /// <summary>
        /// 排程排序
        /// </summary>
        public int SCHEDULE_FSHOW_SEQNO { get; set; } = 999;



        /// <summary>
        /// 客户名称
        /// </summary>
        public string FIUIU_CUST { get; set; }
        /// <summary>
        /// 订单编号
        /// </summary>
        public string FIUIU_ORDER_NO { get; set; }
        /// <summary>
        /// 业务员姓名
        /// </summary>
        public string FIUIU_SALE_NAME { get; set; }
        /// <summary>
        /// 订单交期
        /// </summary>
        public DateTime? FIUIU_ORDER_DATE { get; set; }

        ///<summary>
        ///作业人数
        ///<summary>
        public int? FWORK_NUM
        {
            get;
            set;
        }

        /// <summary>
        /// Id
        /// </summary>
        public string FPROJECT_ID
        {
            get; set;
        }

        /// <summary>
        /// 项目编号
        /// </summary>
        public string FPROJECT_CODE
        {
            get; set;
        }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string FPROJECT_NAME
        {
            get; set;
        }
        /// <summary>
        /// 正在进行中的加工任务
        /// </summary>
        public List<CraftJobBookingModel> HandlingJobs { get; set; }

        /// <summary>
        /// 工艺路线Id
        /// </summary>
        public string FCRAFT_LINE_ID
        {
            get; set;
        }

        /// <summary>
        /// 工艺路线名称
        /// </summary>
        public string FCRAFT_LINE_NAME
        {
            get; set;
        }
        /// <summary>
        /// 父级物料ID
        /// </summary>
        public string FPARENT_MATERIAL_ID
        {
            get; set;
        }
        /// <summary>
        /// 父级物料编码
        /// </summary>
        public string FPARENT_MATERIAL_CODE
        {
            get; set;
        }
        /// <summary>
        /// 父级物料名称
        /// </summary>
        public string FPARENT_MATERIAL_NAME
        {
            get; set;
        }
        /// <summary>
        /// 是否打印标签
        /// </summary>
        public bool? FIF_TAG { get; set; } = false;
        /// <summary>
        /// 打印标签类型CraftTagType子表，内外包箱,产品(ITEM,PACK,PRODCUT)
        /// </summary>
        public string FTAG_TYPE { get; set; }
        /// <summary>
        /// 标签报表ID
        /// </summary>
        public string FTAG_REPORTID { get; set; }

        /// <summary>
        /// 标签记录
        /// </summary>
        public List<TagItem> tagItems { get; set; }

        /// <summary>
        /// 父排程ID 用于标识此排程任务是由哪个父排程拆分而来。根任务此字段为空。
        /// </summary>
        public string FPARENT_CRAFT_SCHEDULE_ID
        {
            get;
            set;
        }
        /// <summary>
        /// 产生排程的加工任务 用于标识此排程任务是由哪个加工任务拆分而来。根任务此字段为空。
        /// </summary>
        public string FSOURCE_JOB_BOOKING_ID
        {
            get;
            set;
        }
        /// <summary>
        /// 工艺类型 数据字典： CraftType  MADE-自制 OPM-委外
        /// </summary>
        public string FCRAFT_TYPE { get; set; }

        /// <summary>
        /// 文本字段1
        /// </summary>
        public string FSCHEDULE_TEXT1 { get; set; }

    }
}
